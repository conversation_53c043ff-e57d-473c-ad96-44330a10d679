<?php
// เปิด error reporting เพื่อ debug
error_reporting(E_ALL);
ini_set('display_errors', 0); // ปิดการแสดง error ใน output

// เริ่ม output buffering
ob_start();

try {
    require_once 'includes/auth.php';
    require_once 'includes/functions.php';

    // ตรวจสอบการเข้าสู่ระบบ
    if (!isLoggedIn()) {
        throw new Exception('กรุณาเข้าสู่ระบบ');
    }

    // ตรวจสอบสิทธิ์ Admin
    if (!isAdmin()) {
        throw new Exception('ไม่มีสิทธิ์ในการเพิ่ม Asset');
    }

    // ตรวจสอบว่าเป็น POST request
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    $assetManager = new AssetManager($pdo);

    // รับข้อมูลจากฟอร์ม
    $data = [
        'type' => trim($_POST['type'] ?? ''),
        'brand' => trim($_POST['brand'] ?? ''),
        'model' => trim($_POST['model'] ?? ''),
        'tag' => trim($_POST['tag'] ?? ''),
        'department' => trim($_POST['department'] ?? ''),
        'status' => trim($_POST['status'] ?? 'ใช้งาน'),
        'hostname' => trim($_POST['hostname'] ?? ''),
        'operating_system' => trim($_POST['operating_system'] ?? ''),
        'serial_number' => trim($_POST['serial_number'] ?? ''),
        'asset_id' => trim($_POST['asset_id'] ?? ''),
        'warranty_expire' => !empty($_POST['warranty_expire']) ? $_POST['warranty_expire'] : null,
        'asset_set' => trim($_POST['set_name'] ?? ''),
        'description' => trim($_POST['description'] ?? ''),
        'created_by' => getCurrentUserFullName(),
        'updated_by' => getCurrentUserFullName()
    ];

    // ตรวจสอบข้อมูลจำเป็น
    if (empty($data['type'])) {
        throw new Exception('กรุณาระบุ Type');
    }

    // หมายเหตุ: ไม่ตรวจสอบ Asset ID ซ้ำ เพราะอนุญาตให้ซ้ำได้
    // หมายเหตุ: ไม่ตรวจสอบ Tag ซ้ำ เพราะอนุญาตให้ซ้ำได้
    // หมายเหตุ: ไม่ตรวจสอบ Serial Number ซ้ำ เพราะอนุญาตให้ซ้ำได้

    // เพิ่ม Asset ผ่าน AssetManager
    $result = $assetManager->createAsset($data);

    if ($result) {
        // ล้าง output buffer
        ob_clean();
        
        // ส่ง JSON response
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'success' => true,
            'message' => 'เพิ่ม Asset สำเร็จ',
            'asset_id' => $result,
            'data' => [
                'id' => $result,
                'type' => $data['type'],
                'brand' => $data['brand'],
                'model' => $data['model'],
                'tag' => $data['tag'],
                'department' => $data['department'],
                'status' => $data['status']
            ]
        ], JSON_UNESCAPED_UNICODE);
    } else {
        throw new Exception('ไม่สามารถเพิ่ม Asset ได้');
    }

} catch (Exception $e) {
    // ล้าง output buffer
    ob_clean();
    
    // ส่ง JSON error response
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (Error $e) {
    // ล้าง output buffer
    ob_clean();
    
    // ส่ง JSON error response
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดของระบบ: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

// จบ output buffering
ob_end_flush();
?>
