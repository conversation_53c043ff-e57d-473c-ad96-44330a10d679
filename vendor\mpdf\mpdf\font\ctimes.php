<?php

$cw = array(
	chr(0) => 250, chr(1) => 250, chr(2) => 250, chr(3) => 250, chr(4) => 250, chr(5) => 250, chr(6) => 250, chr(7) => 250, chr(8) => 250, chr(9) => 250, chr(10) => 250, chr(11) => 250, chr(12) => 250, chr(13) => 250, chr(14) => 250, chr(15) => 250, chr(16) => 250, chr(17) => 250, chr(18) => 250, chr(19) => 250, chr(20) => 250, chr(21) => 250,
	chr(22) => 250, chr(23) => 250, chr(24) => 250, chr(25) => 250, chr(26) => 250, chr(27) => 250, chr(28) => 250, chr(29) => 250, chr(30) => 250, chr(31) => 250, ' ' => 250, '!' => 333, '"' => 408, '#' => 500, '$' => 500, '%' => 833, '&' => 778, '\'' => 180, '(' => 333, ')' => 333, '*' => 500, '+' => 564,
	',' => 250, '-' => 333, '.' => 250, '/' => 278, '0' => 500, '1' => 500, '2' => 500, '3' => 500, '4' => 500, '5' => 500, '6' => 500, '7' => 500, '8' => 500, '9' => 500, ':' => 278, ';' => 278, '<' => 564, '=' => 564, '>' => 564, '?' => 444, '@' => 921, 'A' => 722,
	'B' => 667, 'C' => 667, 'D' => 722, 'E' => 611, 'F' => 556, 'G' => 722, 'H' => 722, 'I' => 333, 'J' => 389, 'K' => 722, 'L' => 611, 'M' => 889, 'N' => 722, 'O' => 722, 'P' => 556, 'Q' => 722, 'R' => 667, 'S' => 556, 'T' => 611, 'U' => 722, 'V' => 722, 'W' => 944,
	'X' => 722, 'Y' => 722, 'Z' => 611, '[' => 333, '\\' => 278, ']' => 333, '^' => 469, '_' => 500, '`' => 333, 'a' => 444, 'b' => 500, 'c' => 444, 'd' => 500, 'e' => 444, 'f' => 333, 'g' => 500, 'h' => 500, 'i' => 278, 'j' => 278, 'k' => 500, 'l' => 278, 'm' => 778,
	'n' => 500, 'o' => 500, 'p' => 500, 'q' => 500, 'r' => 333, 's' => 389, 't' => 278, 'u' => 500, 'v' => 500, 'w' => 722, 'x' => 500, 'y' => 500, 'z' => 444, '{' => 480, '|' => 200, '}' => 480, '~' => 541, chr(127) => 350, chr(128) => 500, chr(129) => 350, chr(130) => 333, chr(131) => 500,
	chr(132) => 444, chr(133) => 1000, chr(134) => 500, chr(135) => 500, chr(136) => 333, chr(137) => 1000, chr(138) => 556, chr(139) => 333, chr(140) => 889, chr(141) => 350, chr(142) => 611, chr(143) => 350, chr(144) => 350, chr(145) => 333, chr(146) => 333, chr(147) => 444, chr(148) => 444, chr(149) => 350, chr(150) => 500, chr(151) => 1000, chr(152) => 333, chr(153) => 980,
	chr(154) => 389, chr(155) => 333, chr(156) => 722, chr(157) => 350, chr(158) => 444, chr(159) => 722, chr(160) => 250, chr(161) => 333, chr(162) => 500, chr(163) => 500, chr(164) => 500, chr(165) => 500, chr(166) => 200, chr(167) => 500, chr(168) => 333, chr(169) => 760, chr(170) => 276, chr(171) => 500, chr(172) => 564, chr(173) => 333, chr(174) => 760, chr(175) => 333,
	chr(176) => 400, chr(177) => 564, chr(178) => 300, chr(179) => 300, chr(180) => 333, chr(181) => 500, chr(182) => 453, chr(183) => 250, chr(184) => 333, chr(185) => 300, chr(186) => 310, chr(187) => 500, chr(188) => 750, chr(189) => 750, chr(190) => 750, chr(191) => 444, chr(192) => 722, chr(193) => 722, chr(194) => 722, chr(195) => 722, chr(196) => 722, chr(197) => 722,
	chr(198) => 889, chr(199) => 667, chr(200) => 611, chr(201) => 611, chr(202) => 611, chr(203) => 611, chr(204) => 333, chr(205) => 333, chr(206) => 333, chr(207) => 333, chr(208) => 722, chr(209) => 722, chr(210) => 722, chr(211) => 722, chr(212) => 722, chr(213) => 722, chr(214) => 722, chr(215) => 564, chr(216) => 722, chr(217) => 722, chr(218) => 722, chr(219) => 722,
	chr(220) => 722, chr(221) => 722, chr(222) => 556, chr(223) => 500, chr(224) => 444, chr(225) => 444, chr(226) => 444, chr(227) => 444, chr(228) => 444, chr(229) => 444, chr(230) => 667, chr(231) => 444, chr(232) => 444, chr(233) => 444, chr(234) => 444, chr(235) => 444, chr(236) => 278, chr(237) => 278, chr(238) => 278, chr(239) => 278, chr(240) => 500, chr(241) => 500,
	chr(242) => 500, chr(243) => 500, chr(244) => 500, chr(245) => 500, chr(246) => 500, chr(247) => 564, chr(248) => 500, chr(249) => 500, chr(250) => 500, chr(251) => 500, chr(252) => 500, chr(253) => 500, chr(254) => 500, chr(255) => 500);

//$desc=array('Ascent'=>683,'Descent'=>-217,'CapHeight'=>662,'FontBBox'=>'[-168 -218 1000 898]');
$desc = array('Flags' => 32, 'FontBBox' => '[-168 -218 1000 898]', 'ItalicAngle' => 0, 'Ascent' => 898, 'Descent' => -218, 'Leading' => 0, 'CapHeight' => 662, 'XHeight' => 450, 'StemV' => 84, 'StemH' => 28, 'AvgWidth' => 495, 'MaxWidth' => 1000, 'MissingWidth' => 495);
$up = -100;
$ut = 50;
$kerninfo = array(chr(49) => array(chr(49) => -37,), chr(65) => array(chr(84) => -110, chr(86) => -128, chr(87) => -80, chr(89) => -91, chr(118) => -74, chr(119) => -91, chr(121) => -91, chr(146) => -110,), chr(70) => array(chr(44) => -80, chr(46) => -80, chr(65) => -74,), chr(76) => array(chr(84) => -91, chr(86) => -91, chr(87) => -74, chr(89) => -100, chr(121) => -55, chr(146) => -91,), chr(80) => array(chr(44) => -110, chr(46) => -110, chr(65) => -91,), chr(82) => array(chr(84) => -60, chr(86) => -80, chr(87) => -55, chr(89) => -55, chr(121) => -40,), chr(84) => array(chr(44) => -74, chr(46) => -74, chr(58) => -49, chr(65) => -80, chr(79) => -18, chr(97) => -69, chr(99) => -69, chr(101) => -69, chr(105) => -35, chr(111) => -69, chr(114) => -35, chr(115) => -69, chr(117) => -35, chr(119) => -69, chr(121) => -69,), chr(86) => array(chr(44) => -128, chr(46) => -128, chr(58) => -74, chr(65) => -128, chr(97) => -110, chr(101) => -110, chr(105) => -60, chr(111) => -128, chr(114) => -60, chr(117) => -60, chr(121) => -110,), chr(87) => array(chr(44) => -91, chr(46) => -91, chr(58) => -37, chr(65) => -110, chr(97) => -80, chr(101) => -80, chr(105) => -40, chr(111) => -80, chr(114) => -40, chr(117) => -40, chr(121) => -60,), chr(89) => array(chr(44) => -128, chr(46) => -128, chr(58) => -91, chr(65) => -110, chr(97) => -100, chr(101) => -100, chr(105) => -55, chr(111) => -100, chr(112) => -91, chr(113) => -110, chr(117) => -110, chr(118) => -100,), chr(102) => array(chr(102) => -18, chr(146) => 55,), chr(114) => array(chr(44) => -40, chr(46) => -55, chr(103) => -18, chr(146) => 37,), chr(118) => array(chr(44) => -64, chr(46) => -64,), chr(119) => array(chr(44) => -64, chr(46) => -64,), chr(121) => array(chr(44) => -64, chr(46) => -64,), chr(145) => array(chr(145) => -74,), chr(146) => array(chr(115) => -55, chr(116) => -18, chr(146) => -74,),);
