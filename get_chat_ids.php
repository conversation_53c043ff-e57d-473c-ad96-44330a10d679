<?php
// เครื่องมือช่วยหา Chat IDs สำหรับ Telegram Bot
require_once 'includes/auth.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

$message = '';
$messageType = '';
$chatIds = [];

if ($_POST && isset($_POST['get_chat_ids'])) {
    $botToken = trim($_POST['bot_token']);
    
    if (empty($botToken)) {
        $message = 'กรุณาใส่ Bot Token';
        $messageType = 'danger';
    } else {
        try {
            // เรียก Telegram API เพื่อดึง updates
            $url = "https://api.telegram.org/bot{$botToken}/getUpdates";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                $result = json_decode($response, true);
                
                if ($result['ok']) {
                    $updates = $result['result'];
                    
                    if (empty($updates)) {
                        $message = 'ไม่พบข้อความใดๆ กรุณาส่งข้อความให้ Bot ก่อน แล้วลองใหม่';
                        $messageType = 'warning';
                    } else {
                        // ดึง Chat IDs จาก updates
                        $foundChats = [];
                        
                        foreach ($updates as $update) {
                            if (isset($update['message']['chat'])) {
                                $chat = $update['message']['chat'];
                                $chatId = $chat['id'];
                                
                                if (!isset($foundChats[$chatId])) {
                                    $foundChats[$chatId] = [
                                        'id' => $chatId,
                                        'type' => $chat['type'],
                                        'title' => $chat['title'] ?? ($chat['first_name'] ?? '') . ' ' . ($chat['last_name'] ?? ''),
                                        'username' => $chat['username'] ?? '',
                                        'last_message' => $update['message']['text'] ?? '[ไม่มีข้อความ]',
                                        'date' => date('Y-m-d H:i:s', $update['message']['date'])
                                    ];
                                }
                            }
                        }
                        
                        $chatIds = array_values($foundChats);
                        $message = 'พบ Chat IDs จำนวน ' . count($chatIds) . ' รายการ';
                        $messageType = 'success';
                    }
                } else {
                    $message = 'Bot Token ไม่ถูกต้อง: ' . ($result['description'] ?? 'Unknown error');
                    $messageType = 'danger';
                }
            } else {
                $message = 'ไม่สามารถเชื่อมต่อ Telegram API ได้ (HTTP ' . $httpCode . ')';
                $messageType = 'danger';
            }
            
        } catch (Exception $e) {
            $message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>หา Chat IDs - Telegram Bot</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>เครื่องมือหา Chat IDs</h1>
            <p class="subtitle">สำหรับ Telegram Bot</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="index.php"><i class="fas fa-list"></i> รายการ Assets</a></li>
                <li><a href="telegram_settings.php"><i class="fab fa-telegram"></i> ตั้งค่า Telegram</a></li>
                <li><a href="test_telegram.php"><i class="fas fa-vial"></i> ทดสอบ Telegram</a></li>
                <li><a href="index.php"><i class="fas fa-arrow-left"></i> กลับหน้าหลัก</a></li>
            </ul>
        </nav>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- คำแนะนำ -->
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-info-circle"></i> วิธีการหา Chat IDs</h2>
            </div>
            <div class="card-body">
                <div class="instructions">
                    <h4>ขั้นตอนการหา Chat IDs:</h4>
                    <ol>
                        <li><strong>สร้าง Bot</strong> ผ่าน @BotFather และได้ Bot Token</li>
                        <li><strong>เพิ่ม Bot</strong> เข้าแชทหรือกลุ่มที่ต้องการ</li>
                        <li><strong>ส่งข้อความ</strong> ให้ Bot (เช่น "/start" หรือ "hello")</li>
                        <li><strong>ใส่ Bot Token</strong> ในช่องด้านล่างแล้วกดค้นหา</li>
                        <li><strong>คัดลอก Chat IDs</strong> ที่ต้องการไปใส่ในการตั้งค่า</li>
                    </ol>
                    
                    <div class="alert alert-info">
                        <strong>หมายเหตุ:</strong>
                        <ul>
                            <li>Chat ID ของแชทส่วนตัวจะเป็นเลขบวก (เช่น 123456789)</li>
                            <li>Chat ID ของกลุ่มจะเป็นเลขลบ (เช่น -987654321)</li>
                            <li>ต้องส่งข้อความให้ Bot ก่อนจึงจะหา Chat ID ได้</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- ฟอร์มค้นหา Chat IDs -->
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-search"></i> ค้นหา Chat IDs</h2>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="form-group">
                        <label for="bot_token">Bot Token</label>
                        <input type="text" id="bot_token" name="bot_token" class="form-control" 
                               value="<?= htmlspecialchars($_POST['bot_token'] ?? '') ?>"
                               placeholder="1234567890:ABCdefGHIjklMNOpqrsTUVwxyz" required>
                        <small class="form-text">Bot Token ที่ได้จาก @BotFather</small>
                    </div>
                    
                    <button type="submit" name="get_chat_ids" class="btn btn-primary">
                        <i class="fas fa-search"></i> ค้นหา Chat IDs
                    </button>
                </form>
            </div>
        </div>

        <!-- แสดงผลลัพธ์ -->
        <?php if (!empty($chatIds)): ?>
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-list"></i> Chat IDs ที่พบ</h2>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Chat ID</th>
                                <th>ประเภท</th>
                                <th>ชื่อ/กลุ่ม</th>
                                <th>Username</th>
                                <th>ข้อความล่าสุด</th>
                                <th>วันที่</th>
                                <th>การดำเนินการ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($chatIds as $chat): ?>
                            <tr>
                                <td>
                                    <code class="chat-id-value"><?= $chat['id'] ?></code>
                                </td>
                                <td>
                                    <?php
                                    $typeIcons = [
                                        'private' => '<i class="fas fa-user"></i> ส่วนตัว',
                                        'group' => '<i class="fas fa-users"></i> กลุ่ม',
                                        'supergroup' => '<i class="fas fa-users"></i> Supergroup',
                                        'channel' => '<i class="fas fa-bullhorn"></i> Channel'
                                    ];
                                    echo $typeIcons[$chat['type']] ?? $chat['type'];
                                    ?>
                                </td>
                                <td><?= htmlspecialchars($chat['title']) ?></td>
                                <td><?= $chat['username'] ? '@' . htmlspecialchars($chat['username']) : '-' ?></td>
                                <td><?= htmlspecialchars(substr($chat['last_message'], 0, 50)) ?><?= strlen($chat['last_message']) > 50 ? '...' : '' ?></td>
                                <td><?= $chat['date'] ?></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="copyChatId('<?= $chat['id'] ?>')">
                                        <i class="fas fa-copy"></i> คัดลอก
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="copy-all-section">
                    <h4>คัดลอกทั้งหมด:</h4>
                    <div class="input-group">
                        <input type="text" id="all_chat_ids" class="form-control" 
                               value="<?= implode(', ', array_column($chatIds, 'id')) ?>" readonly>
                        <div class="input-group-append">
                            <button class="btn btn-success" onclick="copyAllChatIds()">
                                <i class="fas fa-copy"></i> คัดลอกทั้งหมด
                            </button>
                        </div>
                    </div>
                    <small class="form-text">คัดลอกแล้วไปวางในหน้าตั้งค่า Telegram</small>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script>
        function copyChatId(chatId) {
            navigator.clipboard.writeText(chatId).then(function() {
                alert('คัดลอก Chat ID: ' + chatId + ' เรียบร้อยแล้ว');
            }).catch(function(err) {
                console.error('ไม่สามารถคัดลอกได้: ', err);
                // Fallback สำหรับเบราว์เซอร์เก่า
                const textArea = document.createElement('textarea');
                textArea.value = chatId;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('คัดลอก Chat ID: ' + chatId + ' เรียบร้อยแล้ว');
            });
        }

        function copyAllChatIds() {
            const allChatIds = document.getElementById('all_chat_ids');
            allChatIds.select();
            allChatIds.setSelectionRange(0, 99999); // สำหรับมือถือ
            
            navigator.clipboard.writeText(allChatIds.value).then(function() {
                alert('คัดลอก Chat IDs ทั้งหมดเรียบร้อยแล้ว');
            }).catch(function(err) {
                document.execCommand('copy');
                alert('คัดลอก Chat IDs ทั้งหมดเรียบร้อยแล้ว');
            });
        }
    </script>

    <style>
        .instructions ol {
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .chat-id-value {
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-weight: bold;
        }
        
        .copy-all-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        
        .input-group {
            display: flex;
        }
        
        .input-group .form-control {
            flex: 1;
        }
        
        .input-group-append {
            display: flex;
        }
        
        .input-group-append .btn {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
    </style>
</body>
</html>
