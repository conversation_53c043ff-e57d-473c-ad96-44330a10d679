<?php
// Export ข้อมูลเป็น CSV
error_reporting(E_ALL);
ini_set('display_errors', 1);

// เชื่อมต่อฐานข้อมูล
$host = 'localhost';
$username = 'root';
$password = 'Wxmujwsofu@1234';
$database = 'asset_management';

$conn = new mysqli($host, $username, $password, $database);
$conn->set_charset("utf8mb4");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// รับพารามิเตอร์
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';
$filter_serial = $_GET['filter_serial'] ?? '';

// สร้าง SQL query
$sql = "SELECT * FROM assets WHERE 1=1";
$params = [];
$conditions = [];

// เพิ่มเงื่อนไขการกรอง
if (!empty($search)) {
    $conditions[] = "(type LIKE ? OR brand LIKE ? OR model LIKE ? OR tag LIKE ? OR hostname LIKE ? OR department LIKE ? OR asset_id LIKE ? OR serial_number LIKE ?)";
    $searchTerm = '%' . $search . '%';
    for ($i = 0; $i < 8; $i++) {
        $params[] = $searchTerm;
    }
}

if (!empty($filter_type)) {
    $conditions[] = "type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_brand)) {
    $conditions[] = "brand = ?";
    $params[] = $filter_brand;
}

if (!empty($filter_department)) {
    $conditions[] = "department = ?";
    $params[] = $filter_department;
}

if (!empty($filter_status)) {
    $conditions[] = "status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_os)) {
    $conditions[] = "operating_system = ?";
    $params[] = $filter_os;
}

if (!empty($filter_serial)) {
    $conditions[] = "serial_number LIKE ?";
    $params[] = '%' . $filter_serial . '%';
}

// รวมเงื่อนไข
if (!empty($conditions)) {
    $sql .= " AND " . implode(" AND ", $conditions);
}

$sql .= " ORDER BY created_date DESC";

// เตรียม prepared statement
$stmt = $conn->prepare($sql);

// Bind parameters ถ้ามี
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();

// สร้างชื่อไฟล์
$filename = "asset_data_" . date('Y-m-d_H-i-s');
if (!empty($filter_type)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_type);
if (!empty($filter_department)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_department);

// ตั้งค่า headers สำหรับ CSV
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
header('Pragma: no-cache');
header('Expires: 0');

// เปิด output stream
$output = fopen('php://output', 'w');

// เพิ่ม BOM สำหรับ UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// หัวตาราง CSV (ตรงกับโครงสร้างฐานข้อมูล)
$headers = [
    'ID',
    'Asset ID',
    'Type',
    'Brand',
    'Model',
    'Tag',
    'Department',
    'Status',
    'Hostname',
    'IP Address',
    'Operating System',
    'Serial Number',
    'Warranty Expire',
    'Description',
    'Set',
    'Created Date',
    'Created By',
    'Updated Date',
    'Updated By'
];

fputcsv($output, $headers);

// ข้อมูล
while ($row = $result->fetch_assoc()) {
    $data = [
        $row['id'] ?? '',
        $row['asset_id'] ?? '',
        $row['type'] ?? '',
        $row['brand'] ?? '',
        $row['model'] ?? '',
        $row['tag'] ?? '',
        $row['department'] ?? '',
        $row['status'] ?? '',
        $row['hostname'] ?? '',
        $row['ip_address'] ?? '',
        $row['operating_system'] ?? '',
        $row['serial_number'] ?? '',
        $row['warranty_expire'] ?? '',
        $row['description'] ?? '',
        $row['set_name'] ?? '',
        $row['created_date'] ?? '',
        $row['created_by'] ?? '',
        $row['updated_date'] ?? '',
        $row['updated_by'] ?? ''
    ];

    fputcsv($output, $data);
}

fclose($output);

// ปิดการเชื่อมต่อฐานข้อมูล
$stmt->close();
$conn->close();
?>
