<?php
// ล้าง System Cache
header('Content-Type: application/json; charset=utf-8');

require_once 'includes/auth.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

// รับข้อมูล JSON
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

try {
    if ($action !== 'clear_cache') {
        throw new Exception('Invalid action');
    }

    $results = [];
    $errors = [];

    // 1. ล้าง PHP OpCache
    if (function_exists('opcache_reset')) {
        if (opcache_reset()) {
            $results[] = '✓ PHP OpCache ล้างเรียบร้อย';
        } else {
            $errors[] = '✗ ไม่สามารถล้าง PHP OpCache ได้';
        }
    } else {
        $results[] = '- PHP OpCache ไม่ได้เปิดใช้งาน';
    }

    // 2. ล้าง APCu Cache (ถ้ามี)
    if (function_exists('apcu_clear_cache')) {
        if (apcu_clear_cache()) {
            $results[] = '✓ APCu Cache ล้างเรียบร้อย';
        } else {
            $errors[] = '✗ ไม่สามารถล้าง APCu Cache ได้';
        }
    } else {
        $results[] = '- APCu Cache ไม่ได้เปิดใช้งาน';
    }

    // 3. ล้าง Session Files
    $sessionPath = session_save_path();
    if (empty($sessionPath)) {
        $sessionPath = sys_get_temp_dir();
    }
    
    $sessionCleared = 0;
    if (is_dir($sessionPath) && is_writable($sessionPath)) {
        $sessionFiles = glob($sessionPath . '/sess_*');
        foreach ($sessionFiles as $file) {
            if (is_file($file) && unlink($file)) {
                $sessionCleared++;
            }
        }
        $results[] = "✓ ล้าง Session Files: {$sessionCleared} ไฟล์";
    } else {
        $results[] = '- ไม่สามารถเข้าถึง Session Path ได้';
    }

    // 4. ล้าง Temporary Files
    $tempPath = sys_get_temp_dir();
    $tempCleared = 0;
    $tempPatterns = [
        '/tmp_*',
        '/php*',
        '/asset_*'
    ];

    foreach ($tempPatterns as $pattern) {
        $tempFiles = glob($tempPath . $pattern);
        foreach ($tempFiles as $file) {
            if (is_file($file) && is_writable($file)) {
                $fileAge = time() - filemtime($file);
                // ลบไฟล์ที่เก่ากว่า 1 ชั่วโมง
                if ($fileAge > 3600 && unlink($file)) {
                    $tempCleared++;
                }
            }
        }
    }
    $results[] = "✓ ล้าง Temporary Files: {$tempCleared} ไฟล์";

    // 5. ล้าง Application Cache (ถ้ามี)
    $cacheDir = 'cache/';
    $appCacheCleared = 0;
    if (is_dir($cacheDir)) {
        $cacheFiles = glob($cacheDir . '*');
        foreach ($cacheFiles as $file) {
            if (is_file($file) && unlink($file)) {
                $appCacheCleared++;
            }
        }
        $results[] = "✓ ล้าง Application Cache: {$appCacheCleared} ไฟล์";
    } else {
        $results[] = '- ไม่มี Application Cache Directory';
    }

    // 6. ล้าง Log Files เก่า (เก็บไว้แค่ 7 วันล่าสุด)
    $logDir = 'logs/';
    $logCleared = 0;
    if (is_dir($logDir)) {
        $logFiles = glob($logDir . '*.log');
        foreach ($logFiles as $file) {
            if (is_file($file)) {
                $fileAge = time() - filemtime($file);
                // ลบ log ที่เก่ากว่า 7 วัน
                if ($fileAge > (7 * 24 * 3600) && unlink($file)) {
                    $logCleared++;
                }
            }
        }
        $results[] = "✓ ล้าง Log Files เก่า: {$logCleared} ไฟล์";
    } else {
        $results[] = '- ไม่มี Log Directory';
    }

    // 7. ล้าง Database Query Cache (MySQL)
    try {
        $host = 'localhost';
        $username = 'root';
        $password = 'Wxmujwsofu@1234';
        $database = 'asset_management';

        $conn = new mysqli($host, $username, $password, $database);
        if ($conn->connect_error) {
            $errors[] = '✗ ไม่สามารถเชื่อมต่อฐานข้อมูลได้';
        } else {
            // ล้าง Query Cache
            $conn->query("RESET QUERY CACHE");
            $results[] = '✓ ล้าง Database Query Cache เรียบร้อย';
            $conn->close();
        }
    } catch (Exception $e) {
        $errors[] = '✗ เกิดข้อผิดพลาดในการล้าง Database Cache: ' . $e->getMessage();
    }

    // 8. รีเซ็ต File Stat Cache
    clearstatcache();
    $results[] = '✓ รีเซ็ต File Stat Cache เรียบร้อย';

    // สร้างข้อความสรุป
    $message = "ผลการล้าง System Cache:\n\n";
    $message .= implode("\n", $results);
    
    if (!empty($errors)) {
        $message .= "\n\nข้อผิดพลาด:\n";
        $message .= implode("\n", $errors);
    }

    $message .= "\n\nเวลาที่ดำเนินการ: " . date('Y-m-d H:i:s');

    echo json_encode([
        'success' => true,
        'message' => $message,
        'results' => $results,
        'errors' => $errors,
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
    ]);
}
?>
