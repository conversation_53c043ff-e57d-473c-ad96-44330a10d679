<?php
require_once 'includes/auth.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

// สร้างไฟล์ตัวอย่าง CSV
$filename = 'asset_update_template_' . date('Y-m-d') . '.csv';

// Header ของไฟล์ CSV
$headers = [
    'asset_id',
    'type',
    'brand',
    'model',
    'tag',
    'department',
    'status',
    'hostname',
    'ip_address',
    'operating_system',
    'serial_number',
    'warranty_expire',
    'description',
    'set_name'
];

// ดึงข้อมูล Assets จริงจากฐานข้อมูลเพื่อใช้เป็นตัวอย่าง
require_once 'config/database.php';

$sampleData = [];
try {
    $stmt = $pdo->query("SELECT asset_id, type, brand, model, tag, department, status, hostname, operating_system, serial_number, warranty_expire, description, set_name FROM assets LIMIT 3");
    $realAssets = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (!empty($realAssets)) {
        // ใช้ข้อมูลจริงแต่ปรับเป็นตัวอย่างการอัปเดต
        foreach ($realAssets as $asset) {
            $sampleData[] = [
                'asset_id' => $asset['asset_id'],
                'type' => $asset['type'],
                'brand' => $asset['brand'],
                'model' => $asset['model'],
                'tag' => $asset['tag'] . '-UPDATED', // ตัวอย่างการเปลี่ยนแปลง
                'department' => $asset['department'],
                'status' => 'ทดสอบ Import', // ตัวอย่างการเปลี่ยนแปลง
                'hostname' => $asset['hostname'],
                'ip_address' => $asset['ip_address'] ?: '*************', // ตัวอย่าง IP
                'operating_system' => $asset['operating_system'],
                'serial_number' => $asset['serial_number'],
                'warranty_expire' => $asset['warranty_expire'],
                'description' => 'Updated via Import - ' . date('Y-m-d H:i:s'), // ตัวอย่างการเปลี่ยนแปลง
                'set_name' => $asset['set_name']
            ];
        }
    }
} catch (Exception $e) {
    // ถ้าไม่สามารถดึงข้อมูลได้ ใช้ข้อมูลตัวอย่างแบบเดิม
    error_log("Error fetching sample data: " . $e->getMessage());
}

// ถ้าไม่มีข้อมูลจริง ใช้ข้อมูลตัวอย่าง
if (empty($sampleData)) {
    $sampleData = [
        [
            'asset_id' => 'SAMPLE-001',
            'type' => 'Desktop',
            'brand' => 'Dell',
            'model' => 'OptiPlex 7090',
            'tag' => 'SAMPLE-001-UPDATED',
            'department' => 'IT Department',
            'status' => 'ทดสอบ Import',
            'hostname' => 'PC-SAMPLE-001',
            'ip_address' => '*************',
            'operating_system' => 'Windows 11',
            'serial_number' => 'DL123456789',
            'warranty_expire' => '2025-12-31',
            'description' => 'Updated via Import - ' . date('Y-m-d H:i:s'),
            'set_name' => 'Sample Set'
        ],
        [
            'asset_id' => 'SAMPLE-002',
            'type' => 'Laptop',
            'brand' => 'HP',
            'model' => 'EliteBook 840',
            'tag' => 'SAMPLE-002-UPDATED',
            'department' => 'HR Department',
            'status' => 'ทดสอบ Import',
            'hostname' => 'LT-SAMPLE-002',
            'ip_address' => '*************',
            'operating_system' => 'Windows 11',
            'serial_number' => 'HP987654321',
            'warranty_expire' => '2025-06-30',
            'description' => 'Updated via Import - ' . date('Y-m-d H:i:s'),
            'set_name' => 'Sample Set'
        ]
    ];
}

// ตั้งค่า HTTP headers สำหรับดาวน์โหลดไฟล์
header('Content-Type: text/csv; charset=UTF-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Expires: 0');

// เปิด output stream
$output = fopen('php://output', 'w');

// เพิ่ม BOM สำหรับ UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// เขียน header
fputcsv($output, $headers);

// เขียนข้อมูลตัวอย่าง
foreach ($sampleData as $row) {
    fputcsv($output, $row);
}

// เพิ่มแถวว่างสำหรับให้ผู้ใช้เพิ่มข้อมูล
for ($i = 0; $i < 5; $i++) {
    $emptyRow = array_fill(0, count($headers), '');
    fputcsv($output, $emptyRow);
}

// ปิด output stream
fclose($output);
exit;
?>
