<?php
require_once 'includes/auth.php';
require_once 'config/database.php';
require_once 'classes/AssetManager.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>ทดสอบ Import Update Assets แบบง่าย</h2>";

$assetManager = new AssetManager($pdo);

// ทดสอบการอัปเดต Asset โดยตรง
if (isset($_GET['test']) && $_GET['test'] === 'direct') {
    echo "<h3>ทดสอบการอัปเดต Asset โดยตรง</h3>";
    
    try {
        // ค้นหา Asset แรกในระบบ
        $stmt = $pdo->query("SELECT * FROM assets LIMIT 1");
        $asset = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$asset) {
            echo "<p style='color: red;'>ไม่มี Asset ในระบบ กรุณาเพิ่ม Asset ก่อน</p>";
        } else {
            echo "<h4>Asset ที่จะทดสอบ:</h4>";
            echo "<ul>";
            echo "<li><strong>ID:</strong> " . $asset['id'] . "</li>";
            echo "<li><strong>Asset ID:</strong> " . $asset['asset_id'] . "</li>";
            echo "<li><strong>Type:</strong> " . $asset['type'] . "</li>";
            echo "<li><strong>Brand:</strong> " . $asset['brand'] . "</li>";
            echo "<li><strong>Status:</strong> " . $asset['status'] . "</li>";
            echo "</ul>";
            
            // ทดสอบการอัปเดต
            $updateData = [
                'type' => $asset['type'],
                'brand' => $asset['brand'],
                'model' => $asset['model'],
                'tag' => $asset['tag'],
                'department' => $asset['department'],
                'status' => 'ทดสอบ Import', // เปลี่ยนสถานะเป็นทดสอบ
                'hostname' => $asset['hostname'],
                'operating_system' => $asset['operating_system'],
                'serial_number' => $asset['serial_number'],
                'asset_id' => $asset['asset_id'],
                'warranty_expire' => $asset['warranty_expire'],
                'description' => 'ทดสอบการอัปเดตผ่าน Import - ' . date('Y-m-d H:i:s'),
                'set_name' => $asset['set_name'],
                'updated_by' => getCurrentUserFullName() ?? 'Test System'
            ];
            
            echo "<h4>ข้อมูลที่จะอัปเดต:</h4>";
            echo "<pre>" . print_r($updateData, true) . "</pre>";
            
            $result = $assetManager->updateAsset($asset['id'], $updateData);
            
            if ($result) {
                echo "<p style='color: green;'>✅ อัปเดต Asset สำเร็จ!</p>";
                
                // ดึงข้อมูลใหม่
                $updatedAsset = $assetManager->getAsset($asset['id']);
                echo "<h4>ข้อมูลหลังอัปเดต:</h4>";
                echo "<ul>";
                echo "<li><strong>Status:</strong> " . $updatedAsset['status'] . "</li>";
                echo "<li><strong>Description:</strong> " . $updatedAsset['description'] . "</li>";
                echo "<li><strong>Updated Date:</strong> " . $updatedAsset['updated_date'] . "</li>";
                echo "<li><strong>Updated By:</strong> " . $updatedAsset['updated_by'] . "</li>";
                echo "</ul>";
            } else {
                echo "<p style='color: red;'>❌ ไม่สามารถอัปเดต Asset ได้</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
}

// ทดสอบการอ่านไฟล์ CSV และประมวลผล
if (isset($_GET['test']) && $_GET['test'] === 'csv') {
    echo "<h3>ทดสอบการประมวลผล CSV</h3>";
    
    try {
        // ดึง Asset แรกเพื่อใช้ในการทดสอบ
        $stmt = $pdo->query("SELECT asset_id, type, brand, model, status FROM assets LIMIT 1");
        $asset = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$asset) {
            echo "<p style='color: red;'>ไม่มี Asset ในระบบ</p>";
        } else {
            // สร้างไฟล์ CSV ทดสอบ
            $csvContent = "asset_id,type,brand,model,status,description\n";
            $csvContent .= $asset['asset_id'] . ",";
            $csvContent .= $asset['type'] . ",";
            $csvContent .= $asset['brand'] . ",";
            $csvContent .= $asset['model'] . ",";
            $csvContent .= "ทดสอบ Import CSV,";
            $csvContent .= "ทดสอบการอัปเดตผ่าน CSV - " . date('Y-m-d H:i:s') . "\n";
            
            $testFile = 'test_import_simple.csv';
            file_put_contents($testFile, $csvContent);
            
            echo "<h4>ไฟล์ CSV ทดสอบ:</h4>";
            echo "<pre>" . htmlspecialchars($csvContent) . "</pre>";
            
            // รวมฟังก์ชันจาก import_update_assets.php
            include_once 'import_update_assets.php';
            
            // อ่านไฟล์ CSV
            $importData = parseCSVFile($testFile);
            echo "<h4>ข้อมูลที่อ่านได้:</h4>";
            echo "<pre>" . print_r($importData, true) . "</pre>";
            
            // ประมวลผลการอัปเดต
            if (!empty($importData)) {
                $results = processAssetUpdates($assetManager, $importData, $pdo);
                
                echo "<h4>ผลการประมวลผล:</h4>";
                foreach ($results as $result) {
                    $color = $result['status'] === 'success' ? 'green' : ($result['status'] === 'error' ? 'red' : 'orange');
                    echo "<div style='color: {$color}; margin: 10px 0; padding: 10px; border: 1px solid {$color}; border-radius: 5px;'>";
                    echo "<strong>แถว {$result['row']}:</strong> {$result['message']}<br>";
                    echo "<strong>Asset ID:</strong> {$result['asset_id']}<br>";
                    echo "<strong>สถานะ:</strong> {$result['status']}<br>";
                    if (isset($result['changes'])) {
                        echo "<strong>การเปลี่ยนแปลง:</strong><br>";
                        foreach ($result['changes'] as $change) {
                            echo "- {$change['field_name']}: {$change['old_value']} → {$change['new_value']}<br>";
                        }
                    }
                    echo "</div>";
                }
            }
            
            // ลบไฟล์ทดสอบ
            unlink($testFile);
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
}

// แสดงรายการ Assets ปัจจุบัน
echo "<h3>Assets ในระบบ:</h3>";
try {
    $stmt = $pdo->query("SELECT id, asset_id, type, brand, model, status, updated_date FROM assets ORDER BY updated_date DESC LIMIT 5");
    $assets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($assets)) {
        echo "<p>ไม่มี Assets ในระบบ</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>Asset ID</th><th>Type</th><th>Brand</th><th>Model</th><th>Status</th><th>Updated</th>";
        echo "</tr>";
        foreach ($assets as $asset) {
            echo "<tr>";
            echo "<td>" . $asset['id'] . "</td>";
            echo "<td>" . htmlspecialchars($asset['asset_id']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['type']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['brand']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['model']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['status']) . "</td>";
            echo "<td>" . $asset['updated_date'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<hr>
<p>
    <a href="?test=direct" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        🧪 ทดสอบอัปเดตโดยตรง
    </a>
    <a href="?test=csv" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        📄 ทดสอบประมวลผล CSV
    </a>
    <a href="import_update_assets.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        📁 Import Update Assets
    </a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th { background: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
