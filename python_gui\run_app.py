"""
Simple launcher for Asset Management GUI
"""

import sys
import os
import subprocess
from pathlib import Path

def check_requirements():
    """Check if required packages are installed"""
    try:
        import mysql.connector
        import ttkbootstrap
        from PIL import Image
        import dateutil
        return True
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        return False

def install_requirements():
    """Install requirements if missing"""
    print("🔧 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Packages installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install packages")
        return False

def main():
    """Main launcher function"""
    print("🚀 Asset Management System - Python GUI Launcher")
    print("=" * 55)
    
    # Check if we're in the right directory
    if not Path("asset_gui.py").exists():
        print("❌ Error: asset_gui.py not found!")
        print("Please run this script from the python_gui directory.")
        input("Press Enter to exit...")
        return
    
    # Check requirements
    if not check_requirements():
        print("\n📦 Some required packages are missing.")
        response = input("Would you like to install them now? (y/n): ").lower().strip()
        
        if response in ['y', 'yes']:
            if not install_requirements():
                print("❌ Failed to install packages. Please install manually:")
                print("pip install -r requirements.txt")
                input("Press Enter to exit...")
                return
        else:
            print("❌ Cannot run without required packages.")
            input("Press Enter to exit...")
            return
    
    # Test database connection
    try:
        from database_config import DatabaseConfig
        print("🔍 Testing database connection...")
        success, message = DatabaseConfig.test_connection()
        if not success:
            print(f"❌ Database connection failed: {message}")
            print("Please check your database configuration in database_config.py")
            input("Press Enter to exit...")
            return
        print(f"✅ Database connection successful!")
    except Exception as e:
        print(f"❌ Database error: {e}")
        input("Press Enter to exit...")
        return
    
    # Launch the application
    print("🎉 Starting Asset Management GUI...")
    try:
        from asset_gui import main as run_gui
        run_gui()
    except Exception as e:
        print(f"❌ Application error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
