<?php
// Export ข้อมูลผู้ใช้เป็นรูปแบบต่างๆ
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/auth.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

// เชื่อมต่อฐานข้อมูล
$host = 'localhost';
$username = 'root';
$password = 'Wxmujwsofu@1234';
$database = 'asset_management';

$conn = new mysqli($host, $username, $password, $database);
$conn->set_charset("utf8mb4");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// รับพารามิเตอร์
$format = $_GET['format'] ?? 'csv';
$view = isset($_GET['view']) && $_GET['view'] === 'true';

// สร้าง SQL query สำหรับดึงข้อมูลผู้ใช้
$sql = "SELECT id, username, full_name, email, role, status, last_login, created_date FROM users ORDER BY created_date DESC";

$result = $conn->query($sql);

if (!$result) {
    die("Query failed: " . $conn->error);
}

// รวบรวมข้อมูล
$users = [];
while ($row = $result->fetch_assoc()) {
    $users[] = [
        'id' => (int)($row['id'] ?? 0),
        'username' => $row['username'] ?? '',
        'full_name' => $row['full_name'] ?? '',
        'email' => $row['email'] ?? '',
        'role' => $row['role'] ?? 'User',
        'status' => $row['status'] ?? 'Active',
        'last_login' => $row['last_login'] ?? null,
        'created_date' => $row['created_date'] ?? null
    ];
}

// สร้างชื่อไฟล์
$filename = "users_data_" . date('Y-m-d_H-i-s');

// ส่งออกตามรูปแบบที่เลือก
switch ($format) {
    case 'csv':
        exportCSV($users, $filename);
        break;
    case 'excel':
        exportExcel($users, $filename);
        break;
    case 'json':
        exportJSON($users, $filename, $view);
        break;
    default:
        exportCSV($users, $filename);
        break;
}

// ปิดการเชื่อมต่อฐานข้อมูล
$conn->close();

// ฟังก์ชันส่งออก CSV
function exportCSV($users, $filename) {
    // ตั้งค่า headers สำหรับ CSV
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    header('Pragma: no-cache');
    header('Expires: 0');

    // เปิด output stream
    $output = fopen('php://output', 'w');

    // เพิ่ม BOM สำหรับ UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // หัวตาราง CSV
    $headers = [
        'ID',
        'Username',
        'ชื่อ-นามสกุล',
        'Email',
        'บทบาท',
        'สถานะ',
        'เข้าสู่ระบบล่าสุด',
        'วันที่สร้างบัญชี'
    ];

    fputcsv($output, $headers);

    // ข้อมูล
    foreach ($users as $user) {
        $data = [
            $user['id'],
            $user['username'],
            $user['full_name'],
            $user['email'],
            $user['role'],
            $user['status'],
            $user['last_login'] ? date('Y-m-d H:i:s', strtotime($user['last_login'])) : '',
            $user['created_date'] ? date('Y-m-d H:i:s', strtotime($user['created_date'])) : ''
        ];
        
        fputcsv($output, $data);
    }

    fclose($output);
}

// ฟังก์ชันส่งออก Excel
function exportExcel($users, $filename) {
    // ตั้งค่า headers สำหรับ Excel
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');

    // เริ่มต้น HTML table สำหรับ Excel
    echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
    echo '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"></head>';
    echo '<body>';
    echo '<table border="1">';
    
    // หัวตาราง
    echo '<tr style="background-color: #4CAF50; color: white; font-weight: bold;">';
    echo '<th>ID</th>';
    echo '<th>Username</th>';
    echo '<th>ชื่อ-นามสกุล</th>';
    echo '<th>Email</th>';
    echo '<th>บทบาท</th>';
    echo '<th>สถานะ</th>';
    echo '<th>เข้าสู่ระบบล่าสุด</th>';
    echo '<th>วันที่สร้างบัญชี</th>';
    echo '</tr>';

    // ข้อมูล
    foreach ($users as $user) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($user['id']) . '</td>';
        echo '<td>' . htmlspecialchars($user['username']) . '</td>';
        echo '<td>' . htmlspecialchars($user['full_name']) . '</td>';
        echo '<td>' . htmlspecialchars($user['email']) . '</td>';
        echo '<td>' . htmlspecialchars($user['role']) . '</td>';
        echo '<td>' . htmlspecialchars($user['status']) . '</td>';
        echo '<td>' . ($user['last_login'] ? htmlspecialchars(date('Y-m-d H:i:s', strtotime($user['last_login']))) : '') . '</td>';
        echo '<td>' . ($user['created_date'] ? htmlspecialchars(date('Y-m-d H:i:s', strtotime($user['created_date']))) : '') . '</td>';
        echo '</tr>';
    }

    echo '</table>';
    echo '</body>';
    echo '</html>';
}

// ฟังก์ชันส่งออก JSON
function exportJSON($users, $filename, $view = false) {
    // สร้าง metadata
    $metadata = [
        'export_date' => date('Y-m-d H:i:s'),
        'total_records' => count($users),
        'export_type' => 'users',
        'filters_applied' => []
    ];

    // สร้างข้อมูล JSON
    $jsonData = [
        'metadata' => $metadata,
        'data' => $users
    ];

    if ($view) {
        // แสดงผลใน browser
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    } else {
        // ดาวน์โหลดไฟล์
        header('Content-Type: application/json; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.json"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: 0');
        
        echo json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}
?>
