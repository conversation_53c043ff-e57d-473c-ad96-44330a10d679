<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>🔍 ทดสอบโครงสร้าง Export ข้อมูล</h2>";

try {
    // ตรวจสอบโครงสร้างตาราง assets
    echo "<h3>1. โครงสร้างตาราง assets ปัจจุบัน:</h3>";
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th>";
    echo "</tr>";
    
    $actualFields = [];
    foreach ($columns as $column) {
        $actualFields[] = $column['Field'];
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ตรวจสอบข้อมูล Assets ตัวอย่าง
    echo "<h3>2. ข้อมูล Assets ตัวอย่าง (3 รายการแรก):</h3>";
    $stmt = $pdo->query("SELECT * FROM assets LIMIT 3");
    $sampleAssets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($sampleAssets)) {
        echo "<p style='color: orange;'>⚠️ ไม่มี Assets ในระบบ</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; font-size: 0.9rem;'>";
        echo "<tr style='background: #f8f9fa;'>";
        foreach ($actualFields as $field) {
            echo "<th>" . htmlspecialchars($field) . "</th>";
        }
        echo "</tr>";
        
        foreach ($sampleAssets as $asset) {
            echo "<tr>";
            foreach ($actualFields as $field) {
                $value = $asset[$field] ?? '';
                if (strlen($value) > 20) {
                    $value = substr($value, 0, 20) . '...';
                }
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // ตรวจสอบฟิลด์ที่ Export ไฟล์ใช้
    echo "<h3>3. เปรียบเทียบฟิลด์ Export กับฐานข้อมูล:</h3>";
    
    // ฟิลด์ที่ Export ใช้ (หลังแก้ไข)
    $exportFields = [
        'id', 'asset_id', 'type', 'brand', 'model', 'tag', 'department', 'status',
        'hostname', 'operating_system', 'serial_number', 'warranty_expire', 
        'description', 'set_name', 'created_date', 'created_by', 'updated_date', 'updated_by'
    ];
    
    echo "<div style='display: flex; gap: 20px;'>";
    
    // ฟิลด์ในฐานข้อมูล
    echo "<div style='flex: 1;'>";
    echo "<h4>ฟิลด์ในฐานข้อมูล:</h4>";
    echo "<ul>";
    foreach ($actualFields as $field) {
        $inExport = in_array($field, $exportFields);
        $color = $inExport ? 'green' : 'orange';
        $status = $inExport ? '✅' : '⚠️';
        echo "<li style='color: {$color};'>{$status} {$field}</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    // ฟิลด์ที่ Export ใช้
    echo "<div style='flex: 1;'>";
    echo "<h4>ฟิลด์ที่ Export ใช้:</h4>";
    echo "<ul>";
    foreach ($exportFields as $field) {
        $inDB = in_array($field, $actualFields);
        $color = $inDB ? 'green' : 'red';
        $status = $inDB ? '✅' : '❌';
        echo "<li style='color: {$color};'>{$status} {$field}</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
    // ตรวจสอบฟิลด์ที่ขาดหายไป
    $missingInDB = array_diff($exportFields, $actualFields);
    $missingInExport = array_diff($actualFields, $exportFields);
    
    if (!empty($missingInDB)) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4 style='color: #721c24;'>❌ ฟิลด์ที่ Export ต้องการแต่ไม่มีในฐานข้อมูล:</h4>";
        echo "<ul>";
        foreach ($missingInDB as $field) {
            echo "<li style='color: #721c24;'>{$field}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($missingInExport)) {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4 style='color: #856404;'>⚠️ ฟิลด์ที่มีในฐานข้อมูลแต่ไม่ได้ Export:</h4>";
        echo "<ul>";
        foreach ($missingInExport as $field) {
            echo "<li style='color: #856404;'>{$field}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (empty($missingInDB) && empty($missingInExport)) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4 style='color: #155724;'>✅ ฟิลด์ Export ตรงกับฐานข้อมูลทั้งหมด!</h4>";
        echo "</div>";
    }
    
    // ทดสอบ Export
    echo "<h3>4. ทดสอบ Export:</h3>";
    
    if (!empty($sampleAssets)) {
        echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
        
        // ทดสอบ CSV
        echo "<a href='export_csv.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>";
        echo "<i class='fas fa-file-csv'></i> ทดสอบ Export CSV";
        echo "</a>";
        
        // ทดสอบ Excel
        echo "<a href='export_excel_simple.php' target='_blank' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>";
        echo "<i class='fas fa-file-excel'></i> ทดสอบ Export Excel";
        echo "</a>";
        
        // ทดสอบ JSON
        echo "<a href='export_json.php?format=view' target='_blank' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>";
        echo "<i class='fas fa-file-code'></i> ทดสอบ Export JSON";
        echo "</a>";
        
        echo "</div>";
        
        echo "<p style='margin-top: 15px;'><strong>หมายเหตุ:</strong> คลิกลิงก์ด้านบนเพื่อทดสอบการ Export ในรูปแบบต่างๆ</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ ไม่สามารถทดสอบ Export ได้เพราะไม่มีข้อมูล Assets</p>";
        echo "<p><a href='add_asset.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>➕ เพิ่ม Asset</a></p>";
    }
    
    // แสดงข้อมูลสถิติ
    echo "<h3>5. สถิติข้อมูล:</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM assets");
    $total = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as with_asset_id FROM assets WHERE asset_id IS NOT NULL AND asset_id != ''");
    $withAssetId = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as with_serial FROM assets WHERE serial_number IS NOT NULL AND serial_number != ''");
    $withSerial = $stmt->fetchColumn();
    
    echo "<div style='display: flex; gap: 20px; flex-wrap: wrap;'>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center;'>";
    echo "<h4 style='margin: 0; color: #007bff;'>{$total}</h4>";
    echo "<p style='margin: 5px 0 0 0;'>Total Assets</p>";
    echo "</div>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center;'>";
    echo "<h4 style='margin: 0; color: #28a745;'>{$withAssetId}</h4>";
    echo "<p style='margin: 5px 0 0 0;'>มี Asset ID</p>";
    echo "</div>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center;'>";
    echo "<h4 style='margin: 0; color: #17a2b8;'>{$withSerial}</h4>";
    echo "<p style='margin: 5px 0 0 0;'>มี Serial Number</p>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<hr>
<p>
    <a href="export_selector.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        📤 Export ข้อมูล
    </a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th { background: #f8f9fa; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
