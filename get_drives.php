<?php
// ดึงรายการ drives ที่มีในระบบ
header('Content-Type: application/json; charset=utf-8');

require_once 'includes/auth.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

try {
    $drives = [];
    
    // ตรวจสอบระบบปฏิบัติการ
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        // Windows System
        $drives = getWindowsDrives();
    } else {
        // Unix-like System (Linux, macOS)
        $drives = getUnixDrives();
    }
    
    echo json_encode([
        'success' => true,
        'drives' => $drives,
        'os' => PHP_OS
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
    ]);
}

/**
 * ดึงรายการ drives สำหรับ Windows
 */
function getWindowsDrives() {
    $drives = [];
    
    // ตรวจสอบ drives A-Z
    for ($letter = 'A'; $letter <= 'Z'; $letter++) {
        $drivePath = $letter . ':\\';
        
        // ตรวจสอบว่า drive มีอยู่จริง
        if (is_dir($drivePath)) {
            $driveInfo = getDriveInfo($drivePath, $letter);
            if ($driveInfo) {
                $drives[] = $driveInfo;
            }
        }
    }
    
    return $drives;
}

/**
 * ดึงรายการ mount points สำหรับ Unix-like systems
 */
function getUnixDrives() {
    $drives = [];
    
    // เพิ่ม root filesystem
    $rootInfo = getDriveInfo('/', 'Root');
    if ($rootInfo) {
        $drives[] = $rootInfo;
    }
    
    // ตรวจสอบ mount points อื่นๆ
    $commonMounts = [
        '/home' => 'Home',
        '/var' => 'Var',
        '/tmp' => 'Temp',
        '/opt' => 'Optional',
        '/usr' => 'User',
        '/media' => 'Media',
        '/mnt' => 'Mount'
    ];
    
    foreach ($commonMounts as $path => $name) {
        if (is_dir($path) && is_readable($path)) {
            // ตรวจสอบว่าเป็น mount point แยกต่างหากหรือไม่
            $stat1 = stat($path);
            $stat2 = stat(dirname($path));
            
            if ($stat1 && $stat2 && $stat1['dev'] !== $stat2['dev']) {
                $mountInfo = getDriveInfo($path, $name);
                if ($mountInfo) {
                    $drives[] = $mountInfo;
                }
            }
        }
    }
    
    // ตรวจสอบ removable media
    $mediaPath = '/media';
    if (is_dir($mediaPath)) {
        $mediaItems = scandir($mediaPath);
        foreach ($mediaItems as $item) {
            if ($item !== '.' && $item !== '..') {
                $itemPath = $mediaPath . '/' . $item;
                if (is_dir($itemPath)) {
                    $mediaInfo = getDriveInfo($itemPath, 'Media: ' . $item);
                    if ($mediaInfo) {
                        $drives[] = $mediaInfo;
                    }
                }
            }
        }
    }
    
    return $drives;
}

/**
 * ดึงข้อมูลของ drive/mount point
 */
function getDriveInfo($path, $name) {
    try {
        // ตรวจสอบว่าสามารถเข้าถึงได้
        if (!is_readable($path)) {
            return null;
        }
        
        // ดึงข้อมูลพื้นที่
        $totalBytes = disk_total_space($path);
        $freeBytes = disk_free_space($path);
        
        if ($totalBytes === false || $freeBytes === false) {
            return [
                'name' => $name,
                'path' => $path,
                'total_space' => 'Unknown',
                'free_space' => 'Unknown',
                'used_percent' => 0
            ];
        }
        
        $usedBytes = $totalBytes - $freeBytes;
        $usedPercent = $totalBytes > 0 ? round(($usedBytes / $totalBytes) * 100, 1) : 0;
        
        return [
            'name' => $name,
            'path' => $path,
            'total_space' => formatBytes($totalBytes),
            'free_space' => formatBytes($freeBytes),
            'used_space' => formatBytes($usedBytes),
            'used_percent' => $usedPercent,
            'total_bytes' => $totalBytes,
            'free_bytes' => $freeBytes
        ];
        
    } catch (Exception $e) {
        return null;
    }
}

/**
 * แปลงขนาดไฟล์เป็นรูปแบบที่อ่านง่าย
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB', 'PB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * ตรวจสอบประเภทของ drive (สำหรับ Windows)
 */
function getDriveType($drivePath) {
    if (strtoupper(substr(PHP_OS, 0, 3)) !== 'WIN') {
        return 'Unknown';
    }
    
    // ใช้ WMI เพื่อตรวจสอบประเภท drive (ถ้าเป็นไปได้)
    try {
        $wmi = new COM("winmgmts:");
        $drives = $wmi->ExecQuery("SELECT * FROM Win32_LogicalDisk WHERE DeviceID = '" . substr($drivePath, 0, 2) . "'");
        
        foreach ($drives as $drive) {
            switch ($drive->DriveType) {
                case 2: return 'Removable';
                case 3: return 'Fixed';
                case 4: return 'Network';
                case 5: return 'CD-ROM';
                case 6: return 'RAM';
                default: return 'Unknown';
            }
        }
    } catch (Exception $e) {
        // ถ้าไม่สามารถใช้ COM ได้
    }
    
    return 'Unknown';
}
?>
