<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

// ตรวจสอบ method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// ตรวจสอบ action
if (!isset($_POST['action']) || $_POST['action'] !== 'update_ip') {
    echo json_encode(['success' => false, 'error' => 'Invalid action']);
    exit;
}

// ฟังก์ชัน NSLOOKUP ที่ปรับปรุงแล้ว
function performNslookup($hostname) {
    $result = [
        'success' => false,
        'ip_address' => null,
        'method' => '',
        'error' => '',
        'execution_time' => 0
    ];
    
    $startTime = microtime(true);
    
    if (empty(trim($hostname))) {
        $result['error'] = 'Hostname is empty';
        return $result;
    }
    
    $hostname = trim($hostname);
    
    // Method 1: gethostbyname - IPv4 เท่านั้น
    $ip = gethostbyname($hostname);
    if ($ip !== $hostname && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
        $result['success'] = true;
        $result['ip_address'] = $ip;
        $result['method'] = 'gethostbyname';
        $result['execution_time'] = round((microtime(true) - $startTime) * 1000, 2);
        return $result;
    }
    
    // Method 2: dns_get_record - IPv4 เท่านั้น
    if (function_exists('dns_get_record')) {
        $records = @dns_get_record($hostname, DNS_A);
        if ($records && isset($records[0]['ip'])) {
            $ip = $records[0]['ip'];
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                $result['success'] = true;
                $result['ip_address'] = $ip;
                $result['method'] = 'dns_get_record';
                $result['execution_time'] = round((microtime(true) - $startTime) * 1000, 2);
                return $result;
            }
        }
    }
    
    // Method 3: nslookup command
    if (function_exists('exec')) {
        $output = [];
        $return_var = 0;
        
        $command = "nslookup " . escapeshellarg($hostname) . " 2>&1";
        exec($command, $output, $return_var);
        
        if ($return_var === 0 && !empty($output)) {
            foreach ($output as $i => $line) {
                $line = trim($line);
                
                // ข้าม DNS Server lines
                if (preg_match('/^Server:|^Address:.*#53/', $line)) {
                    continue;
                }
                
                // หา Address lines
                if (preg_match('/^Address(?:es)?:\s*(.+)/', $line, $matches)) {
                    $addressPart = trim($matches[1]);
                    if (preg_match('/(\d+\.\d+\.\d+\.\d+)/', $addressPart, $ipMatches)) {
                        $ip = $ipMatches[1];
                        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                            $result['success'] = true;
                            $result['ip_address'] = $ip;
                            $result['method'] = 'nslookup command';
                            $result['execution_time'] = round((microtime(true) - $startTime) * 1000, 2);
                            return $result;
                        }
                    }
                }
                
                // Windows format
                if (preg_match('/^Name:\s*' . preg_quote($hostname, '/') . '/i', $line)) {
                    if (isset($output[$i + 1])) {
                        $nextLine = trim($output[$i + 1]);
                        if (preg_match('/^Address(?:es)?:\s*(.+)/', $nextLine, $matches)) {
                            $addressPart = trim($matches[1]);
                            if (preg_match('/(\d+\.\d+\.\d+\.\d+)/', $addressPart, $ipMatches)) {
                                $ip = $ipMatches[1];
                                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                                    $result['success'] = true;
                                    $result['ip_address'] = $ip;
                                    $result['method'] = 'nslookup command (Windows)';
                                    $result['execution_time'] = round((microtime(true) - $startTime) * 1000, 2);
                                    return $result;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    // Method 4: dig command (Linux/Unix)
    if (function_exists('exec') && strtoupper(substr(PHP_OS, 0, 3)) !== 'WIN') {
        $output = [];
        $return_var = 0;
        
        $command = "dig +short " . escapeshellarg($hostname) . " A 2>&1";
        exec($command, $output, $return_var);
        
        if ($return_var === 0 && !empty($output)) {
            foreach ($output as $line) {
                $line = trim($line);
                if (filter_var($line, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                    $result['success'] = true;
                    $result['ip_address'] = $line;
                    $result['method'] = 'dig command';
                    $result['execution_time'] = round((microtime(true) - $startTime) * 1000, 2);
                    return $result;
                }
            }
        }
    }
    
    $result['error'] = 'Unable to resolve hostname to IPv4 address';
    $result['execution_time'] = round((microtime(true) - $startTime) * 1000, 2);
    return $result;
}

try {
    // รับข้อมูลจาก POST
    $assetId = $_POST['asset_id'] ?? '';
    $hostname = $_POST['hostname'] ?? '';
    
    // ตรวจสอบข้อมูล
    if (empty($assetId)) {
        echo json_encode(['success' => false, 'error' => 'Asset ID is required']);
        exit;
    }
    
    if (empty($hostname)) {
        echo json_encode(['success' => false, 'error' => 'Hostname is required']);
        exit;
    }
    
    // ตรวจสอบว่า Asset มีอยู่จริง
    $stmt = $pdo->prepare("SELECT id, asset_id FROM assets WHERE id = ?");
    $stmt->execute([$assetId]);
    $asset = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$asset) {
        echo json_encode(['success' => false, 'error' => 'Asset not found']);
        exit;
    }
    
    // ทำ NSLOOKUP
    $lookupResult = performNslookup($hostname);
    
    if ($lookupResult['success']) {
        // อัปเดต IP Address ในฐานข้อมูล
        $updateStmt = $pdo->prepare("UPDATE assets SET ip_address = ?, last_ip_check = NOW() WHERE id = ?");
        $updateStmt->execute([$lookupResult['ip_address'], $assetId]);
        
        // ส่งผลลัพธ์กลับ
        echo json_encode([
            'success' => true,
            'ip_address' => $lookupResult['ip_address'],
            'method' => $lookupResult['method'],
            'execution_time' => $lookupResult['execution_time'] . 'ms',
            'message' => 'IP Address updated successfully'
        ]);
        
    } else {
        // ส่งข้อผิดพลาดกลับ
        echo json_encode([
            'success' => false,
            'error' => $lookupResult['error'],
            'execution_time' => $lookupResult['execution_time'] . 'ms'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Error in update_asset_ip.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
