<?php
// ตรวจสอบและแก้ไขชื่อคอลัมน์ในฐานข้อมูล
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 ตรวจสอบและแก้ไขชื่อคอลัมน์ในฐานข้อมูล</h2>";

// เชื่อมต่อฐานข้อมูล
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', 'Wxmujwsofu@1234');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} catch (Exception $e) {
    die("<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูล: " . $e->getMessage() . "</p>");
}

// ตรวจสอบโครงสร้างตารางปัจจุบัน
echo "<h3>โครงสร้างตารางปัจจุบัน:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Field</th>";
    echo "<th style='padding: 10px;'>Type</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Key</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "</tr>";
    
    $existingColumns = [];
    foreach ($columns as $column) {
        $existingColumns[] = $column['Field'];
        echo "<tr>";
        echo "<td style='padding: 10px;'><strong>{$column['Field']}</strong></td>";
        echo "<td style='padding: 10px;'>{$column['Type']}</td>";
        echo "<td style='padding: 10px;'>{$column['Null']}</td>";
        echo "<td style='padding: 10px;'>{$column['Key']}</td>";
        echo "<td style='padding: 10px;'>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><strong>คอลัมน์ที่มีอยู่:</strong> " . implode(', ', $existingColumns) . "</p>";
    
    // ตรวจสอบคอลัมน์ที่ต้องการ
    $requiredColumns = ['created_by', 'updated_by', 'created_date', 'updated_date'];
    $oldColumns = ['person_added', 'person_modified', 'date_added', 'date_modified'];
    
    echo "<h4>การตรวจสอบคอลัมน์:</h4>";
    echo "<ul>";
    
    $needsUpdate = false;
    $columnMappings = [
        'person_added' => 'created_by',
        'person_modified' => 'updated_by', 
        'date_added' => 'created_date',
        'date_modified' => 'updated_date'
    ];
    
    foreach ($columnMappings as $oldName => $newName) {
        $hasOld = in_array($oldName, $existingColumns);
        $hasNew = in_array($newName, $existingColumns);
        
        if ($hasOld && !$hasNew) {
            echo "<li>⚠️ <strong>$oldName</strong> ต้องเปลี่ยนเป็น <strong>$newName</strong></li>";
            $needsUpdate = true;
        } elseif (!$hasOld && $hasNew) {
            echo "<li>✅ <strong>$newName</strong> มีอยู่แล้ว</li>";
        } elseif ($hasOld && $hasNew) {
            echo "<li>🔄 <strong>$oldName</strong> และ <strong>$newName</strong> มีทั้งคู่ (ควรลบ $oldName)</li>";
            $needsUpdate = true;
        } elseif (!$hasOld && !$hasNew) {
            echo "<li>❌ ไม่มี <strong>$oldName</strong> หรือ <strong>$newName</strong> (ต้องเพิ่ม $newName)</li>";
            $needsUpdate = true;
        }
    }
    echo "</ul>";
    
    if (!$needsUpdate) {
        echo "<p style='color: green;'><strong>✅ โครงสร้างตารางถูกต้องแล้ว</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ไม่สามารถดูโครงสร้างตาราง: " . $e->getMessage() . "</p>";
}

// แก้ไขโครงสร้างตาราง
if ($_POST && isset($_POST['fix_columns'])) {
    echo "<h3>🔧 กำลังแก้ไขโครงสร้างตาราง...</h3>";
    
    $columnMappings = [
        'person_added' => 'created_by',
        'person_modified' => 'updated_by',
        'date_added' => 'created_date', 
        'date_modified' => 'updated_date'
    ];
    
    foreach ($columnMappings as $oldName => $newName) {
        $hasOld = in_array($oldName, $existingColumns);
        $hasNew = in_array($newName, $existingColumns);
        
        if ($hasOld && !$hasNew) {
            try {
                // เปลี่ยนชื่อคอลัมน์
                if (strpos($newName, 'date') !== false) {
                    $sql = "ALTER TABLE assets CHANGE $oldName $newName DATETIME";
                    if ($newName == 'created_date') {
                        $sql .= " DEFAULT CURRENT_TIMESTAMP";
                    } elseif ($newName == 'updated_date') {
                        $sql .= " DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";
                    }
                } else {
                    $sql = "ALTER TABLE assets CHANGE $oldName $newName VARCHAR(100)";
                }
                
                $pdo->exec($sql);
                echo "<p style='color: green;'>✅ เปลี่ยนชื่อคอลัมน์ '$oldName' เป็น '$newName' สำเร็จ</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ ไม่สามารถเปลี่ยนชื่อคอลัมน์ '$oldName': " . $e->getMessage() . "</p>";
            }
        } elseif (!$hasOld && !$hasNew) {
            try {
                // เพิ่มคอลัมน์ใหม่
                if (strpos($newName, 'date') !== false) {
                    $sql = "ALTER TABLE assets ADD COLUMN $newName DATETIME";
                    if ($newName == 'created_date') {
                        $sql .= " DEFAULT CURRENT_TIMESTAMP";
                    } elseif ($newName == 'updated_date') {
                        $sql .= " DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";
                    }
                } else {
                    $sql = "ALTER TABLE assets ADD COLUMN $newName VARCHAR(100)";
                }
                
                $pdo->exec($sql);
                echo "<p style='color: green;'>✅ เพิ่มคอลัมน์ '$newName' สำเร็จ</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ ไม่สามารถเพิ่มคอลัมน์ '$newName': " . $e->getMessage() . "</p>";
            }
        } elseif ($hasOld && $hasNew) {
            try {
                // ลบคอลัมน์เก่า
                $sql = "ALTER TABLE assets DROP COLUMN $oldName";
                $pdo->exec($sql);
                echo "<p style='color: green;'>✅ ลบคอลัมน์เก่า '$oldName' สำเร็จ</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ ไม่สามารถลบคอลัมน์ '$oldName': " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<p style='color: green;'><strong>✅ แก้ไขโครงสร้างตารางเสร็จสิ้น</strong></p>";
    echo "<p><a href='verify_database_columns.php'>รีเฟรชหน้าเพื่อดูผลลัพธ์</a></p>";
}

// ทดสอบ INSERT
if ($_POST && isset($_POST['test_insert'])) {
    echo "<h3>🧪 ทดสอบ INSERT:</h3>";
    
    try {
        $sql = "INSERT INTO assets (type, brand, model, tag, department, status, hostname, operating_system, serial_number, asset_id, warranty_expire, description, set_name, created_by, updated_by, created_date, updated_date) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        echo "<h4>SQL ที่ใช้:</h4>";
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;'>";
        echo htmlspecialchars($sql);
        echo "</div>";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            'Desktop',
            'Dell', 
            'OptiPlex 7090',
            'TAG-TEST',
            'IT Department',
            'ใช้งาน',
            'PC-TEST',
            'Windows 10',
            'SN123456789',
            'ASSET-TEST',
            '2025-12-31',
            'Test Asset Description',
            'Test Set',
            'admin',
            'admin'
        ]);
        
        if ($result) {
            $newId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ INSERT สำเร็จ! ID: $newId</p>";
            
            // แสดงข้อมูลที่เพิ่ม
            $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
            $checkStmt->execute([$newId]);
            $newRecord = $checkStmt->fetch();
            
            echo "<h4>ข้อมูลที่เพิ่ม:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
            echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
            foreach ($newRecord as $key => $value) {
                if (!is_numeric($key)) {
                    echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
                }
            }
            echo "</table>";
            
            // ลบข้อมูลทดสอบ
            $deleteStmt = $pdo->prepare("DELETE FROM assets WHERE id = ?");
            $deleteStmt->execute([$newId]);
            echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
            
        } else {
            echo "<p style='color: red;'>❌ INSERT ไม่สำเร็จ</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ INSERT error: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>Error Code: " . $e->getCode() . "</p>";
    }
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ตรวจสอบและแก้ไขชื่อคอลัมน์</title>
    <style>
        body {
            font-family: 'TH Sarabun New', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .action-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover {
            background: #2980b9;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>

<div class="action-section">
    <h3>🔧 แก้ไขชื่อคอลัมน์</h3>
    <p>แก้ไขชื่อคอลัมน์ให้ตรงกับที่ต้องการ:</p>
    <ul>
        <li><strong>person_added</strong> → <strong>created_by</strong></li>
        <li><strong>person_modified</strong> → <strong>updated_by</strong></li>
        <li><strong>date_added</strong> → <strong>created_date</strong></li>
        <li><strong>date_modified</strong> → <strong>updated_date</strong></li>
    </ul>
    <form method="POST">
        <input type="hidden" name="fix_columns" value="1">
        <button type="submit">🔧 แก้ไขชื่อคอลัมน์</button>
    </form>
</div>

<div class="action-section">
    <h3>🧪 ทดสอบ INSERT</h3>
    <p>ทดสอบการเพิ่มข้อมูลหลังจากแก้ไขชื่อคอลัมน์แล้ว</p>
    <form method="POST">
        <input type="hidden" name="test_insert" value="1">
        <button type="submit">🧪 ทดสอบ INSERT</button>
    </form>
</div>

<div class="warning">
    <strong>⚠️ หมายเหตุ:</strong> การแก้ไขจะเปลี่ยนชื่อคอลัมน์ในตาราง assets ไม่มีการลบหรือแก้ไขข้อมูลที่มีอยู่
</div>

<div style="margin: 20px 0;">
    <a href="add_asset.php" style="background: #27ae60; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">➕ ไปหน้าเพิ่ม Asset</a>
    <a href="index.php" style="background: #95a5a6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 กลับหน้าหลัก</a>
</div>

</body>
</html>
