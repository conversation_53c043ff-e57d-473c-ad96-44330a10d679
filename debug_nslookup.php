<?php
require_once 'includes/auth.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>🔍 Debug NSLOOKUP แบบละเอียด</h2>";

// ฟังก์ชันทดสอบ NSLOOKUP แบบ debug
function debugNslookup($hostname) {
    echo "<div style='background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #007bff;'>";
    echo "<h3>🎯 Debug: " . htmlspecialchars($hostname) . "</h3>";
    
    // Method 1: gethostbyname
    echo "<h4>1. gethostbyname:</h4>";
    $start = microtime(true);
    $result1 = gethostbyname($hostname);
    $time1 = microtime(true) - $start;
    
    echo "<p><strong>Raw result:</strong> <code>" . htmlspecialchars($result1) . "</code></p>";
    echo "<p><strong>Time:</strong> " . number_format($time1 * 1000, 2) . " ms</p>";
    echo "<p><strong>Same as hostname:</strong> " . ($result1 === $hostname ? '❌ Not resolved' : '✅ Resolved') . "</p>";
    
    if ($result1 !== $hostname) {
        echo "<p><strong>Is valid IP:</strong> " . (filter_var($result1, FILTER_VALIDATE_IP) ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Is IPv4:</strong> " . (filter_var($result1, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Is IPv6:</strong> " . (filter_var($result1, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6) ? '✅ Yes' : '❌ No') . "</p>";
    }
    
    // Method 2: dns_get_record
    echo "<h4>2. dns_get_record:</h4>";
    if (function_exists('dns_get_record')) {
        $start = microtime(true);
        $records = @dns_get_record($hostname, DNS_A);
        $time2 = microtime(true) - $start;
        
        echo "<p><strong>Time:</strong> " . number_format($time2 * 1000, 2) . " ms</p>";
        echo "<p><strong>Records count:</strong> " . (is_array($records) ? count($records) : 0) . "</p>";
        
        if ($records) {
            echo "<p><strong>All A records:</strong></p>";
            echo "<ul>";
            foreach ($records as $i => $record) {
                $ip = $record['ip'] ?? 'N/A';
                $isIPv4 = filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4);
                $color = $isIPv4 ? 'green' : 'red';
                echo "<li style='color: {$color};'>[{$i}] " . htmlspecialchars($ip) . " " . ($isIPv4 ? '(IPv4 ✅)' : '(Not IPv4 ❌)') . "</li>";
            }
            echo "</ul>";
            
            echo "<details><summary>Raw records data:</summary>";
            echo "<pre>" . htmlspecialchars(print_r($records, true)) . "</pre>";
            echo "</details>";
        } else {
            echo "<p style='color: red;'>❌ No A records found</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ dns_get_record not available</p>";
    }
    
    // Method 3: nslookup command
    echo "<h4>3. nslookup command:</h4>";
    if (function_exists('exec')) {
        $start = microtime(true);
        $output = [];
        $return_var = 0;
        
        $command = "nslookup " . escapeshellarg($hostname) . " 2>&1";
        exec($command, $output, $return_var);
        $time3 = microtime(true) - $start;
        
        echo "<p><strong>Command:</strong> <code>" . htmlspecialchars($command) . "</code></p>";
        echo "<p><strong>Return code:</strong> " . $return_var . "</p>";
        echo "<p><strong>Time:</strong> " . number_format($time3 * 1000, 2) . " ms</p>";
        echo "<p><strong>Output lines:</strong> " . count($output) . "</p>";
        
        echo "<h5>Raw output:</h5>";
        echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;'>";
        foreach ($output as $i => $line) {
            echo sprintf("%2d: %s\n", $i + 1, htmlspecialchars($line));
        }
        echo "</pre>";
        
        // Parse output
        echo "<h5>Parsing analysis:</h5>";
        $foundIPs = [];
        $dnsServerLines = [];
        
        foreach ($output as $lineNum => $line) {
            $line = trim($line);
            $lineInfo = "Line " . ($lineNum + 1) . ": " . htmlspecialchars($line);
            
            // Check for DNS server info
            if (preg_match('/Server:|Address:.*#53/', $line)) {
                $dnsServerLines[] = $lineInfo . " <span style='color: orange;'>(DNS Server - SKIP)</span>";
                continue;
            }
            
            // Look for Address lines
            if (preg_match('/Address(?:es)?:\s*(.+)/', $line, $matches)) {
                $addressPart = trim($matches[1]);
                echo "<p><strong>Found Address line:</strong> " . htmlspecialchars($line) . "</p>";
                echo "<p><strong>Address part:</strong> <code>" . htmlspecialchars($addressPart) . "</code></p>";
                
                // Extract IP from address part
                if (preg_match('/(\d+\.\d+\.\d+\.\d+)/', $addressPart, $ipMatches)) {
                    $ip = $ipMatches[1];
                    $isIPv4 = filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4);
                    $color = $isIPv4 ? 'green' : 'red';
                    echo "<p style='color: {$color};'><strong>Extracted IP:</strong> {$ip} " . ($isIPv4 ? '(Valid IPv4 ✅)' : '(Invalid IPv4 ❌)') . "</p>";
                    
                    if ($isIPv4) {
                        $foundIPs[] = $ip;
                    }
                }
            }
            
            // Look for Name: hostname pattern (Windows)
            if (preg_match('/Name:\s*' . preg_quote($hostname, '/') . '/i', $line)) {
                echo "<p><strong>Found Name line:</strong> " . htmlspecialchars($line) . "</p>";
                
                // Check next line for Address
                if (isset($output[$lineNum + 1])) {
                    $nextLine = trim($output[$lineNum + 1]);
                    echo "<p><strong>Next line:</strong> " . htmlspecialchars($nextLine) . "</p>";
                    
                    if (preg_match('/Address(?:es)?:\s*(.+)/', $nextLine, $matches)) {
                        $addressPart = trim($matches[1]);
                        if (preg_match('/(\d+\.\d+\.\d+\.\d+)/', $addressPart, $ipMatches)) {
                            $ip = $ipMatches[1];
                            $isIPv4 = filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4);
                            $color = $isIPv4 ? 'green' : 'red';
                            echo "<p style='color: {$color};'><strong>Windows format IP:</strong> {$ip} " . ($isIPv4 ? '(Valid IPv4 ✅)' : '(Invalid IPv4 ❌)') . "</p>";
                            
                            if ($isIPv4) {
                                $foundIPs[] = $ip;
                            }
                        }
                    }
                }
            }
        }
        
        if (!empty($dnsServerLines)) {
            echo "<h5>DNS Server lines (skipped):</h5>";
            echo "<ul>";
            foreach ($dnsServerLines as $line) {
                echo "<li>{$line}</li>";
            }
            echo "</ul>";
        }
        
        if (!empty($foundIPs)) {
            echo "<h5 style='color: green;'>✅ Found IPv4 addresses:</h5>";
            echo "<ul>";
            foreach (array_unique($foundIPs) as $ip) {
                echo "<li style='color: green; font-weight: bold;'>{$ip}</li>";
            }
            echo "</ul>";
        } else {
            echo "<h5 style='color: red;'>❌ No IPv4 addresses found</h5>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ exec function not available</p>";
    }
    
    // Method 4: dig command (Linux/Unix)
    if (strtoupper(substr(PHP_OS, 0, 3)) !== 'WIN') {
        echo "<h4>4. dig command:</h4>";
        if (function_exists('exec')) {
            $start = microtime(true);
            $output = [];
            $return_var = 0;
            
            $command = "dig +short " . escapeshellarg($hostname) . " A 2>&1";
            exec($command, $output, $return_var);
            $time4 = microtime(true) - $start;
            
            echo "<p><strong>Command:</strong> <code>" . htmlspecialchars($command) . "</code></p>";
            echo "<p><strong>Return code:</strong> " . $return_var . "</p>";
            echo "<p><strong>Time:</strong> " . number_format($time4 * 1000, 2) . " ms</p>";
            echo "<p><strong>Output lines:</strong> " . count($output) . "</p>";
            
            if (!empty($output)) {
                echo "<h5>dig output analysis:</h5>";
                echo "<ul>";
                foreach ($output as $line) {
                    $line = trim($line);
                    $isIPv4 = filter_var($line, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4);
                    $color = $isIPv4 ? 'green' : 'orange';
                    $status = $isIPv4 ? '(Valid IPv4 ✅)' : '(Not IPv4 ⚠️)';
                    echo "<li style='color: {$color};'>" . htmlspecialchars($line) . " {$status}</li>";
                }
                echo "</ul>";
            } else {
                echo "<p style='color: red;'>❌ No output from dig</p>";
            }
        }
    }
    
    echo "</div>";
}

// ทดสอบ hostnames ต่างๆ
$testHostnames = [
    'google.com',
    'facebook.com',
    'github.com',
    'stackoverflow.com'
];

// ทดสอบ hostname ที่ผู้ใช้ระบุ
if (isset($_GET['hostname']) && !empty($_GET['hostname'])) {
    $userHostname = $_GET['hostname'];
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎯 ทดสอบ Hostname ที่คุณระบุ</h3>";
    debugNslookup($userHostname);
    echo "</div>";
}

echo "<h3>📋 ทดสอบ Hostnames ตัวอย่าง:</h3>";

foreach ($testHostnames as $hostname) {
    debugNslookup($hostname);
}

// ฟอร์มทดสอบ
echo "<h3>🧪 ทดสอบ Hostname กำหนดเอง:</h3>";
echo "<form method='GET' style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='hostname'><strong>Hostname:</strong></label><br>";
echo "<input type='text' id='hostname' name='hostname' placeholder='เช่น example.com' style='width: 300px; padding: 8px; margin-top: 5px;' value='" . htmlspecialchars($_GET['hostname'] ?? '') . "'>";
echo "</div>";
echo "<button type='submit' style='background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>🔍 Debug NSLOOKUP</button>";
echo "</form>";

// แสดงข้อมูลระบบ
echo "<h3>ℹ️ ข้อมูลระบบ:</h3>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>Operating System:</strong> " . PHP_OS . "</li>";
echo "<li><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</li>";
echo "<li><strong>gethostbyname:</strong> " . (function_exists('gethostbyname') ? '✅ Available' : '❌ Not Available') . "</li>";
echo "<li><strong>dns_get_record:</strong> " . (function_exists('dns_get_record') ? '✅ Available' : '❌ Not Available') . "</li>";
echo "<li><strong>exec:</strong> " . (function_exists('exec') ? '✅ Available' : '❌ Not Available') . "</li>";
echo "</ul>";
?>

<hr>
<p>
    <a href="test_ipv4_validation.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        🧪 ทดสอบ IPv4 Validation
    </a>
    <a href="simple_nslookup_test.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🔍 ทดสอบ NSLOOKUP
    </a>
    <a href="ip_lookup_manager.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🔧 จัดการ IP Lookup
    </a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4, h5 { color: #333; }
details { margin: 10px 0; }
summary { font-weight: bold; cursor: pointer; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
