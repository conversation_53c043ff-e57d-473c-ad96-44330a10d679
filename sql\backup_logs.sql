-- สร้างตาราง backup_logs สำหรับเก็บ log การ backup
CREATE TABLE IF NOT EXISTS `backup_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filename` varchar(255) NOT NULL COMMENT 'ชื่อไฟล์ backup',
  `action_type` varchar(50) NOT NULL COMMENT 'ประเภทการดำเนินการ (CREATE, UPDATE, DELETE, MANUAL)',
  `description` text COMMENT 'รายละเอียดการ backup',
  `file_size` bigint(20) DEFAULT NULL COMMENT 'ขนาดไฟล์ (bytes)',
  `created_date` datetime NOT NULL COMMENT 'วันที่สร้าง backup',
  `created_by` varchar(100) DEFAULT NULL COMMENT 'ผู้สร้าง backup',
  PRIMARY KEY (`id`),
  KEY `idx_created_date` (`created_date`),
  KEY `idx_action_type` (`action_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตาราง log การ backup ฐานข้อมูล';
