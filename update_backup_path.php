<?php
// ไฟล์สำหรับอัพเดท backup path ให้ใช้ path ที่เลือกจาก folder browser
require_once 'includes/auth.php';
require_once 'config/database.php';
require_once 'classes/BackupManager.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>อัพเดท Backup Path</h2>";

try {
    // สร้าง BackupManager instance
    $backupManager = new BackupManager($pdo);
    
    // ดึงการตั้งค่าปัจจุบัน
    $currentConfig = $backupManager->getConfig();
    
    echo "<h3>การตั้งค่าปัจจุบัน:</h3>";
    echo "<ul>";
    echo "<li><strong>Backup Path:</strong> " . htmlspecialchars($currentConfig['backup_path']) . "</li>";
    echo "<li><strong>เปิดใช้งาน:</strong> " . ($currentConfig['enabled'] ? 'ใช่' : 'ไม่') . "</li>";
    echo "</ul>";
    
    // ตัวอย่าง path ใหม่
    $newPaths = [
        'C:\\Backups\\',
        'D:\\Asset_Backups\\',
        '/var/backups/asset/',
        './custom_backups/',
        'backups/'
    ];
    
    echo "<h3>ทดสอบ Path ต่างๆ:</h3>";
    
    if (isset($_GET['test_path'])) {
        $testPath = $_GET['test_path'];
        echo "<h4>ทดสอบ Path: " . htmlspecialchars($testPath) . "</h4>";
        
        // อัพเดท config
        $newConfig = $currentConfig;
        $newConfig['backup_path'] = $testPath;
        
        $backupManager->updateConfig($newConfig);
        
        echo "<p style='color: green;'>✅ อัพเดท config เรียบร้อย</p>";
        
        // ทดสอบสร้าง backup
        $testDescription = "ทดสอบ backup ใน path ใหม่: {$testPath}";
        $result = $backupManager->createAutoBackup('TEST_PATH', $testDescription);
        
        if ($result) {
            echo "<p style='color: green;'>✅ สร้าง backup สำเร็จ: {$result}</p>";
            
            // ตรวจสอบไฟล์
            $cleanPath = rtrim($testPath, '/\\');
            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                $cleanPath .= '\\';
            } else {
                $cleanPath .= '/';
            }
            
            $filePath = $cleanPath . $result;
            if (file_exists($filePath)) {
                $fileSize = filesize($filePath);
                echo "<p><strong>ไฟล์ที่สร้าง:</strong> {$filePath}</p>";
                echo "<p><strong>ขนาด:</strong> " . formatFileSize($fileSize) . "</p>";
                echo "<p><strong>Real Path:</strong> " . realpath($filePath) . "</p>";
            } else {
                echo "<p style='color: red;'>❌ ไม่พบไฟล์ที่สร้าง: {$filePath}</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ ไม่สามารถสร้าง backup ได้</p>";
        }
        
        // แสดง config ใหม่
        $updatedConfig = $backupManager->getConfig();
        echo "<h4>Config หลังอัพเดท:</h4>";
        echo "<pre>" . json_encode($updatedConfig, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    }
    
    echo "<h3>ทดสอบ Path ต่างๆ:</h3>";
    echo "<ul>";
    foreach ($newPaths as $path) {
        $isCurrentPath = ($path === $currentConfig['backup_path']);
        $style = $isCurrentPath ? 'font-weight: bold; color: blue;' : '';
        
        echo "<li style='{$style}'>";
        echo "<a href='?test_path=" . urlencode($path) . "'>";
        echo htmlspecialchars($path);
        echo "</a>";
        if ($isCurrentPath) {
            echo " (ปัจจุบัน)";
        }
        echo "</li>";
    }
    echo "</ul>";
    
    // ฟอร์มสำหรับใส่ path เอง
    echo "<h3>ทดสอบ Path ที่กำหนดเอง:</h3>";
    echo "<form method='GET'>";
    echo "<input type='text' name='test_path' placeholder='เช่น C:\\MyBackups\\' style='width: 300px; padding: 5px;'>";
    echo "<button type='submit' style='padding: 5px 10px; margin-left: 10px;'>ทดสอบ</button>";
    echo "</form>";
    
    // ตรวจสอบโฟลเดอร์ที่มีอยู่
    echo "<h3>ตรวจสอบโฟลเดอร์ Backup ที่มีอยู่:</h3>";
    $possiblePaths = ['backups/', 'C:\\Backups\\', 'D:\\Backups\\', '/var/backups/', './custom_backups/'];
    
    foreach ($possiblePaths as $path) {
        $cleanPath = rtrim($path, '/\\');
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            $cleanPath .= '\\';
        } else {
            $cleanPath .= '/';
        }
        
        echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd;'>";
        echo "<strong>Path:</strong> " . htmlspecialchars($cleanPath) . "<br>";
        echo "<strong>มีอยู่:</strong> " . (is_dir($cleanPath) ? '✅ ใช่' : '❌ ไม่') . "<br>";
        
        if (is_dir($cleanPath)) {
            echo "<strong>เขียนได้:</strong> " . (is_writable($cleanPath) ? '✅ ใช่' : '❌ ไม่') . "<br>";
            
            $backupFiles = glob($cleanPath . 'asset_backup_*.sql');
            echo "<strong>ไฟล์ Backup:</strong> " . count($backupFiles) . " ไฟล์<br>";
            
            if (!empty($backupFiles)) {
                usort($backupFiles, function($a, $b) {
                    return filemtime($b) - filemtime($a);
                });
                $latestFile = basename($backupFiles[0]);
                $fileTime = date('Y-m-d H:i:s', filemtime($backupFiles[0]));
                echo "<strong>ไฟล์ล่าสุด:</strong> {$latestFile} ({$fileTime})<br>";
            }
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "<strong>เกิดข้อผิดพลาด:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

// ฟังก์ชันช่วย
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}
?>

<hr>
<p>
    <a href="backup_settings.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        ⚙️ ตั้งค่า Backup
    </a>
    <a href="test_backup.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🧪 ทดสอบ Backup
    </a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
