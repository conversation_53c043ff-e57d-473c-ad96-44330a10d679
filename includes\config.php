<?php
// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'asset_management');
define('DB_USER', 'root');
define('DB_PASS', 'Wxmujwsofu@1234');
define('DB_CHARSET', 'utf8mb4');

// Application Configuration
define('APP_NAME', 'Asset Management System');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/asset');

// Security Configuration
define('SESSION_TIMEOUT', 3600); // 1 hour in seconds
define('PASSWORD_MIN_LENGTH', 6);

// File Upload Configuration
define('UPLOAD_MAX_SIZE', 5242880); // 5MB in bytes
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']);

// Backup Configuration
define('BACKUP_PATH', 'backups/');
define('BACKUP_MAX_FILES', 30);

// Email Configuration (Default values - can be overridden in backup settings)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_SECURE', 'tls');

// Timezone
date_default_timezone_set('Asia/Bangkok');

// Error Reporting (for development)
if (defined('ENVIRONMENT') && ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}
?>
