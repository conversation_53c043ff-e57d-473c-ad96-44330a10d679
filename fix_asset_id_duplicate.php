<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>🔧 แก้ไข Asset ID ให้สามารถซ้ำกันได้</h2>";

try {
    // ตรวจสอบโครงสร้างตารางปัจจุบัน
    echo "<h3>1. ตรวจสอบโครงสร้างตารางปัจจุบัน:</h3>";
    $stmt = $pdo->query("SHOW CREATE TABLE assets");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 0.8rem; overflow-x: auto;'>";
    echo htmlspecialchars($result['Create Table']);
    echo "</pre>";
    
    // ตรวจสอบ indexes
    echo "<h3>2. ตรวจสอบ Indexes ปัจจุบัน:</h3>";
    $stmt = $pdo->query("SHOW INDEX FROM assets");
    $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Key Name</th><th>Column Name</th><th>Non Unique</th><th>Index Type</th>";
    echo "</tr>";
    
    $hasAssetIdUnique = false;
    $hasAssetIdIndex = false;
    
    foreach ($indexes as $index) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($index['Key_name']) . "</td>";
        echo "<td>" . htmlspecialchars($index['Column_name']) . "</td>";
        echo "<td>" . ($index['Non_unique'] ? 'Yes' : 'No') . "</td>";
        echo "<td>" . htmlspecialchars($index['Index_type']) . "</td>";
        echo "</tr>";
        
        if ($index['Column_name'] === 'asset_id') {
            if ($index['Non_unique'] == 0) {
                $hasAssetIdUnique = true;
            } else {
                $hasAssetIdIndex = true;
            }
        }
    }
    echo "</table>";
    
    // แสดงสถานะ
    echo "<h3>3. สถานะ Asset ID:</h3>";
    if ($hasAssetIdUnique) {
        echo "<p style='color: red;'>❌ Asset ID มี UNIQUE constraint (ไม่สามารถซ้ำได้)</p>";
    } else {
        echo "<p style='color: green;'>✅ Asset ID ไม่มี UNIQUE constraint (สามารถซ้ำได้)</p>";
    }
    
    if ($hasAssetIdIndex) {
        echo "<p style='color: blue;'>ℹ️ Asset ID มี Index ธรรมดา (ดีสำหรับการค้นหา)</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Asset ID ไม่มี Index (ควรเพิ่มเพื่อประสิทธิภาพ)</p>";
    }
    
    // ดำเนินการแก้ไข
    if (isset($_POST['fix_asset_id'])) {
        echo "<h3>4. กำลังดำเนินการแก้ไข:</h3>";
        
        // ลบ UNIQUE constraint ถ้ามี
        if ($hasAssetIdUnique) {
            try {
                // หา constraint name
                $stmt = $pdo->query("
                    SELECT CONSTRAINT_NAME 
                    FROM information_schema.TABLE_CONSTRAINTS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'assets' 
                    AND CONSTRAINT_TYPE = 'UNIQUE'
                    AND CONSTRAINT_NAME LIKE '%asset_id%'
                ");
                $constraints = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                foreach ($constraints as $constraintName) {
                    $pdo->exec("ALTER TABLE assets DROP INDEX `{$constraintName}`");
                    echo "<p style='color: green;'>✅ ลบ UNIQUE constraint '{$constraintName}' สำเร็จ</p>";
                }
                
                // ลองลบ index ที่อาจมีชื่อต่างๆ
                $possibleIndexNames = ['asset_id', 'idx_asset_id', 'assets_asset_id_unique'];
                foreach ($possibleIndexNames as $indexName) {
                    try {
                        $pdo->exec("ALTER TABLE assets DROP INDEX `{$indexName}`");
                        echo "<p style='color: green;'>✅ ลบ index '{$indexName}' สำเร็จ</p>";
                    } catch (PDOException $e) {
                        if (strpos($e->getMessage(), "check that column/key exists") === false) {
                            echo "<p style='color: orange;'>⚠️ ไม่สามารถลบ index '{$indexName}': " . $e->getMessage() . "</p>";
                        }
                    }
                }
                
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ เกิดข้อผิดพลาดในการลบ UNIQUE constraint: " . $e->getMessage() . "</p>";
            }
        }
        
        // เพิ่ม index ธรรมดาถ้ายังไม่มี
        if (!$hasAssetIdIndex) {
            try {
                $pdo->exec("ALTER TABLE assets ADD INDEX idx_asset_id (asset_id)");
                echo "<p style='color: green;'>✅ เพิ่ม index ธรรมดาสำหรับ asset_id สำเร็จ</p>";
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), "Duplicate key name") !== false) {
                    echo "<p style='color: orange;'>⚠️ Index มีอยู่แล้ว</p>";
                } else {
                    echo "<p style='color: red;'>❌ เกิดข้อผิดพลาดในการเพิ่ม index: " . $e->getMessage() . "</p>";
                }
            }
        }
        
        echo "<p style='color: blue;'>🔄 กำลังรีเฟรชหน้า...</p>";
        echo "<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>";
    }
    
    // ทดสอบการเพิ่ม Asset ID ซ้ำ
    if (isset($_POST['test_duplicate'])) {
        echo "<h3>5. ทดสอบการเพิ่ม Asset ID ซ้ำ:</h3>";
        
        $testAssetId = 'TEST-DUPLICATE-' . date('His');
        
        try {
            // เพิ่มรายการแรก
            $stmt = $pdo->prepare("INSERT INTO assets (type, asset_id, created_by, updated_by) VALUES (?, ?, ?, ?)");
            $stmt->execute(['Test Type 1', $testAssetId, 'system', 'system']);
            $firstId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ เพิ่ม Asset ID: {$testAssetId} (ครั้งที่ 1) สำเร็จ - ID: {$firstId}</p>";
            
            // เพิ่มรายการที่สอง (Asset ID ซ้ำ)
            $stmt->execute(['Test Type 2', $testAssetId, 'system', 'system']);
            $secondId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ เพิ่ม Asset ID: {$testAssetId} (ครั้งที่ 2) สำเร็จ - ID: {$secondId}</p>";
            echo "<p style='color: green;'><strong>🎉 Asset ID สามารถซ้ำกันได้แล้ว!</strong></p>";
            
            // แสดงข้อมูลที่เพิ่ม
            $stmt = $pdo->prepare("SELECT id, type, asset_id FROM assets WHERE asset_id = ? ORDER BY id");
            $stmt->execute([$testAssetId]);
            $testAssets = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h4>ข้อมูลที่เพิ่ม:</h4>";
            echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
            echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Type</th><th>Asset ID</th></tr>";
            foreach ($testAssets as $asset) {
                echo "<tr>";
                echo "<td>" . $asset['id'] . "</td>";
                echo "<td>" . htmlspecialchars($asset['type']) . "</td>";
                echo "<td>" . htmlspecialchars($asset['asset_id']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // ลบข้อมูลทดสอบ
            $pdo->prepare("DELETE FROM assets WHERE asset_id = ?")->execute([$testAssetId]);
            echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ เกิดข้อผิดพลาดในการทดสอบ: " . $e->getMessage() . "</p>";
            if (strpos($e->getMessage(), "Duplicate entry") !== false) {
                echo "<p style='color: red;'>💡 ยังคงมี UNIQUE constraint อยู่ กรุณาคลิก 'แก้ไข Asset ID' ก่อน</p>";
            }
        }
    }
    
    // ตรวจสอบข้อมูล Asset ID ที่มีอยู่
    echo "<h3>6. ข้อมูล Asset ID ที่มีอยู่:</h3>";
    $stmt = $pdo->query("
        SELECT asset_id, COUNT(*) as count 
        FROM assets 
        WHERE asset_id IS NOT NULL AND asset_id != '' 
        GROUP BY asset_id 
        ORDER BY count DESC, asset_id 
        LIMIT 10
    ");
    $assetIds = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($assetIds)) {
        echo "<p>ไม่มี Asset ID ในระบบ</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr style='background: #f8f9fa;'><th>Asset ID</th><th>จำนวน</th><th>สถานะ</th></tr>";
        foreach ($assetIds as $asset) {
            $status = $asset['count'] > 1 ? 
                "<span style='color: orange;'>ซ้ำ ({$asset['count']} รายการ)</span>" : 
                "<span style='color: green;'>ไม่ซ้ำ</span>";
            echo "<tr>";
            echo "<td>" . htmlspecialchars($asset['asset_id']) . "</td>";
            echo "<td>" . $asset['count'] . "</td>";
            echo "<td>" . $status . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<hr>
<form method="POST" style="margin: 20px 0;">
    <button type="submit" name="fix_asset_id" value="1" 
            style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        🔧 แก้ไข Asset ID ให้ซ้ำได้
    </button>
    <button type="submit" name="test_duplicate" value="1" 
            style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
        🧪 ทดสอบ Asset ID ซ้ำ
    </button>
</form>

<p>
    <a href="add_asset.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        ➕ ทดสอบเพิ่ม Asset
    </a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th { background: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
button:hover { opacity: 0.8; }
</style>
