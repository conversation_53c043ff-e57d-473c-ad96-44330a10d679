<?php
// ตรวจสอบความถูกต้องของ path
header('Content-Type: application/json; charset=utf-8');

require_once 'includes/auth.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

// รับข้อมูล JSON
$input = json_decode(file_get_contents('php://input'), true);
$path = $input['path'] ?? '';

try {
    // ตรวจสอบว่ามี path หรือไม่
    if (empty($path)) {
        echo json_encode([
            'valid' => false,
            'message' => 'กรุณาระบุ path'
        ]);
        exit;
    }
    
    // ทำความสะอาด path
    $cleanPath = trim($path);
    
    // ตรวจสอบว่า path มีอยู่จริงหรือไม่
    if (!file_exists($cleanPath)) {
        // ถ้าไม่มี ลองสร้าง
        if (!mkdir($cleanPath, 0755, true)) {
            echo json_encode([
                'valid' => false,
                'message' => 'โฟลเดอร์ไม่มีอยู่และไม่สามารถสร้างได้'
            ]);
            exit;
        }
    }
    
    // ตรวจสอบว่าเป็นโฟลเดอร์หรือไม่
    if (!is_dir($cleanPath)) {
        echo json_encode([
            'valid' => false,
            'message' => 'Path ที่ระบุไม่ใช่โฟลเดอร์'
        ]);
        exit;
    }
    
    // ตรวจสอบสิทธิ์การเขียน
    if (!is_writable($cleanPath)) {
        echo json_encode([
            'valid' => false,
            'message' => 'ไม่สามารถเขียนในโฟลเดอร์นี้ได้ กรุณาตรวจสอบสิทธิ์'
        ]);
        exit;
    }
    
    // ตรวจสอบพื้นที่ว่าง
    $freeBytes = disk_free_space($cleanPath);
    $totalBytes = disk_total_space($cleanPath);
    
    if ($freeBytes === false || $totalBytes === false) {
        echo json_encode([
            'valid' => true,
            'message' => 'โฟลเดอร์ถูกต้องและสามารถเขียนได้ (ไม่สามารถตรวจสอบพื้นที่ว่าง)'
        ]);
        exit;
    }
    
    $freeGB = round($freeBytes / (1024 * 1024 * 1024), 2);
    $totalGB = round($totalBytes / (1024 * 1024 * 1024), 2);
    $usedPercent = round((($totalBytes - $freeBytes) / $totalBytes) * 100, 1);
    
    // เตือนถ้าพื้นที่ว่างน้อยกว่า 1GB หรือใช้ไปแล้วมากกว่า 90%
    if ($freeGB < 1 || $usedPercent > 90) {
        echo json_encode([
            'valid' => true,
            'message' => "โฟลเดอร์ถูกต้องแต่พื้นที่ว่างเหลือน้อย (เหลือ {$freeGB} GB จาก {$totalGB} GB)",
            'warning' => true,
            'disk_info' => [
                'free_gb' => $freeGB,
                'total_gb' => $totalGB,
                'used_percent' => $usedPercent
            ]
        ]);
    } else {
        echo json_encode([
            'valid' => true,
            'message' => "โฟลเดอร์ถูกต้องและสามารถเขียนได้ (พื้นที่ว่าง {$freeGB} GB จาก {$totalGB} GB)",
            'disk_info' => [
                'free_gb' => $freeGB,
                'total_gb' => $totalGB,
                'used_percent' => $usedPercent
            ]
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'valid' => false,
        'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
    ]);
}
?>
