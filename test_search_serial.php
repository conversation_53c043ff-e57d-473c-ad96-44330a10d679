<?php
require_once 'includes/auth.php';
require_once 'config/database.php';
require_once 'classes/AssetManager.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>🔍 ทดสอบการค้นหา Serial Number</h2>";

$assetManager = new AssetManager($pdo);

try {
    // ตรวจสอบข้อมูล Serial Number ที่มีอยู่
    echo "<h3>1. ข้อมูล Serial Number ที่มีอยู่:</h3>";
    $stmt = $pdo->query("
        SELECT id, asset_id, type, brand, model, serial_number 
        FROM assets 
        WHERE serial_number IS NOT NULL AND serial_number != '' 
        ORDER BY serial_number 
        LIMIT 10
    ");
    $assetsWithSerial = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($assetsWithSerial)) {
        echo "<p style='color: orange;'>⚠️ ไม่มี Assets ที่มี Serial Number ในระบบ</p>";
        
        // เพิ่มข้อมูลทดสอบ
        echo "<h4>เพิ่มข้อมูลทดสอบ:</h4>";
        $testData = [
            ['type' => 'Desktop', 'brand' => 'Dell', 'model' => 'OptiPlex 7090', 'serial_number' => 'DL123456789', 'asset_id' => 'TEST-001'],
            ['type' => 'Laptop', 'brand' => 'HP', 'model' => 'EliteBook 840', 'serial_number' => 'HP987654321', 'asset_id' => 'TEST-002'],
            ['type' => 'Monitor', 'brand' => 'Samsung', 'model' => '24" LED', 'serial_number' => 'SM456789123', 'asset_id' => 'TEST-003']
        ];
        
        foreach ($testData as $data) {
            $data['created_by'] = getCurrentUserFullName() ?? 'Test System';
            $data['updated_by'] = $data['created_by'];
            
            $newId = $assetManager->createAsset($data);
            if ($newId) {
                echo "<p style='color: green;'>✅ เพิ่ม Asset: {$data['type']} {$data['brand']} (Serial: {$data['serial_number']}) - ID: {$newId}</p>";
            }
        }
        
        // ดึงข้อมูลใหม่
        $stmt = $pdo->query("
            SELECT id, asset_id, type, brand, model, serial_number 
            FROM assets 
            WHERE serial_number IS NOT NULL AND serial_number != '' 
            ORDER BY serial_number 
            LIMIT 10
        ");
        $assetsWithSerial = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    if (!empty($assetsWithSerial)) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>Asset ID</th><th>Type</th><th>Brand</th><th>Model</th><th>Serial Number</th>";
        echo "</tr>";
        
        foreach ($assetsWithSerial as $asset) {
            echo "<tr>";
            echo "<td>" . $asset['id'] . "</td>";
            echo "<td>" . htmlspecialchars($asset['asset_id']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['type']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['brand']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['model']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($asset['serial_number']) . "</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // ทดสอบการค้นหา Serial Number
    if (!empty($assetsWithSerial)) {
        echo "<h3>2. ทดสอบการค้นหา Serial Number:</h3>";
        
        $testSerial = $assetsWithSerial[0]['serial_number'];
        $partialSerial = substr($testSerial, 0, 5); // ใช้ 5 ตัวแรก
        
        echo "<h4>ทดสอบค้นหาด้วย Serial Number เต็ม: <code>{$testSerial}</code></h4>";
        $searchResults1 = $assetManager->getAllAssets($testSerial);
        
        if (!empty($searchResults1)) {
            echo "<p style='color: green;'>✅ พบ " . count($searchResults1) . " รายการ</p>";
            echo "<ul>";
            foreach ($searchResults1 as $result) {
                echo "<li>ID: {$result['id']} - {$result['type']} {$result['brand']} (Serial: {$result['serial_number']})</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ ไม่พบผลลัพธ์</p>";
        }
        
        echo "<h4>ทดสอบค้นหาด้วย Serial Number บางส่วน: <code>{$partialSerial}</code></h4>";
        $searchResults2 = $assetManager->getAllAssets($partialSerial);
        
        if (!empty($searchResults2)) {
            echo "<p style='color: green;'>✅ พบ " . count($searchResults2) . " รายการ</p>";
            echo "<ul>";
            foreach ($searchResults2 as $result) {
                echo "<li>ID: {$result['id']} - {$result['type']} {$result['brand']} (Serial: {$result['serial_number']})</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ ไม่พบผลลัพธ์</p>";
        }
        
        // ทดสอบการค้นหาแบบ case-insensitive
        $lowerSerial = strtolower($partialSerial);
        echo "<h4>ทดสอบค้นหาแบบ case-insensitive: <code>{$lowerSerial}</code></h4>";
        $searchResults3 = $assetManager->getAllAssets($lowerSerial);
        
        if (!empty($searchResults3)) {
            echo "<p style='color: green;'>✅ พบ " . count($searchResults3) . " รายการ (case-insensitive ทำงาน)</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ ไม่พบผลลัพธ์ (อาจเป็นเพราะ case-sensitive)</p>";
        }
    }
    
    // ทดสอบการค้นหาผ่าน URL (จำลอง)
    echo "<h3>3. ทดสอบการค้นหาผ่าน URL:</h3>";
    
    if (!empty($assetsWithSerial)) {
        $testSerial = $assetsWithSerial[0]['serial_number'];
        $searchUrl = "index.php?search=" . urlencode($testSerial);
        
        echo "<p>URL ทดสอบ: <a href='{$searchUrl}' target='_blank'>{$searchUrl}</a></p>";
        echo "<p>คลิกลิงก์ด้านบนเพื่อทดสอบการค้นหาใน index.php</p>";
    }
    
    // แสดงข้อมูลการค้นหาที่รองรับ
    echo "<h3>4. ฟิลด์ที่รองรับการค้นหา:</h3>";
    echo "<p>ระบบสามารถค้นหาจากฟิลด์เหล่านี้:</p>";
    echo "<ul>";
    echo "<li><strong>type</strong> - ประเภท Asset</li>";
    echo "<li><strong>brand</strong> - ยี่ห้อ</li>";
    echo "<li><strong>model</strong> - รุ่น</li>";
    echo "<li><strong>tag</strong> - แท็ก</li>";
    echo "<li><strong>hostname</strong> - ชื่อเครื่อง</li>";
    echo "<li><strong>department</strong> - แผนก</li>";
    echo "<li><strong>asset_id</strong> - รหัส Asset</li>";
    echo "<li><strong>serial_number</strong> - หมายเลขเครื่อง ✅</li>";
    echo "</ul>";
    
    // แสดงตัวอย่างการค้นหา
    echo "<h3>5. ตัวอย่างการค้นหา Serial Number:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h4>วิธีการค้นหา:</h4>";
    echo "<ol>";
    echo "<li>ไปที่หน้า <a href='index.php' target='_blank'>index.php</a></li>";
    echo "<li>ใส่ Serial Number ในช่องค้นหา</li>";
    echo "<li>กดปุ่ม 'ค้นหา' หรือกด Enter</li>";
    echo "</ol>";
    
    echo "<h4>รูปแบบที่รองรับ:</h4>";
    echo "<ul>";
    echo "<li>Serial Number เต็ม: <code>DL123456789</code></li>";
    echo "<li>Serial Number บางส่วน: <code>DL123</code></li>";
    echo "<li>ตัวเลขบางส่วน: <code>123456</code></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<hr>
<p>
    <a href="index.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        🔍 ทดสอบค้นหาใน Index
    </a>
    <a href="add_asset.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        ➕ เพิ่ม Asset
    </a>
    <a href="fix_asset_id_duplicate.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🔧 แก้ไข Asset ID
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th { background: #f8f9fa; }
code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
ul, ol { margin: 10px 0; }
li { margin: 5px 0; }
</style>
