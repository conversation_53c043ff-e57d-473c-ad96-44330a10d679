@echo off
title Asset Management System - Python GUI
echo.
echo ========================================
echo  Asset Management System - Python GUI
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher
    pause
    exit /b 1
)

REM Run the application
echo Starting Asset Management GUI...
echo.
python run_app.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo Application exited with an error.
    pause
)
