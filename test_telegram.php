<?php
// ไฟล์ทดสอบระบบแจ้งเตือน Telegram
require_once 'includes/auth.php';
require_once 'classes/TelegramNotifier.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>ทดสอบระบบแจ้งเตือน Telegram</h2>";

try {
    // สร้าง TelegramNotifier instance
    $telegramNotifier = new TelegramNotifier();
    
    // ดึงการตั้งค่าปัจจุบัน
    $config = $telegramNotifier->getConfig();
    
    echo "<h3>การตั้งค่าปัจจุบัน:</h3>";
    echo "<ul>";
    echo "<li><strong>เปิดใช้งาน:</strong> " . ($config['enabled'] ? 'ใช่' : 'ไม่') . "</li>";
    echo "<li><strong>Bot Token:</strong> " . (empty($config['bot_token']) ? 'ไม่ได้ตั้งค่า' : substr($config['bot_token'], 0, 10) . '...') . "</li>";
    echo "<li><strong>Chat IDs:</strong> " . (empty($config['chat_ids']) ? 'ไม่ได้ตั้งค่า' : implode(', ', $config['chat_ids'])) . "</li>";
    echo "</ul>";
    
    if (!$config['enabled']) {
        echo "<div style='color: orange; padding: 10px; border: 1px solid orange; background: #fff8e1;'>";
        echo "<strong>⚠️ การแจ้งเตือน Telegram ปิดใช้งาน</strong><br>";
        echo "กรุณาเปิดใช้งานในหน้าตั้งค่า Telegram";
        echo "</div>";
    } elseif (empty($config['bot_token']) || empty($config['chat_ids'])) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
        echo "<strong>❌ การตั้งค่าไม่ครบถ้วน</strong><br>";
        echo "กรุณาตั้งค่า Bot Token และ Chat IDs";
        echo "</div>";
    } else {
        echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
        echo "<strong>✅ การตั้งค่าครบถ้วน</strong>";
        echo "</div>";
    }
    
    // ทดสอบการส่งข้อความต่างๆ
    if (isset($_GET['test']) && $config['enabled'] && !empty($config['bot_token']) && !empty($config['chat_ids'])) {
        $testType = $_GET['test'];
        
        echo "<h3>กำลังทดสอบการส่งข้อความ...</h3>";
        
        switch ($testType) {
            case 'basic':
                echo "<h4>ทดสอบข้อความพื้นฐาน</h4>";
                $result = $telegramNotifier->testMessage();
                break;
                
            case 'asset_create':
                echo "<h4>ทดสอบแจ้งเตือนเพิ่ม Asset</h4>";
                $assetData = [
                    'type' => 'Desktop',
                    'brand' => 'Dell',
                    'model' => 'OptiPlex 7090',
                    'tag' => 'IT-001',
                    'department' => 'IT',
                    'status' => 'ใช้งาน'
                ];
                $result = $telegramNotifier->notifyAssetCreated($assetData, 'Admin (ทดสอบ)');
                break;
                
            case 'asset_update':
                echo "<h4>ทดสอบแจ้งเตือนแก้ไข Asset</h4>";
                $assetData = [
                    'type' => 'Laptop',
                    'brand' => 'HP',
                    'model' => 'EliteBook 840',
                    'tag' => 'IT-002',
                    'department' => 'HR',
                    'status' => 'ชำรุด'
                ];
                $changes = [
                    ['field_name' => 'status', 'old_value' => 'ใช้งาน', 'new_value' => 'ชำรุด'],
                    ['field_name' => 'department', 'old_value' => 'IT', 'new_value' => 'HR']
                ];
                $result = $telegramNotifier->notifyAssetUpdated(123, $assetData, 'Admin (ทดสอบ)', $changes);
                break;
                
            case 'asset_delete':
                echo "<h4>ทดสอบแจ้งเตือนลบ Asset</h4>";
                $assetData = [
                    'type' => 'Monitor',
                    'brand' => 'Samsung',
                    'model' => '24" LED',
                    'tag' => 'IT-003',
                    'department' => 'Finance',
                    'status' => 'สำรอง'
                ];
                $result = $telegramNotifier->notifyAssetDeleted($assetData, 'Admin (ทดสอบ)');
                break;
                
            case 'backup_success':
                echo "<h4>ทดสอบแจ้งเตือน Backup สำเร็จ</h4>";
                $filename = 'asset_backup_2024-01-15_14-30-25_TEST.sql';
                $action = 'TEST';
                $description = 'ทดสอบระบบ Auto Backup';
                $fileSize = 2560000; // 2.5 MB
                $result = $telegramNotifier->notifyBackupSuccess($filename, $action, $description, $fileSize);
                break;
                
            case 'backup_error':
                echo "<h4>ทดสอบแจ้งเตือน Backup ล้มเหลว</h4>";
                $action = 'TEST';
                $error = 'ไม่สามารถเขียนไฟล์ได้ เนื่องจากพื้นที่ดิสก์เต็ม';
                $result = $telegramNotifier->notifyBackupError($action, $error);
                break;
                
            default:
                echo "<p style='color: red;'>ประเภทการทดสอบไม่ถูกต้อง</p>";
                $result = false;
        }
        
        if ($result && !empty(array_filter($result))) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
            echo "<strong>✅ ส่งข้อความสำเร็จ!</strong><br>";
            echo "ผลลัพธ์: " . json_encode($result);
            echo "</div>";
        } else {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
            echo "<strong>❌ ไม่สามารถส่งข้อความได้</strong><br>";
            echo "ผลลัพธ์: " . json_encode($result);
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "<strong>เกิดข้อผิดพลาด:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}
?>

<h3>ทดสอบการส่งข้อความ:</h3>
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; margin: 20px 0;">
    <a href="?test=basic" style="background: #007bff; color: white; padding: 15px; text-decoration: none; border-radius: 5px; text-align: center;">
        🤖 ข้อความทดสอบพื้นฐาน
    </a>
    <a href="?test=asset_create" style="background: #28a745; color: white; padding: 15px; text-decoration: none; border-radius: 5px; text-align: center;">
        🆕 เพิ่ม Asset
    </a>
    <a href="?test=asset_update" style="background: #ffc107; color: black; padding: 15px; text-decoration: none; border-radius: 5px; text-align: center;">
        ✏️ แก้ไข Asset
    </a>
    <a href="?test=asset_delete" style="background: #dc3545; color: white; padding: 15px; text-decoration: none; border-radius: 5px; text-align: center;">
        🗑️ ลบ Asset
    </a>
    <a href="?test=backup_success" style="background: #17a2b8; color: white; padding: 15px; text-decoration: none; border-radius: 5px; text-align: center;">
        💾 Backup สำเร็จ
    </a>
    <a href="?test=backup_error" style="background: #6c757d; color: white; padding: 15px; text-decoration: none; border-radius: 5px; text-align: center;">
        ❌ Backup ล้มเหลว
    </a>
</div>

<hr>
<p>
    <a href="telegram_settings.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        ⚙️ ตั้งค่า Telegram
    </a>
    <a href="backup_settings.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🗄️ ตั้งค่า Backup
    </a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
.grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
</style>
