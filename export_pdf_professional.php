<?php
// รายงาน PDF แบบเป็นทางการและเรียบง่าย
error_reporting(E_ALL);
ini_set('display_errors', 1);

// เชื่อมต่อฐานข้อมูล
$host = 'localhost';
$username = 'root';
$password = 'Wxmujwsofu@1234';
$database = 'asset_management';

$conn = new mysqli($host, $username, $password, $database);
$conn->set_charset("utf8mb4");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

require_once 'vendor/autoload.php';

// รับพารามิเตอร์
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';
$filter_serial = $_GET['filter_serial'] ?? '';
$isDownload = isset($_GET['download']) && $_GET['download'] === 'true';

// สร้าง TCPDF object
$pdf = new TCPDF('L', PDF_UNIT, 'A4', true, 'UTF-8', false);

// ตั้งค่าข้อมูลเอกสาร
$pdf->SetCreator('Asset Management System');
$pdf->SetAuthor('Asset Management System');
$pdf->SetTitle('Asset Management Report');
$pdf->SetSubject('Asset Report');

// ตั้งค่าหน้า
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
$pdf->SetMargins(20, 25, 20);
$pdf->SetHeaderMargin(10);
$pdf->SetFooterMargin(15);
$pdf->SetAutoPageBreak(TRUE, 20);

// ตั้งค่าฟอนต์
$pdf->SetFont('helvetica', '', 10);

// เพิ่มหน้าแรก
$pdf->AddPage();

// สร้าง SQL query
$sql = "SELECT * FROM assets WHERE 1=1";
$params = [];
$conditions = [];

// เพิ่มเงื่อนไขการกรอง
if (!empty($search)) {
    $conditions[] = "(type LIKE ? OR brand LIKE ? OR model LIKE ? OR tag LIKE ? OR hostname LIKE ? OR department LIKE ? OR asset_id LIKE ? OR serial_number LIKE ?)";
    $searchTerm = '%' . $search . '%';
    for ($i = 0; $i < 8; $i++) {
        $params[] = $searchTerm;
    }
}

if (!empty($filter_type)) {
    $conditions[] = "type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_brand)) {
    $conditions[] = "brand = ?";
    $params[] = $filter_brand;
}

if (!empty($filter_department)) {
    $conditions[] = "department = ?";
    $params[] = $filter_department;
}

if (!empty($filter_status)) {
    $conditions[] = "status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_os)) {
    $conditions[] = "operating_system = ?";
    $params[] = $filter_os;
}

if (!empty($filter_serial)) {
    $conditions[] = "serial_number LIKE ?";
    $params[] = '%' . $filter_serial . '%';
}

// รวมเงื่อนไข
if (!empty($conditions)) {
    $sql .= " AND " . implode(" AND ", $conditions);
}

$sql .= " ORDER BY created_date DESC";

// สร้างชื่อรายงาน
$title = "Asset Management Report";
$filterInfo = [];
if (!empty($search)) $filterInfo[] = "Search: $search";
if (!empty($filter_type)) $filterInfo[] = "Type: $filter_type";
if (!empty($filter_brand)) $filterInfo[] = "Brand: $filter_brand";
if (!empty($filter_department)) $filterInfo[] = "Department: $filter_department";
if (!empty($filter_status)) $filterInfo[] = "Status: $filter_status";
if (!empty($filter_os)) $filterInfo[] = "OS: $filter_os";
if (!empty($filter_serial)) $filterInfo[] = "Serial: $filter_serial";

// วันที่รายงาน
$reportDate = date('F d, Y H:i:s');

// === HEADER SECTION ===
// Logo/Company Name Area
$pdf->SetFont('helvetica', 'B', 20);
$pdf->SetTextColor(44, 62, 80); // Dark blue-gray
$pdf->Cell(0, 12, 'ASSET MANAGEMENT SYSTEM', 0, 1, 'C');

$pdf->SetFont('helvetica', '', 10);
$pdf->SetTextColor(127, 140, 141); // Light gray
$pdf->Cell(0, 6, 'Professional Asset Tracking & Management', 0, 1, 'C');

// Divider line
$pdf->Ln(5);
$pdf->SetDrawColor(189, 195, 199); // Light gray line
$pdf->Line(20, $pdf->GetY(), 277, $pdf->GetY());
$pdf->Ln(8);

// Report Title Section
$pdf->SetFont('helvetica', 'B', 16);
$pdf->SetTextColor(52, 73, 94); // Dark blue
$pdf->Cell(0, 10, $title, 0, 1, 'C');

// Report Info
$pdf->SetFont('helvetica', '', 9);
$pdf->SetTextColor(127, 140, 141);
$pdf->Cell(0, 5, 'Generated on: ' . $reportDate, 0, 1, 'C');

if (!empty($filterInfo)) {
    $pdf->Cell(0, 5, 'Filters: ' . implode(", ", $filterInfo), 0, 1, 'C');
}

$pdf->Ln(10);

// === TABLE HEADER ===
$pdf->SetFillColor(52, 73, 94); // Dark blue background
$pdf->SetTextColor(255, 255, 255); // White text
$pdf->SetDrawColor(52, 73, 94); // Dark blue border

// กำหนดความกว้างของคอลัมน์
$colWidths = [15, 35, 30, 30, 30, 30, 35, 25, 27];

// หัวตาราง
$headers = ['No.', 'Department', 'Type', 'Asset ID', 'Tag', 'Brand/Model', 'Serial Number', 'Status', 'Created'];

$pdf->SetFont('helvetica', 'B', 9);
foreach ($headers as $i => $header) {
    $pdf->Cell($colWidths[$i], 8, $header, 1, 0, 'C', true);
}
$pdf->Ln();

// === TABLE DATA ===
$pdf->SetTextColor(44, 62, 80); // Dark text
$pdf->SetFont('helvetica', '', 8);

// เตรียม prepared statement
$stmt = $conn->prepare($sql);

// Bind parameters ถ้ามี
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();

$sequenceNumber = 1;
$fill = false;
$rowsPerPage = 0;
$maxRowsPerPage = 28; // เพิ่มจำนวนแถวต่อหน้า

while ($row = $result->fetch_assoc()) {
    // ตรวจสอบว่าต้องขึ้นหน้าใหม่หรือไม่
    if ($rowsPerPage >= $maxRowsPerPage || $pdf->GetY() > 180) {
        $pdf->AddPage();
        
        // สร้างหัวตารางใหม่
        $pdf->SetFillColor(52, 73, 94);
        $pdf->SetTextColor(255, 255, 255);
        $pdf->SetFont('helvetica', 'B', 9);
        foreach ($headers as $i => $header) {
            $pdf->Cell($colWidths[$i], 8, $header, 1, 0, 'C', true);
        }
        $pdf->Ln();
        
        $pdf->SetTextColor(44, 62, 80);
        $pdf->SetFont('helvetica', '', 8);
        $rowsPerPage = 0;
    }

    // สลับสีแถว
    if ($fill) {
        $pdf->SetFillColor(248, 249, 250); // Very light gray
    } else {
        $pdf->SetFillColor(255, 255, 255); // White
    }

    // ข้อมูลแถว
    $brandModel = trim(($row['brand'] ?? '') . ' ' . ($row['model'] ?? ''));
    $createdDate = !empty($row['created_date']) ? date('d/m/Y', strtotime($row['created_date'])) : '';
    
    $rowData = [
        $sequenceNumber,
        substr($row['department'] ?? '', 0, 20),
        $row['type'] ?? '',
        $row['asset_id'] ?? '',
        $row['tag'] ?? '',
        substr($brandModel, 0, 25),
        substr($row['serial_number'] ?? '', 0, 20),
        $row['status'] ?? '',
        $createdDate
    ];

    foreach ($rowData as $i => $data) {
        $align = ($i == 0) ? 'C' : 'L';
        $pdf->Cell($colWidths[$i], 6, $data, 1, 0, $align, $fill);
    }
    $pdf->Ln();

    $sequenceNumber++;
    $rowsPerPage++;
    $fill = !$fill;
}

// === SUMMARY SECTION ===
$totalRecords = $result->num_rows;
$pdf->Ln(10);

// Summary Box
$pdf->SetFillColor(236, 240, 241); // Light gray background
$pdf->SetTextColor(44, 62, 80);
$pdf->SetDrawColor(189, 195, 199);

$pdf->SetFont('helvetica', 'B', 12);
$pdf->Cell(0, 8, 'REPORT SUMMARY', 1, 1, 'C', true);

$pdf->SetFont('helvetica', '', 10);
$pdf->SetFillColor(255, 255, 255);

$summaryData = [
    ['Total Records:', $totalRecords . ' items'],
    ['Report Date:', $reportDate],
    ['Generated By:', 'Asset Management System']
];

if (!empty($filterInfo)) {
    $summaryData[] = ['Applied Filters:', implode(", ", $filterInfo)];
}

foreach ($summaryData as $row) {
    $pdf->Cell(60, 6, $row[0], 1, 0, 'L', true);
    $pdf->Cell(0, 6, $row[1], 1, 1, 'L', true);
}

// === FOOTER ===
$pdf->Ln(10);
$pdf->SetFont('helvetica', 'I', 8);
$pdf->SetTextColor(127, 140, 141);
$pdf->Cell(0, 5, 'This report is generated automatically by Asset Management System', 0, 1, 'C');
$pdf->Cell(0, 5, 'For internal use only - Confidential', 0, 1, 'C');

// สร้างชื่อไฟล์
$filename = "asset_report_professional_" . date('Y-m-d_H-i-s');
if (!empty($filter_type)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_type);
if (!empty($filter_department)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_department);

// ส่งออก PDF
if ($isDownload) {
    $pdf->Output($filename . '.pdf', 'D');
} else {
    $pdf->Output($filename . '.pdf', 'I');
}

// ปิดการเชื่อมต่อฐานข้อมูล
$stmt->close();
$conn->close();
?>
