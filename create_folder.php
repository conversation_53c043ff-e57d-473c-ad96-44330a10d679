<?php
// สร้างโฟลเดอร์ใหม่
header('Content-Type: application/json; charset=utf-8');

require_once 'includes/auth.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

// รับข้อมูล JSON
$input = json_decode(file_get_contents('php://input'), true);
$parentPath = $input['path'] ?? './';
$folderName = $input['name'] ?? '';

try {
    // ตรวจสอบข้อมูลที่ส่งมา
    if (empty($folderName)) {
        echo json_encode([
            'success' => false,
            'message' => 'กรุณาระบุชื่อโฟลเดอร์'
        ]);
        exit;
    }
    
    // ตรวจสอบชื่อโฟลเดอร์
    if (!preg_match('/^[a-zA-Z0-9_\-\s]+$/', $folderName)) {
        echo json_encode([
            'success' => false,
            'message' => 'ชื่อโฟลเดอร์ต้องประกอบด้วยตัวอักษร ตัวเลข เครื่องหมาย _ - และช่องว่างเท่านั้น'
        ]);
        exit;
    }
    
    // ทำความสะอาด parent path
    $parentPath = realpath($parentPath);
    if ($parentPath === false) {
        echo json_encode([
            'success' => false,
            'message' => 'โฟลเดอร์หลักไม่มีอยู่จริง'
        ]);
        exit;
    }
    
    // ตรวจสอบว่า parent path เป็นโฟลเดอร์และสามารถเขียนได้
    if (!is_dir($parentPath) || !is_writable($parentPath)) {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่สามารถเขียนในโฟลเดอร์หลักได้'
        ]);
        exit;
    }
    
    // สร้าง path ของโฟลเดอร์ใหม่
    $newFolderPath = $parentPath . DIRECTORY_SEPARATOR . $folderName;
    
    // ตรวจสอบว่าโฟลเดอร์มีอยู่แล้วหรือไม่
    if (file_exists($newFolderPath)) {
        echo json_encode([
            'success' => false,
            'message' => 'โฟลเดอร์นี้มีอยู่แล้ว'
        ]);
        exit;
    }
    
    // สร้างโฟลเดอร์
    if (mkdir($newFolderPath, 0755)) {
        echo json_encode([
            'success' => true,
            'message' => 'สร้างโฟลเดอร์สำเร็จ',
            'folder_path' => $newFolderPath
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่สามารถสร้างโฟลเดอร์ได้'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
    ]);
}
?>
