<?php
/**
 * TelegramNotifier Class
 * จัดการการส่งแจ้งเตือนผ่าน Telegram Bot
 */

class TelegramNotifier {
    private $botToken;
    private $chatIds;
    private $enabled;
    
    public function __construct() {
        $this->loadConfig();
    }
    
    /**
     * โหลดการตั้งค่า Telegram
     */
    private function loadConfig() {
        $configFile = 'config/telegram_config.json';
        
        if (file_exists($configFile)) {
            $config = json_decode(file_get_contents($configFile), true);
            $this->botToken = $config['bot_token'] ?? '';
            $this->chatIds = $config['chat_ids'] ?? [];
            $this->enabled = $config['enabled'] ?? false;
        } else {
            // ค่าเริ่มต้น
            $this->botToken = '';
            $this->chatIds = [];
            $this->enabled = false;
            $this->saveConfig();
        }
    }
    
    /**
     * บันทึกการตั้งค่า
     */
    public function saveConfig() {
        $config = [
            'enabled' => $this->enabled,
            'bot_token' => $this->botToken,
            'chat_ids' => $this->chatIds
        ];
        
        $configFile = 'config/telegram_config.json';
        $configDir = dirname($configFile);
        
        if (!is_dir($configDir)) {
            mkdir($configDir, 0755, true);
        }
        
        file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
    
    /**
     * อัปเดตการตั้งค่า
     */
    public function updateConfig($newConfig) {
        $this->enabled = $newConfig['enabled'] ?? false;
        $this->botToken = $newConfig['bot_token'] ?? '';
        $this->chatIds = $newConfig['chat_ids'] ?? [];
        $this->saveConfig();
    }
    
    /**
     * ดึงการตั้งค่า
     */
    public function getConfig() {
        return [
            'enabled' => $this->enabled,
            'bot_token' => $this->botToken,
            'chat_ids' => $this->chatIds
        ];
    }
    
    /**
     * ส่งข้อความผ่าน Telegram
     */
    public function sendMessage($message, $parseMode = 'HTML') {
        if (!$this->enabled || empty($this->botToken) || empty($this->chatIds)) {
            return false;
        }
        
        $results = [];
        
        foreach ($this->chatIds as $chatId) {
            $result = $this->sendToChat($chatId, $message, $parseMode);
            $results[] = $result;
        }
        
        return $results;
    }
    
    /**
     * ส่งข้อความไปยัง Chat ID เฉพาะ
     */
    private function sendToChat($chatId, $message, $parseMode = 'HTML') {
        $url = "https://api.telegram.org/bot{$this->botToken}/sendMessage";
        
        $data = [
            'chat_id' => $chatId,
            'text' => $message,
            'parse_mode' => $parseMode,
            'disable_web_page_preview' => true
        ];
        
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                $result = json_decode($response, true);
                return $result['ok'] ?? false;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Telegram Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * แจ้งเตือนการเพิ่ม Asset
     */
    public function notifyAssetCreated($assetData, $createdBy) {
        $message = "🆕 <b>เพิ่ม Asset ใหม่</b>\n\n";
        $message .= "📋 <b>รายละเอียด:</b>\n";
        $message .= "• <b>Type:</b> {$assetData['type']}\n";
        $message .= "• <b>Brand:</b> {$assetData['brand']}\n";
        $message .= "• <b>Model:</b> {$assetData['model']}\n";
        
        if (!empty($assetData['tag'])) {
            $message .= "• <b>Tag:</b> {$assetData['tag']}\n";
        }
        
        if (!empty($assetData['department'])) {
            $message .= "• <b>Department:</b> {$assetData['department']}\n";
        }
        
        $message .= "• <b>Status:</b> {$assetData['status']}\n";
        $message .= "\n👤 <b>ผู้เพิ่ม:</b> {$createdBy}";
        $message .= "\n🕐 <b>เวลา:</b> " . date('Y-m-d H:i:s');
        
        return $this->sendMessage($message);
    }
    
    /**
     * แจ้งเตือนการแก้ไข Asset
     */
    public function notifyAssetUpdated($assetId, $assetData, $updatedBy, $changes = []) {
        $message = "✏️ <b>แก้ไข Asset</b>\n\n";
        $message .= "🆔 <b>Asset ID:</b> {$assetId}\n";
        $message .= "📋 <b>รายละเอียด:</b>\n";
        $message .= "• <b>Type:</b> {$assetData['type']}\n";
        $message .= "• <b>Brand:</b> {$assetData['brand']}\n";
        $message .= "• <b>Model:</b> {$assetData['model']}\n";
        
        if (!empty($changes)) {
            $message .= "\n🔄 <b>การเปลี่ยนแปลง:</b>\n";
            foreach ($changes as $change) {
                $fieldName = $change['field_name'];
                $oldValue = $change['old_value'] ?: 'ไม่มี';
                $newValue = $change['new_value'] ?: 'ไม่มี';
                $message .= "• <b>{$fieldName}:</b> {$oldValue} → {$newValue}\n";
            }
        }
        
        $message .= "\n👤 <b>ผู้แก้ไข:</b> {$updatedBy}";
        $message .= "\n🕐 <b>เวลา:</b> " . date('Y-m-d H:i:s');
        
        return $this->sendMessage($message);
    }
    
    /**
     * แจ้งเตือนการลบ Asset
     */
    public function notifyAssetDeleted($assetData, $deletedBy) {
        $message = "🗑️ <b>ลบ Asset</b>\n\n";
        $message .= "📋 <b>รายละเอียดที่ลบ:</b>\n";
        $message .= "• <b>Type:</b> {$assetData['type']}\n";
        $message .= "• <b>Brand:</b> {$assetData['brand']}\n";
        $message .= "• <b>Model:</b> {$assetData['model']}\n";
        
        if (!empty($assetData['tag'])) {
            $message .= "• <b>Tag:</b> {$assetData['tag']}\n";
        }
        
        if (!empty($assetData['department'])) {
            $message .= "• <b>Department:</b> {$assetData['department']}\n";
        }
        
        $message .= "\n👤 <b>ผู้ลบ:</b> {$deletedBy}";
        $message .= "\n🕐 <b>เวลา:</b> " . date('Y-m-d H:i:s');
        $message .= "\n\n⚠️ <i>การดำเนินการนี้ไม่สามารถยกเลิกได้</i>";
        
        return $this->sendMessage($message);
    }
    
    /**
     * แจ้งเตือนการ Backup สำเร็จ
     */
    public function notifyBackupSuccess($filename, $action, $description, $fileSize) {
        $message = "💾 <b>Auto Backup สำเร็จ</b>\n\n";
        $message .= "📁 <b>ไฟล์:</b> <code>{$filename}</code>\n";
        $message .= "🔧 <b>การดำเนินการ:</b> {$action}\n";
        $message .= "📝 <b>รายละเอียด:</b> {$description}\n";
        $message .= "📊 <b>ขนาดไฟล์:</b> " . $this->formatFileSize($fileSize) . "\n";
        $message .= "🕐 <b>เวลา:</b> " . date('Y-m-d H:i:s');
        
        return $this->sendMessage($message);
    }
    
    /**
     * แจ้งเตือนข้อผิดพลาดของ Backup
     */
    public function notifyBackupError($action, $error) {
        $message = "❌ <b>Auto Backup ล้มเหลว</b>\n\n";
        $message .= "🔧 <b>การดำเนินการ:</b> {$action}\n";
        $message .= "⚠️ <b>ข้อผิดพลาด:</b> {$error}\n";
        $message .= "🕐 <b>เวลา:</b> " . date('Y-m-d H:i:s');
        $message .= "\n\n🔍 <i>กรุณาตรวจสอบระบบ Backup</i>";
        
        return $this->sendMessage($message);
    }
    
    /**
     * ทดสอบการส่งข้อความ
     */
    public function testMessage() {
        $message = "🤖 <b>ทดสอบ Telegram Bot</b>\n\n";
        $message .= "✅ การเชื่อมต่อ Telegram Bot ทำงานปกติ\n";
        $message .= "📱 ระบบแจ้งเตือน Asset Management พร้อมใช้งาน\n";
        $message .= "🕐 <b>เวลาทดสอบ:</b> " . date('Y-m-d H:i:s');
        
        return $this->sendMessage($message);
    }
    
    /**
     * ตรวจสอบ Bot Token
     */
    public function validateBotToken($token) {
        $url = "https://api.telegram.org/bot{$token}/getMe";
        
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                $result = json_decode($response, true);
                return $result['ok'] ?? false;
            }
            
            return false;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * แปลงขนาดไฟล์
     */
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
?>
