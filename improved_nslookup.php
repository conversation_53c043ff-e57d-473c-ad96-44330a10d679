<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>🔧 Improved NSLOOKUP System</h2>";

// ฟังก์ชัน NSLOOKUP ที่ปรับปรุงแล้ว
function improvedNslookup($hostname) {
    $result = [
        'success' => false,
        'ip_address' => null,
        'method' => '',
        'error' => '',
        'execution_time' => 0,
        'all_ips' => [],
        'debug_info' => []
    ];
    
    $startTime = microtime(true);
    
    if (empty(trim($hostname))) {
        $result['error'] = 'Hostname is empty';
        return $result;
    }
    
    $hostname = trim($hostname);
    $result['debug_info'][] = "Target hostname: $hostname";
    
    // Method 1: gethostbyname (ได้ IPv4 เท่านั้น)
    $result['debug_info'][] = "=== Method 1: gethostbyname ===";
    $ip = gethostbyname($hostname);
    $result['debug_info'][] = "gethostbyname result: $ip";
    
    if ($ip !== $hostname) {
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            $result['success'] = true;
            $result['ip_address'] = $ip;
            $result['method'] = 'gethostbyname';
            $result['all_ips'][] = $ip;
            $result['execution_time'] = microtime(true) - $startTime;
            $result['debug_info'][] = "SUCCESS: Found IPv4 via gethostbyname: $ip";
            return $result;
        } else {
            $result['debug_info'][] = "gethostbyname returned non-IPv4: $ip";
        }
    } else {
        $result['debug_info'][] = "gethostbyname failed to resolve";
    }
    
    // Method 2: dns_get_record
    if (function_exists('dns_get_record')) {
        $result['debug_info'][] = "=== Method 2: dns_get_record ===";
        $records = @dns_get_record($hostname, DNS_A);
        
        if ($records && is_array($records)) {
            $result['debug_info'][] = "Found " . count($records) . " A records";
            
            foreach ($records as $i => $record) {
                if (isset($record['ip'])) {
                    $ip = $record['ip'];
                    $result['debug_info'][] = "A record [$i]: $ip";
                    
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                        $result['all_ips'][] = $ip;
                        
                        if (!$result['success']) {
                            $result['success'] = true;
                            $result['ip_address'] = $ip;
                            $result['method'] = 'dns_get_record';
                            $result['execution_time'] = microtime(true) - $startTime;
                            $result['debug_info'][] = "SUCCESS: Found IPv4 via dns_get_record: $ip";
                        }
                    } else {
                        $result['debug_info'][] = "A record [$i] is not IPv4: $ip";
                    }
                }
            }
            
            if ($result['success']) {
                return $result;
            }
        } else {
            $result['debug_info'][] = "No A records found via dns_get_record";
        }
    } else {
        $result['debug_info'][] = "dns_get_record function not available";
    }
    
    // Method 3: nslookup command
    if (function_exists('exec')) {
        $result['debug_info'][] = "=== Method 3: nslookup command ===";
        $output = [];
        $return_var = 0;
        
        $command = "nslookup " . escapeshellarg($hostname) . " 2>&1";
        exec($command, $output, $return_var);
        
        $result['debug_info'][] = "Command: $command";
        $result['debug_info'][] = "Return code: $return_var";
        $result['debug_info'][] = "Output lines: " . count($output);
        
        if ($return_var === 0 && !empty($output)) {
            $skipNext = false;
            
            for ($i = 0; $i < count($output); $i++) {
                $line = trim($output[$i]);
                $result['debug_info'][] = "Line $i: $line";
                
                // ข้าม DNS Server information
                if (preg_match('/^Server:|^Address:.*#53/', $line)) {
                    $result['debug_info'][] = "  -> Skipping DNS server line";
                    continue;
                }
                
                // หา Address: lines
                if (preg_match('/^Address(?:es)?:\s*(.+)/', $line, $matches)) {
                    $addressPart = trim($matches[1]);
                    $result['debug_info'][] = "  -> Found Address line: $addressPart";
                    
                    // Extract IPv4 from address part
                    if (preg_match('/(\d+\.\d+\.\d+\.\d+)/', $addressPart, $ipMatches)) {
                        $ip = $ipMatches[1];
                        $result['debug_info'][] = "  -> Extracted IP: $ip";
                        
                        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                            $result['all_ips'][] = $ip;
                            
                            if (!$result['success']) {
                                $result['success'] = true;
                                $result['ip_address'] = $ip;
                                $result['method'] = 'nslookup command';
                                $result['execution_time'] = microtime(true) - $startTime;
                                $result['debug_info'][] = "SUCCESS: Found IPv4 via nslookup: $ip";
                            }
                        } else {
                            $result['debug_info'][] = "  -> Not a valid IPv4: $ip";
                        }
                    }
                }
                
                // Windows format: Name: hostname
                if (preg_match('/^Name:\s*' . preg_quote($hostname, '/') . '/i', $line)) {
                    $result['debug_info'][] = "  -> Found Name line for hostname";
                    
                    // Check next line for Address
                    if (isset($output[$i + 1])) {
                        $nextLine = trim($output[$i + 1]);
                        $result['debug_info'][] = "  -> Next line: $nextLine";
                        
                        if (preg_match('/^Address(?:es)?:\s*(.+)/', $nextLine, $matches)) {
                            $addressPart = trim($matches[1]);
                            
                            if (preg_match('/(\d+\.\d+\.\d+\.\d+)/', $addressPart, $ipMatches)) {
                                $ip = $ipMatches[1];
                                $result['debug_info'][] = "  -> Windows format IP: $ip";
                                
                                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                                    $result['all_ips'][] = $ip;
                                    
                                    if (!$result['success']) {
                                        $result['success'] = true;
                                        $result['ip_address'] = $ip;
                                        $result['method'] = 'nslookup command (Windows)';
                                        $result['execution_time'] = microtime(true) - $startTime;
                                        $result['debug_info'][] = "SUCCESS: Found IPv4 via nslookup Windows format: $ip";
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            if ($result['success']) {
                return $result;
            }
        } else {
            $result['debug_info'][] = "nslookup command failed or no output";
        }
    } else {
        $result['debug_info'][] = "exec function not available";
    }
    
    // Method 4: dig command (Linux/Unix only)
    if (function_exists('exec') && strtoupper(substr(PHP_OS, 0, 3)) !== 'WIN') {
        $result['debug_info'][] = "=== Method 4: dig command ===";
        $output = [];
        $return_var = 0;
        
        $command = "dig +short " . escapeshellarg($hostname) . " A 2>&1";
        exec($command, $output, $return_var);
        
        $result['debug_info'][] = "Command: $command";
        $result['debug_info'][] = "Return code: $return_var";
        
        if ($return_var === 0 && !empty($output)) {
            foreach ($output as $line) {
                $line = trim($line);
                $result['debug_info'][] = "dig output: $line";
                
                if (filter_var($line, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                    $result['all_ips'][] = $line;
                    
                    if (!$result['success']) {
                        $result['success'] = true;
                        $result['ip_address'] = $line;
                        $result['method'] = 'dig command';
                        $result['execution_time'] = microtime(true) - $startTime;
                        $result['debug_info'][] = "SUCCESS: Found IPv4 via dig: $line";
                    }
                }
            }
            
            if ($result['success']) {
                return $result;
            }
        } else {
            $result['debug_info'][] = "dig command failed or no output";
        }
    }
    
    $result['error'] = 'Unable to resolve hostname to IPv4 address';
    $result['execution_time'] = microtime(true) - $startTime;
    $result['debug_info'][] = "FAILED: No IPv4 address found for $hostname";
    
    return $result;
}

// ทดสอบ hostname
if (isset($_POST['test_hostname'])) {
    $testHostname = $_POST['hostname'];
    echo "<h3>🔍 ผลการทดสอบ: " . htmlspecialchars($testHostname) . "</h3>";
    
    $lookupResult = improvedNslookup($testHostname);
    
    echo "<div style='background: " . ($lookupResult['success'] ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    if ($lookupResult['success']) {
        echo "<h4 style='color: green;'>✅ สำเร็จ!</h4>";
        echo "<p><strong>Primary IP Address:</strong> " . htmlspecialchars($lookupResult['ip_address']) . "</p>";
        echo "<p><strong>Method:</strong> " . htmlspecialchars($lookupResult['method']) . "</p>";
        echo "<p><strong>Execution Time:</strong> " . number_format($lookupResult['execution_time'] * 1000, 2) . " ms</p>";
        
        if (count($lookupResult['all_ips']) > 1) {
            echo "<p><strong>All IPv4 addresses found:</strong></p>";
            echo "<ul>";
            foreach (array_unique($lookupResult['all_ips']) as $ip) {
                echo "<li>" . htmlspecialchars($ip) . "</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<h4 style='color: red;'>❌ ล้มเหลว</h4>";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($lookupResult['error']) . "</p>";
        echo "<p><strong>Execution Time:</strong> " . number_format($lookupResult['execution_time'] * 1000, 2) . " ms</p>";
    }
    
    // แสดง debug info
    echo "<details style='margin-top: 10px;'>";
    echo "<summary style='cursor: pointer; font-weight: bold;'>🔍 Debug Information</summary>";
    echo "<div style='background: #f8f9fa; padding: 10px; margin-top: 5px; border-radius: 3px;'>";
    echo "<pre style='font-size: 0.9rem; margin: 0;'>";
    foreach ($lookupResult['debug_info'] as $info) {
        echo htmlspecialchars($info) . "\n";
    }
    echo "</pre>";
    echo "</div>";
    echo "</details>";
    
    echo "</div>";
}

// อัปเดต IP Address สำหรับ Asset
if (isset($_POST['update_asset_ip'])) {
    $assetId = $_POST['asset_id'];
    
    try {
        $stmt = $pdo->prepare("SELECT id, asset_id, hostname FROM assets WHERE id = ?");
        $stmt->execute([$assetId]);
        $asset = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($asset && !empty($asset['hostname'])) {
            echo "<h3>🔄 อัปเดต IP Address สำหรับ Asset: " . htmlspecialchars($asset['asset_id']) . "</h3>";
            
            $lookupResult = improvedNslookup($asset['hostname']);
            
            if ($lookupResult['success']) {
                $updateStmt = $pdo->prepare("UPDATE assets SET ip_address = ?, last_ip_check = NOW() WHERE id = ?");
                $updateStmt->execute([$lookupResult['ip_address'], $assetId]);
                
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4 style='color: green;'>✅ อัปเดต IP Address สำเร็จ!</h4>";
                echo "<p><strong>Asset ID:</strong> " . htmlspecialchars($asset['asset_id']) . "</p>";
                echo "<p><strong>Hostname:</strong> " . htmlspecialchars($asset['hostname']) . "</p>";
                echo "<p><strong>IP Address:</strong> " . htmlspecialchars($lookupResult['ip_address']) . "</p>";
                echo "<p><strong>Method:</strong> " . htmlspecialchars($lookupResult['method']) . "</p>";
                echo "<p><strong>Time:</strong> " . number_format($lookupResult['execution_time'] * 1000, 2) . " ms</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4 style='color: red;'>❌ ไม่สามารถหา IP Address ได้</h4>";
                echo "<p><strong>Asset ID:</strong> " . htmlspecialchars($asset['asset_id']) . "</p>";
                echo "<p><strong>Hostname:</strong> " . htmlspecialchars($asset['hostname']) . "</p>";
                echo "<p><strong>Error:</strong> " . htmlspecialchars($lookupResult['error']) . "</p>";
                echo "</div>";
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// แสดง Assets ที่มี hostname
try {
    echo "<h3>📋 Assets ที่มี Hostname:</h3>";
    $stmt = $pdo->query("SELECT id, asset_id, hostname, ip_address, last_ip_check FROM assets WHERE hostname IS NOT NULL AND hostname != '' ORDER BY hostname LIMIT 10");
    $assets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($assets)) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Asset ID</th><th>Hostname</th><th>Current IP</th><th>Last Check</th><th>Action</th>";
        echo "</tr>";
        
        foreach ($assets as $asset) {
            $ipDisplay = $asset['ip_address'] ? htmlspecialchars($asset['ip_address']) : '<span style="color: orange;">ยังไม่มี</span>';
            $lastCheck = $asset['last_ip_check'] ? date('d/m/Y H:i', strtotime($asset['last_ip_check'])) : '-';
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($asset['asset_id']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($asset['hostname']) . "</strong></td>";
            echo "<td>" . $ipDisplay . "</td>";
            echo "<td>" . $lastCheck . "</td>";
            echo "<td>";
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='asset_id' value='" . $asset['id'] . "'>";
            echo "<button type='submit' name='update_asset_ip' style='background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;'>🔄 อัปเดต</button>";
            echo "</form>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ ไม่มี Assets ที่มี Hostname</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// ฟอร์มทดสอบ
echo "<h3>🧪 ทดสอบ Improved NSLOOKUP:</h3>";
echo "<form method='POST' style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='hostname'><strong>Hostname:</strong></label><br>";
echo "<input type='text' id='hostname' name='hostname' placeholder='เช่น google.com' style='width: 300px; padding: 8px; margin-top: 5px;' required>";
echo "</div>";
echo "<button type='submit' name='test_hostname' style='background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>🔍 ทดสอบ NSLOOKUP</button>";
echo "</form>";

// ตัวอย่าง hostnames
echo "<h4>💡 ตัวอย่าง Hostnames:</h4>";
$exampleHostnames = ['google.com', 'facebook.com', 'github.com', 'stackoverflow.com', 'cloudflare.com'];
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
foreach ($exampleHostnames as $hostname) {
    echo "<form method='POST' style='display: inline;'>";
    echo "<input type='hidden' name='hostname' value='{$hostname}'>";
    echo "<button type='submit' name='test_hostname' style='background: #17a2b8; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; font-size: 0.9rem;'>{$hostname}</button>";
    echo "</form>";
}
echo "</div>";
?>

<hr>
<p>
    <a href="debug_nslookup.php" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        🐛 Debug NSLOOKUP
    </a>
    <a href="ip_lookup_manager.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🔧 IP Lookup Manager
    </a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th { background: #f8f9fa; }
details { margin: 10px 0; }
summary { font-weight: bold; cursor: pointer; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
button:hover { opacity: 0.8; }
</style>
