"""
Setup script for Asset Management Python GUI
"""

import subprocess
import sys
import os
from database_config import DatabaseConfig

def install_requirements():
    """Install required packages"""
    print("🔧 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        return False

def test_database_connection():
    """Test database connection"""
    print("🔍 Testing database connection...")
    try:
        success, message = DatabaseConfig.test_connection()
        if success:
            print(f"✅ Database connection successful: {message}")
            return True
        else:
            print(f"❌ Database connection failed: {message}")
            return False
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def create_database_if_needed():
    """Create database if it doesn't exist"""
    print("🗄️ Checking database...")
    try:
        success, message = DatabaseConfig.create_database_if_not_exists()
        if success:
            print(f"✅ Database ready: {message}")
            return True
        else:
            print(f"❌ Database creation failed: {message}")
            return False
    except Exception as e:
        print(f"❌ Database creation error: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Asset Management Python GUI Setup")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("requirements.txt"):
        print("❌ Error: requirements.txt not found. Please run this script from the python_gui directory.")
        return False
    
    # Install requirements
    if not install_requirements():
        print("❌ Setup failed: Could not install required packages")
        return False
    
    # Test database connection
    if not test_database_connection():
        print("❌ Setup failed: Database connection failed")
        print("Please check your database configuration in database_config.py")
        return False
    
    # Create database if needed
    if not create_database_if_needed():
        print("❌ Setup failed: Could not create database")
        return False
    
    print("\n✅ Setup completed successfully!")
    print("🎉 You can now run the application with: python asset_gui.py")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
