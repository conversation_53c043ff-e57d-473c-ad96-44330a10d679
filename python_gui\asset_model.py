"""
Asset Model for Database Operations
"""

from database_config import DatabaseConfig
from mysql.connector import Error
from datetime import datetime
import logging

class AssetModel:
    """Asset model for database operations"""
    
    def __init__(self):
        self.connection = None
    
    def connect(self):
        """Establish database connection"""
        try:
            self.connection = DatabaseConfig.get_connection()
            return True
        except Error as e:
            logging.error(f"Connection error: {e}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
    
    def get_all_assets(self, search_term=""):
        """Get all assets with optional search"""
        try:
            if not self.connect():
                return []
            
            cursor = self.connection.cursor(dictionary=True)
            
            if search_term:
                query = """
                SELECT * FROM assets 
                WHERE type LIKE %s OR brand LIKE %s OR model LIKE %s 
                   OR tag LIKE %s OR department LIKE %s OR hostname LIKE %s
                   OR serial_number LIKE %s OR asset_id LIKE %s
                ORDER BY id DESC
                """
                search_pattern = f"%{search_term}%"
                cursor.execute(query, (search_pattern,) * 8)
            else:
                cursor.execute("SELECT * FROM assets ORDER BY id DESC")
            
            assets = cursor.fetchall()
            cursor.close()
            self.disconnect()
            return assets
            
        except Error as e:
            logging.error(f"Error fetching assets: {e}")
            return []
    
    def get_asset_by_id(self, asset_id):
        """Get single asset by ID"""
        try:
            if not self.connect():
                return None
            
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute("SELECT * FROM assets WHERE id = %s", (asset_id,))
            asset = cursor.fetchone()
            cursor.close()
            self.disconnect()
            return asset
            
        except Error as e:
            logging.error(f"Error fetching asset: {e}")
            return None
    
    def create_asset(self, asset_data):
        """Create new asset"""
        try:
            if not self.connect():
                return False, "Database connection failed"
            
            cursor = self.connection.cursor()
            
            # Prepare data
            current_time = datetime.now()
            
            query = """
            INSERT INTO assets (
                type, brand, model, tag, department, status, hostname, 
                operating_system, serial_number, asset_id, warranty_expire, 
                description, set_name, created_by, updated_by, created_date, updated_date
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """
            
            values = (
                asset_data.get('type', ''),
                asset_data.get('brand', ''),
                asset_data.get('model', ''),
                asset_data.get('tag', ''),
                asset_data.get('department', ''),
                asset_data.get('status', 'ใช้งาน'),
                asset_data.get('hostname', ''),
                asset_data.get('operating_system', ''),
                asset_data.get('serial_number', ''),
                asset_data.get('asset_id', ''),
                asset_data.get('warranty_expire', None),
                asset_data.get('description', ''),
                asset_data.get('set_name', ''),
                asset_data.get('created_by', 'Python GUI'),
                asset_data.get('updated_by', 'Python GUI'),
                current_time,
                current_time
            )
            
            cursor.execute(query, values)
            self.connection.commit()
            
            new_id = cursor.lastrowid
            cursor.close()
            self.disconnect()
            
            return True, f"Asset created successfully with ID: {new_id}"
            
        except Error as e:
            logging.error(f"Error creating asset: {e}")
            return False, f"Error creating asset: {e}"
    
    def update_asset(self, asset_id, asset_data):
        """Update existing asset"""
        try:
            if not self.connect():
                return False, "Database connection failed"
            
            cursor = self.connection.cursor()
            
            query = """
            UPDATE assets SET 
                type = %s, brand = %s, model = %s, tag = %s, department = %s, 
                status = %s, hostname = %s, operating_system = %s, serial_number = %s, 
                asset_id = %s, warranty_expire = %s, description = %s, set_name = %s,
                updated_by = %s, updated_date = %s
            WHERE id = %s
            """
            
            values = (
                asset_data.get('type', ''),
                asset_data.get('brand', ''),
                asset_data.get('model', ''),
                asset_data.get('tag', ''),
                asset_data.get('department', ''),
                asset_data.get('status', 'ใช้งาน'),
                asset_data.get('hostname', ''),
                asset_data.get('operating_system', ''),
                asset_data.get('serial_number', ''),
                asset_data.get('asset_id', ''),
                asset_data.get('warranty_expire', None),
                asset_data.get('description', ''),
                asset_data.get('set_name', ''),
                asset_data.get('updated_by', 'Python GUI'),
                datetime.now(),
                asset_id
            )
            
            cursor.execute(query, values)
            self.connection.commit()
            
            affected_rows = cursor.rowcount
            cursor.close()
            self.disconnect()
            
            if affected_rows > 0:
                return True, "Asset updated successfully"
            else:
                return False, "No asset found with the given ID"
                
        except Error as e:
            logging.error(f"Error updating asset: {e}")
            return False, f"Error updating asset: {e}"
    
    def delete_asset(self, asset_id):
        """Delete asset"""
        try:
            if not self.connect():
                return False, "Database connection failed"
            
            cursor = self.connection.cursor()
            cursor.execute("DELETE FROM assets WHERE id = %s", (asset_id,))
            self.connection.commit()
            
            affected_rows = cursor.rowcount
            cursor.close()
            self.disconnect()
            
            if affected_rows > 0:
                return True, "Asset deleted successfully"
            else:
                return False, "No asset found with the given ID"
                
        except Error as e:
            logging.error(f"Error deleting asset: {e}")
            return False, f"Error deleting asset: {e}"
    
    def get_unique_values(self, column):
        """Get unique values for dropdown lists"""
        try:
            if not self.connect():
                return []
            
            cursor = self.connection.cursor()
            cursor.execute(f"SELECT DISTINCT {column} FROM assets WHERE {column} IS NOT NULL AND {column} != '' ORDER BY {column}")
            values = [row[0] for row in cursor.fetchall()]
            cursor.close()
            self.disconnect()
            return values
            
        except Error as e:
            logging.error(f"Error fetching unique values: {e}")
            return []
