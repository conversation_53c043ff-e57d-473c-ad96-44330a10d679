<?php
require_once 'includes/auth.php';
require_once 'config/database.php';
require_once 'classes/AssetManager.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

$assetManager = new AssetManager($pdo);
$message = '';
$messageType = '';
$importResults = [];

// ประมวลผลการอัปโหลดไฟล์
if ($_POST && isset($_FILES['import_file'])) {
    try {
        $uploadedFile = $_FILES['import_file'];

        // ตรวจสอบไฟล์
        if ($uploadedFile['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('เกิดข้อผิดพลาดในการอัปโหลดไฟล์: ' . $uploadedFile['error']);
        }

        $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, ['csv'])) {
            throw new Exception('รองรับเฉพาะไฟล์ CSV เท่านั้น');
        }

        // Debug: แสดงข้อมูลไฟล์
        error_log("Import file: " . $uploadedFile['name'] . ", Size: " . $uploadedFile['size']);

        // อ่านข้อมูลจากไฟล์
        $importData = parseImportFile($uploadedFile['tmp_name'], $fileExtension);

        // Debug: แสดงข้อมูลที่อ่านได้
        error_log("Import data count: " . count($importData));
        error_log("Import data: " . print_r($importData, true));

        if (empty($importData)) {
            throw new Exception('ไม่พบข้อมูลในไฟล์ หรือรูปแบบไฟล์ไม่ถูกต้อง');
        }

        // ประมวลผลการอัปเดต
        $importResults = processAssetUpdates($assetManager, $importData, $pdo);

        $successCount = count(array_filter($importResults, function($result) {
            return $result['status'] === 'success';
        }));

        $errorCount = count($importResults) - $successCount;

        if ($successCount > 0) {
            $message = "อัปเดตสำเร็จ {$successCount} รายการ";
            if ($errorCount > 0) {
                $message .= ", ล้มเหลว {$errorCount} รายการ";
            }
            $messageType = $errorCount > 0 ? 'warning' : 'success';
        } else {
            $message = "ไม่สามารถอัปเดตข้อมูลได้ ({$errorCount} รายการล้มเหลว)";
            $messageType = 'danger';
        }

    } catch (Exception $e) {
        $message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
        $messageType = 'danger';
        error_log("Import error: " . $e->getMessage());
    }
}

// ฟังก์ชันอ่านไฟล์
function parseImportFile($filePath, $extension) {
    $data = [];
    
    if ($extension === 'csv') {
        $data = parseCSVFile($filePath);
    } else {
        $data = parseExcelFile($filePath);
    }
    
    return $data;
}

function parseCSVFile($filePath) {
    $data = [];
    $headers = [];

    // อ่านไฟล์และตรวจสอบ BOM
    $content = file_get_contents($filePath);
    if (!$content) {
        throw new Exception('ไม่สามารถอ่านไฟล์ได้');
    }

    // ลบ BOM ถ้ามี
    $content = str_replace("\xEF\xBB\xBF", '', $content);

    // แปลงเป็น UTF-8 ถ้าจำเป็น
    if (!mb_check_encoding($content, 'UTF-8')) {
        $content = mb_convert_encoding($content, 'UTF-8', 'auto');
    }

    // แยกบรรทัด
    $lines = explode("\n", $content);
    $rowIndex = 0;

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        // แยกคอลัมน์
        $row = str_getcsv($line, ',', '"');

        if ($rowIndex === 0) {
            // บรรทัดแรกเป็น header
            $headers = array_map('trim', $row);
            error_log("CSV Headers: " . print_r($headers, true));
        } else {
            // ตรวจสอบว่าจำนวนคอลัมน์ตรงกับ header หรือไม่
            if (count($row) >= count($headers)) {
                // ตัดให้เท่ากับจำนวน headers
                $row = array_slice($row, 0, count($headers));
                $rowData = array_combine($headers, array_map('trim', $row));
                $data[] = $rowData;
                error_log("CSV Row {$rowIndex}: " . print_r($rowData, true));
            } else {
                error_log("CSV Row {$rowIndex} skipped: column count mismatch");
            }
        }
        $rowIndex++;
    }

    return $data;
}

function parseExcelFile($filePath) {
    // ไม่รองรับ Excel ในเวอร์ชันนี้
    throw new Exception('รองรับเฉพาะไฟล์ CSV เท่านั้น กรุณาแปลงไฟล์ Excel เป็น CSV ก่อนนำเข้า');
}

function processAssetUpdates($assetManager, $importData, $pdo) {
    $results = [];
    $currentUser = getCurrentUserFullName();

    error_log("Processing " . count($importData) . " rows for update");

    foreach ($importData as $index => $row) {
        $rowNumber = $index + 2; // +2 เพราะเริ่มจากแถวที่ 2 (แถวแรกเป็น header)

        try {
            error_log("Processing row {$rowNumber}: " . print_r($row, true));

            // ตรวจสอบข้อมูลที่จำเป็น
            if (empty($row['asset_id'])) {
                throw new Exception('ไม่พบ Asset ID');
            }

            // ค้นหา Asset ที่มีอยู่
            $stmt = $pdo->prepare("SELECT * FROM assets WHERE asset_id = ?");
            $stmt->execute([$row['asset_id']]);
            $existingAsset = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$existingAsset) {
                throw new Exception("ไม่พบ Asset ID: {$row['asset_id']}");
            }

            error_log("Found existing asset: " . $existingAsset['id']);

            // เตรียมข้อมูลสำหรับอัปเดต
            $updateData = prepareUpdateData($row, $existingAsset);
            error_log("Update data prepared: " . print_r($updateData, true));

            // ตรวจสอบว่ามีการเปลี่ยนแปลงหรือไม่
            $changes = detectChanges($existingAsset, $updateData);
            error_log("Changes detected: " . print_r($changes, true));

            if (empty($changes)) {
                $results[] = [
                    'row' => $rowNumber,
                    'asset_id' => $row['asset_id'],
                    'status' => 'skipped',
                    'message' => 'ไม่มีการเปลี่ยนแปลง'
                ];
                continue;
            }

            // อัปเดตข้อมูล
            $success = $assetManager->updateAsset($existingAsset['id'], $updateData);
            error_log("Update result: " . ($success ? 'success' : 'failed'));

            if ($success) {
                // บันทึก log การเปลี่ยนแปลง
                logAssetChanges($pdo, $existingAsset['id'], $changes, $currentUser);

                $results[] = [
                    'row' => $rowNumber,
                    'asset_id' => $row['asset_id'],
                    'status' => 'success',
                    'message' => 'อัปเดตสำเร็จ',
                    'changes' => $changes
                ];
            } else {
                throw new Exception('ไม่สามารถอัปเดตข้อมูลได้');
            }

        } catch (Exception $e) {
            error_log("Error processing row {$rowNumber}: " . $e->getMessage());
            $results[] = [
                'row' => $rowNumber,
                'asset_id' => $row['asset_id'] ?? 'ไม่ระบุ',
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    return $results;
}

function prepareUpdateData($row, $existingAsset) {
    $updateData = [];

    // รายการฟิลด์ที่สามารถอัปเดตได้ (ตรงกับโครงสร้างฐานข้อมูล)
    $updatableFields = [
        'type', 'brand', 'model', 'tag', 'department', 'status',
        'hostname', 'ip_address', 'operating_system', 'serial_number', 'warranty_expire',
        'description', 'set_name'
    ];

    foreach ($updatableFields as $field) {
        if (isset($row[$field]) && trim($row[$field]) !== '') {
            $updateData[$field] = trim($row[$field]);
        } else {
            // ถ้าไม่มีข้อมูลใหม่ ใช้ข้อมูลเดิม
            $updateData[$field] = $existingAsset[$field] ?? '';
        }
    }

    // เพิ่มฟิลด์ที่จำเป็นสำหรับ AssetManager (ตรงกับโครงสร้างฐานข้อมูล)
    $updateData['asset_id'] = $existingAsset['asset_id']; // ไม่ให้เปลี่ยน asset_id
    $updateData['updated_by'] = getCurrentUserFullName() ?? 'Import System'; // ใช้ updated_by

    return $updateData;
}

function detectChanges($existingAsset, $updateData) {
    $changes = [];

    // เฉพาะฟิลด์ที่สามารถเปลี่ยนแปลงได้
    $changeableFields = [
        'type', 'brand', 'model', 'tag', 'department', 'status',
        'hostname', 'operating_system', 'serial_number', 'warranty_expire',
        'description', 'set_name'
    ];

    foreach ($changeableFields as $field) {
        $oldValue = $existingAsset[$field] ?? '';
        $newValue = $updateData[$field] ?? '';

        // แปลง null เป็น empty string เพื่อเปรียบเทียบ
        $oldValue = $oldValue === null ? '' : (string)$oldValue;
        $newValue = $newValue === null ? '' : (string)$newValue;

        if ($oldValue !== $newValue) {
            $changes[] = [
                'field_name' => $field,
                'old_value' => $oldValue,
                'new_value' => $newValue
            ];
        }
    }

    return $changes;
}

function logAssetChanges($pdo, $assetId, $changes, $userName) {
    foreach ($changes as $change) {
        $stmt = $pdo->prepare("
            INSERT INTO asset_logs (asset_id, action_type, field_name, old_value, new_value, changed_by, changed_date, description)
            VALUES (?, 'UPDATE', ?, ?, ?, ?, NOW(), ?)
        ");
        $stmt->execute([
            $assetId,
            $change['field_name'],
            $change['old_value'],
            $change['new_value'],
            $userName,
            "Import Update: {$change['field_name']} changed from '{$change['old_value']}' to '{$change['new_value']}'"
        ]);
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import Update Assets - Asset Management System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ระบบจัดการ Asset</h1>
            <p class="subtitle">Import Update Assets</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="index.php"><i class="fas fa-list"></i> รายการ Assets</a></li>
                <?php if (isAdmin()): ?>
                    <li><a href="users.php"><i class="fas fa-users"></i> จัดการผู้ใช้</a></li>
                <?php endif; ?>
                <li><a href="#" onclick="openToolsModal()"><i class="fas fa-tools"></i> เครื่องมือ</a></li>
                <li><a href="#" onclick="openProfileModal()"><i class="fas fa-user"></i> โปรไฟล์</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> ออกจากระบบ</a></li>
            </ul>
        </nav>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- คำแนะนำการใช้งาน -->
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-info-circle"></i> วิธีการใช้งาน Import Update</h2>
            </div>
            <div class="card-body">
                <div class="instructions">
                    <h4>ขั้นตอนการอัปเดต Assets แบบ Import:</h4>
                    <ol>
                        <li><strong>ดาวน์โหลดไฟล์ตัวอย่าง</strong> เพื่อดูรูปแบบที่ถูกต้อง</li>
                        <li><strong>เตรียมข้อมูล</strong> ในไฟล์ CSV โดยใส่ Asset ID ที่ต้องการอัปเดต</li>
                        <li><strong>ใส่เฉพาะข้อมูลที่ต้องการเปลี่ยน</strong> ช่องที่ว่างจะใช้ข้อมูลเดิม</li>
                        <li><strong>อัปโหลดไฟล์</strong> และตรวจสอบผลลัพธ์</li>
                    </ol>
                    
                    <div class="alert alert-info">
                        <strong>หมายเหตุ:</strong>
                        <ul>
                            <li>Asset ID เป็นฟิลด์บังคับ ใช้สำหรับค้นหา Asset ที่จะอัปเดต</li>
                            <li>ช่องที่ว่างหรือไม่มีข้อมูลจะไม่ถูกอัปเดต (ใช้ข้อมูลเดิม)</li>
                            <li>ระบบจะสร้าง Auto Backup และส่งแจ้งเตือนอัตโนมัติ</li>
                            <li>รองรับไฟล์ CSV เท่านั้น (Excel ให้แปลงเป็น CSV ก่อน)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- ดาวน์โหลดไฟล์ตัวอย่าง -->
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-download"></i> ดาวน์โหลดไฟล์ตัวอย่าง</h2>
            </div>
            <div class="card-body">
                <p>ดาวน์โหลดไฟล์ตัวอย่างเพื่อดูรูปแบบที่ถูกต้อง:</p>
                <div class="download-buttons">
                    <a href="download_update_template.php" class="btn btn-primary">
                        <i class="fas fa-file-csv"></i> ดาวน์โหลดไฟล์ตัวอย่าง CSV
                    </a>
                    <a href="export_current_assets.php?format=csv" class="btn btn-secondary">
                        <i class="fas fa-database"></i> Export ข้อมูลปัจจุบัน (CSV)
                    </a>
                </div>
            </div>
        </div>

        <!-- ฟอร์มอัปโหลด -->
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-upload"></i> อัปโหลดไฟล์ Update</h2>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="importForm">
                    <div class="form-group">
                        <label for="import_file">เลือกไฟล์ CSV</label>
                        <input type="file" id="import_file" name="import_file" class="form-control"
                               accept=".csv" required>
                        <small class="form-text">รองรับเฉพาะไฟล์ CSV (.csv) เท่านั้น</small>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="confirm_update" required>
                            ฉันยืนยันว่าได้ตรวจสอบข้อมูลแล้วและต้องการอัปเดต Assets
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-upload"></i> อัปโหลดและอัปเดต
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> กลับหน้าหลัก
                    </a>
                </form>
            </div>
        </div>

        <!-- แสดงผลลัพธ์ -->
        <?php if (!empty($importResults)): ?>
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-chart-bar"></i> ผลลัพธ์การ Import</h2>
            </div>
            <div class="card-body">
                <div class="import-summary">
                    <?php
                    $successCount = count(array_filter($importResults, function($r) { return $r['status'] === 'success'; }));
                    $errorCount = count(array_filter($importResults, function($r) { return $r['status'] === 'error'; }));
                    $skippedCount = count(array_filter($importResults, function($r) { return $r['status'] === 'skipped'; }));
                    ?>
                    <div class="summary-stats">
                        <div class="stat-item success">
                            <div class="stat-number"><?= $successCount ?></div>
                            <div class="stat-label">สำเร็จ</div>
                        </div>
                        <div class="stat-item error">
                            <div class="stat-number"><?= $errorCount ?></div>
                            <div class="stat-label">ล้มเหลว</div>
                        </div>
                        <div class="stat-item skipped">
                            <div class="stat-number"><?= $skippedCount ?></div>
                            <div class="stat-label">ข้าม</div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>แถว</th>
                                <th>Asset ID</th>
                                <th>สถานะ</th>
                                <th>ข้อความ</th>
                                <th>การเปลี่ยนแปลง</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($importResults as $result): ?>
                            <tr class="result-<?= $result['status'] ?>">
                                <td><?= $result['row'] ?></td>
                                <td><?= htmlspecialchars($result['asset_id']) ?></td>
                                <td>
                                    <?php if ($result['status'] === 'success'): ?>
                                        <span class="badge badge-success">สำเร็จ</span>
                                    <?php elseif ($result['status'] === 'error'): ?>
                                        <span class="badge badge-danger">ล้มเหลว</span>
                                    <?php else: ?>
                                        <span class="badge badge-secondary">ข้าม</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= htmlspecialchars($result['message']) ?></td>
                                <td>
                                    <?php if (isset($result['changes']) && !empty($result['changes'])): ?>
                                        <ul class="changes-list">
                                            <?php foreach ($result['changes'] as $change): ?>
                                            <li>
                                                <strong><?= htmlspecialchars($change['field_name']) ?>:</strong>
                                                <?= htmlspecialchars($change['old_value'] ?: 'ว่าง') ?>
                                                → <?= htmlspecialchars($change['new_value'] ?: 'ว่าง') ?>
                                            </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script>
        document.getElementById('importForm').addEventListener('submit', function(e) {
            const fileInput = document.getElementById('import_file');
            const confirmCheckbox = document.getElementById('confirm_update');
            const submitBtn = document.getElementById('submitBtn');

            if (!fileInput.files.length) {
                e.preventDefault();
                alert('กรุณาเลือกไฟล์');
                return;
            }

            if (!confirmCheckbox.checked) {
                e.preventDefault();
                alert('กรุณายืนยันการอัปเดต');
                return;
            }

            // แสดง loading
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> กำลังประมวลผล...';
            submitBtn.disabled = true;
        });

        // ตรวจสอบไฟล์ที่เลือก
        document.getElementById('import_file').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const fileSize = file.size / 1024 / 1024; // MB
                if (fileSize > 10) {
                    alert('ไฟล์มีขนาดใหญ่เกิน 10MB');
                    e.target.value = '';
                }
            }
        });
    </script>

    <style>
        .download-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .import-summary {
            margin-bottom: 20px;
        }

        .summary-stats {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            min-width: 80px;
        }

        .stat-item.success {
            background: #d4edda;
            color: #155724;
        }

        .stat-item.error {
            background: #f8d7da;
            color: #721c24;
        }

        .stat-item.skipped {
            background: #e2e3e5;
            color: #383d41;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }

        .stat-label {
            font-size: 0.9rem;
        }

        .result-success {
            background-color: #f8fff9;
        }

        .result-error {
            background-color: #fff8f8;
        }

        .result-skipped {
            background-color: #f8f9fa;
        }

        .changes-list {
            margin: 0;
            padding-left: 20px;
            font-size: 0.9rem;
        }

        .changes-list li {
            margin-bottom: 5px;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .badge-success {
            background: #28a745;
            color: white;
        }

        .badge-danger {
            background: #dc3545;
            color: white;
        }

        .badge-secondary {
            background: #6c757d;
            color: white;
        }
    </style>
</body>
</html>
