<?php
// ดึงรายการโฟลเดอร์สำหรับ Folder Browser
header('Content-Type: application/json; charset=utf-8');

require_once 'includes/auth.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

// รับข้อมูล JSON
$input = json_decode(file_get_contents('php://input'), true);
$requestedPath = $input['path'] ?? './';

try {
    // ทำความสะอาด path
    $path = $requestedPath;

    // สำหรับ Windows drives
    if (preg_match('/^[A-Za-z]:\\?$/', $path)) {
        $path = rtrim($path, '\\') . '\\';
    }
    // สำหรับ Unix root
    elseif ($path === '/' || $path === '') {
        $path = '/';
    }
    // สำหรับ path อื่นๆ
    else {
        $realPath = realpath($path);
        if ($realPath !== false) {
            $path = $realPath;
        } else {
            // ถ้า realpath ล้มเหลว ให้ทำความสะอาด path
            $path = rtrim($path, '/\\');
            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                $path .= '\\';
            } else {
                $path .= '/';
            }
        }
    }

    // ตรวจสอบว่า path มีอยู่จริง
    if (!is_dir($path)) {
        echo json_encode([
            'success' => false,
            'message' => 'โฟลเดอร์ไม่มีอยู่จริง: ' . $path
        ]);
        exit;
    }
    
    // ดึงรายการโฟลเดอร์
    $folders = [];
    $items = scandir($path);
    
    if ($items === false) {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่สามารถอ่านโฟลเดอร์ได้'
        ]);
        exit;
    }
    
    foreach ($items as $item) {
        // ข้าม . และ ..
        if ($item === '.' || $item === '..') {
            continue;
        }
        
        // สร้าง path ที่ถูกต้อง
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            // Windows
            $itemPath = rtrim($path, '\\') . '\\' . $item;
        } else {
            // Unix-like
            $itemPath = rtrim($path, '/') . '/' . $item;
        }

        // เฉพาะโฟลเดอร์เท่านั้น
        if (is_dir($itemPath)) {
            // ตรวจสอบสิทธิ์การเขียน
            $writable = is_writable($itemPath);

            $folders[] = [
                'name' => $item,
                'path' => $itemPath,
                'writable' => $writable
            ];
        }
    }
    
    // เรียงลำดับตามชื่อ
    usort($folders, function($a, $b) {
        return strcasecmp($a['name'], $b['name']);
    });
    
    echo json_encode([
        'success' => true,
        'folders' => $folders,
        'current_path' => $path
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
    ]);
}
?>
