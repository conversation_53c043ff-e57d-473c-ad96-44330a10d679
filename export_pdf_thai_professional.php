<?php
// รายงาน PDF แบบไทยเป็นทางการและเรียบง่าย
error_reporting(E_ALL);
ini_set('display_errors', 1);

// เชื่อมต่อฐานข้อมูล
$host = 'localhost';
$username = 'root';
$password = 'Wxmujwsofu@1234';
$database = 'asset_management';

$conn = new mysqli($host, $username, $password, $database);
$conn->set_charset("utf8mb4");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

require_once 'vendor/autoload.php';

// ฟังก์ชันสำหรับตั้งค่าฟอนต์ไทย
function setupThaiFont() {
    $fontPath = __DIR__ . "/fonts/THSarabunNew.ttf";
    
    if (file_exists($fontPath)) {
        try {
            $fontName = TCPDF_FONTS::addTTFfont($fontPath, "TrueTypeUnicode", "", 96);
            return $fontName ? $fontName : "helvetica";
        } catch (Exception $e) {
            return "helvetica";
        }
    }
    
    return "helvetica";
}

// รับพารามิเตอร์
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';
$filter_serial = $_GET['filter_serial'] ?? '';
$isDownload = isset($_GET['download']) && $_GET['download'] === 'true';

// สร้าง TCPDF object
$pdf = new TCPDF('L', PDF_UNIT, 'A4', true, 'UTF-8', false);

// ตั้งค่าข้อมูลเอกสาร
$pdf->SetCreator('ระบบจัดการทรัพย์สิน');
$pdf->SetAuthor('ระบบจัดการทรัพย์สิน');
$pdf->SetTitle('รายงานทรัพย์สิน');
$pdf->SetSubject('รายงานทรัพย์สิน');

// ตั้งค่าหน้า
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
$pdf->SetMargins(20, 25, 20);
$pdf->SetHeaderMargin(10);
$pdf->SetFooterMargin(15);
$pdf->SetAutoPageBreak(TRUE, 20);

// ตั้งค่าฟอนต์ไทย
$thaiFont = setupThaiFont();
$pdf->SetFont($thaiFont, '', 10);

// เพิ่มหน้าแรก
$pdf->AddPage();

// สร้าง SQL query
$sql = "SELECT * FROM assets WHERE 1=1";
$params = [];
$conditions = [];

// เพิ่มเงื่อนไขการกรอง
if (!empty($search)) {
    $conditions[] = "(type LIKE ? OR brand LIKE ? OR model LIKE ? OR tag LIKE ? OR hostname LIKE ? OR department LIKE ? OR asset_id LIKE ? OR serial_number LIKE ?)";
    $searchTerm = '%' . $search . '%';
    for ($i = 0; $i < 8; $i++) {
        $params[] = $searchTerm;
    }
}

if (!empty($filter_type)) {
    $conditions[] = "type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_brand)) {
    $conditions[] = "brand = ?";
    $params[] = $filter_brand;
}

if (!empty($filter_department)) {
    $conditions[] = "department = ?";
    $params[] = $filter_department;
}

if (!empty($filter_status)) {
    $conditions[] = "status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_os)) {
    $conditions[] = "operating_system = ?";
    $params[] = $filter_os;
}

if (!empty($filter_serial)) {
    $conditions[] = "serial_number LIKE ?";
    $params[] = '%' . $filter_serial . '%';
}

// รวมเงื่อนไข
if (!empty($conditions)) {
    $sql .= " AND " . implode(" AND ", $conditions);
}

$sql .= " ORDER BY created_date DESC";

// สร้างชื่อรายงาน
$title = "รายงานทรัพย์สิน";
$filterInfo = [];
if (!empty($search)) $filterInfo[] = "ค้นหา: $search";
if (!empty($filter_type)) $filterInfo[] = "ประเภท: $filter_type";
if (!empty($filter_brand)) $filterInfo[] = "ยี่ห้อ: $filter_brand";
if (!empty($filter_department)) $filterInfo[] = "แผนก: $filter_department";
if (!empty($filter_status)) $filterInfo[] = "สถานะ: $filter_status";
if (!empty($filter_os)) $filterInfo[] = "ระบบปฏิบัติการ: $filter_os";
if (!empty($filter_serial)) $filterInfo[] = "หมายเลขเครื่อง: $filter_serial";

// วันที่รายงาน
$reportDate = date('d/m/Y H:i:s');

// === HEADER SECTION ===
// ส่วนหัวองค์กร
$pdf->SetFont($thaiFont, 'B', 22);
$pdf->SetTextColor(25, 42, 86); // สีน้ำเงินเข้ม
$pdf->Cell(0, 12, 'ระบบจัดการทรัพย์สิน', 0, 1, 'C');

$pdf->SetFont($thaiFont, '', 11);
$pdf->SetTextColor(108, 117, 125); // สีเทาอ่อน
$pdf->Cell(0, 6, 'Asset Management System', 0, 1, 'C');

// เส้นแบ่ง
$pdf->Ln(5);
$pdf->SetDrawColor(173, 181, 189); // เส้นสีเทาอ่อน
$pdf->Line(20, $pdf->GetY(), 277, $pdf->GetY());
$pdf->Ln(8);

// ชื่อรายงาน
$pdf->SetFont($thaiFont, 'B', 18);
$pdf->SetTextColor(33, 37, 41); // สีดำเข้ม
$pdf->Cell(0, 10, $title, 0, 1, 'C');

// ข้อมูลรายงาน
$pdf->SetFont($thaiFont, '', 10);
$pdf->SetTextColor(108, 117, 125);
$pdf->Cell(0, 5, 'วันที่สร้างรายงาน: ' . $reportDate, 0, 1, 'C');

if (!empty($filterInfo)) {
    $pdf->Cell(0, 5, 'เงื่อนไขการกรอง: ' . implode(", ", $filterInfo), 0, 1, 'C');
}

$pdf->Ln(10);

// === TABLE HEADER ===
$pdf->SetFillColor(33, 37, 41); // พื้นหลังสีดำเข้ม
$pdf->SetTextColor(255, 255, 255); // ตัวอักษรสีขาว
$pdf->SetDrawColor(33, 37, 41); // เส้นขอบสีดำเข้ม

// กำหนดความกว้างของคอลัมน์
$colWidths = [15, 35, 30, 30, 30, 30, 35, 25, 27];

// หัวตาราง
$headers = ['ลำดับ', 'แผนก', 'ประเภท', 'รหัสทรัพย์สิน', 'แท็ก', 'ยี่ห้อ/รุ่น', 'หมายเลขเครื่อง', 'สถานะ', 'วันที่สร้าง'];

$pdf->SetFont($thaiFont, 'B', 10);
foreach ($headers as $i => $header) {
    $pdf->Cell($colWidths[$i], 8, $header, 1, 0, 'C', true);
}
$pdf->Ln();

// === TABLE DATA ===
$pdf->SetTextColor(33, 37, 41); // ตัวอักษรสีดำเข้ม
$pdf->SetFont($thaiFont, '', 9);

// เตรียม prepared statement
$stmt = $conn->prepare($sql);

// Bind parameters ถ้ามี
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();

$sequenceNumber = 1;
$fill = false;
$rowsPerPage = 0;
$maxRowsPerPage = 28;

while ($row = $result->fetch_assoc()) {
    // ตรวจสอบว่าต้องขึ้นหน้าใหม่หรือไม่
    if ($rowsPerPage >= $maxRowsPerPage || $pdf->GetY() > 180) {
        $pdf->AddPage();
        
        // สร้างหัวตารางใหม่
        $pdf->SetFillColor(33, 37, 41);
        $pdf->SetTextColor(255, 255, 255);
        $pdf->SetFont($thaiFont, 'B', 10);
        foreach ($headers as $i => $header) {
            $pdf->Cell($colWidths[$i], 8, $header, 1, 0, 'C', true);
        }
        $pdf->Ln();
        
        $pdf->SetTextColor(33, 37, 41);
        $pdf->SetFont($thaiFont, '', 9);
        $rowsPerPage = 0;
    }

    // สลับสีแถว
    if ($fill) {
        $pdf->SetFillColor(248, 249, 250); // สีเทาอ่อนมาก
    } else {
        $pdf->SetFillColor(255, 255, 255); // สีขาว
    }

    // ข้อมูลแถว
    $brandModel = trim(($row['brand'] ?? '') . ' ' . ($row['model'] ?? ''));
    $createdDate = !empty($row['created_date']) ? date('d/m/Y', strtotime($row['created_date'])) : '';
    
    $rowData = [
        $sequenceNumber,
        substr($row['department'] ?? '', 0, 20),
        $row['type'] ?? '',
        $row['asset_id'] ?? '',
        $row['tag'] ?? '',
        substr($brandModel, 0, 25),
        substr($row['serial_number'] ?? '', 0, 20),
        $row['status'] ?? '',
        $createdDate
    ];

    foreach ($rowData as $i => $data) {
        $align = ($i == 0) ? 'C' : 'L';
        $pdf->Cell($colWidths[$i], 6, $data, 1, 0, $align, $fill);
    }
    $pdf->Ln();

    $sequenceNumber++;
    $rowsPerPage++;
    $fill = !$fill;
}

// === SUMMARY SECTION ===
$totalRecords = $result->num_rows;
$pdf->Ln(10);

// กล่องสรุป
$pdf->SetFillColor(233, 236, 239); // พื้นหลังสีเทาอ่อน
$pdf->SetTextColor(33, 37, 41);
$pdf->SetDrawColor(173, 181, 189);

$pdf->SetFont($thaiFont, 'B', 14);
$pdf->Cell(0, 8, 'สรุปรายงาน', 1, 1, 'C', true);

$pdf->SetFont($thaiFont, '', 11);
$pdf->SetFillColor(255, 255, 255);

$summaryData = [
    ['จำนวนรายการทั้งหมด:', $totalRecords . ' รายการ'],
    ['วันที่สร้างรายงาน:', $reportDate],
    ['ระบบที่สร้าง:', 'ระบบจัดการทรัพย์สิน']
];

if (!empty($filterInfo)) {
    $summaryData[] = ['เงื่อนไขที่ใช้:', implode(", ", $filterInfo)];
}

foreach ($summaryData as $row) {
    $pdf->Cell(60, 6, $row[0], 1, 0, 'L', true);
    $pdf->Cell(0, 6, $row[1], 1, 1, 'L', true);
}

// === FOOTER ===
$pdf->Ln(10);
$pdf->SetFont($thaiFont, 'I', 9);
$pdf->SetTextColor(108, 117, 125);
$pdf->Cell(0, 5, 'รายงานนี้สร้างโดยระบบจัดการทรัพย์สินอัตโนมัติ', 0, 1, 'C');
$pdf->Cell(0, 5, 'สำหรับใช้ภายในองค์กรเท่านั้น - ข้อมูลลับ', 0, 1, 'C');

// สร้างชื่อไฟล์
$filename = "รายงานทรัพย์สิน_" . date('Y-m-d_H-i-s');
if (!empty($filter_type)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9ก-๙]/', '_', $filter_type);
if (!empty($filter_department)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9ก-๙]/', '_', $filter_department);

// ส่งออก PDF
if ($isDownload) {
    $pdf->Output($filename . '.pdf', 'D');
} else {
    $pdf->Output($filename . '.pdf', 'I');
}

// ปิดการเชื่อมต่อฐานข้อมูล
$stmt->close();
$conn->close();
?>
