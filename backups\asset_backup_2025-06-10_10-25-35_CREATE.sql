-- Asset Management System Database Backup
-- Created: 2025-06-10 10:25:35
-- Action: CREATE
-- Description: เพิ่ม Asset ใหม่ ID: 8698 - Desktop Lenovo Test
-- Generated by Auto Backup System

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;

-- Table: asset_logs
DROP TABLE IF EXISTS `asset_logs`;
CREATE TABLE `asset_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `asset_id` int NOT NULL,
  `action_type` enum('CREATE','UPDATE','DELETE') COLLATE utf8mb4_unicode_ci NOT NULL,
  `field_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `old_value` text COLLATE utf8mb4_unicode_ci,
  `new_value` text COLLATE utf8mb4_unicode_ci,
  `changed_by` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `changed_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `description` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `idx_asset_id` (`asset_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_changed_date` (`changed_date`),
  CONSTRAINT `asset_logs_ibfk_1` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13996 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: assets
DROP TABLE IF EXISTS `assets`;
CREATE TABLE `assets` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `brand` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `model` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tag` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `department` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('ใช้งาน','ชำรุด','สำรอง') COLLATE utf8mb4_unicode_ci DEFAULT 'ใช้งาน',
  `hostname` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `operating_system` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `serial_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `asset_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `warranty_expire` date DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `set_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updated_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_brand` (`brand`),
  KEY `idx_department` (`department`),
  KEY `idx_status` (`status`),
  KEY `idx_tag` (`tag`),
  KEY `idx_asset_id` (`asset_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8699 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `assets` (`id`, `type`, `brand`, `model`, `tag`, `department`, `status`, `hostname`, `operating_system`, `serial_number`, `asset_id`, `warranty_expire`, `description`, `set_name`, `created_date`, `created_by`, `updated_date`, `updated_by`) VALUES
('8698', 'Desktop', 'Lenovo', 'Test', 'Test', 'Customer Service', 'ใช้งาน', 'Test', 'Windows 11', 'Test', 'Test', NULL, 'Test', NULL, '2025-06-10 10:25:35', 'เอกลักษณ์ สงวนสินธ์', '2025-06-10 10:25:35', 'เอกลักษณ์ สงวนสินธ์');

-- Table: backup_logs
DROP TABLE IF EXISTS `backup_logs`;
CREATE TABLE `backup_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `filename` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'ชื่อไฟล์ backup',
  `action_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'ประเภทการดำเนินการ (CREATE, UPDATE, DELETE, MANUAL)',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT 'รายละเอียดการ backup',
  `file_size` bigint DEFAULT NULL COMMENT 'ขนาดไฟล์ (bytes)',
  `created_date` datetime NOT NULL COMMENT 'วันที่สร้าง backup',
  `created_by` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'ผู้สร้าง backup',
  PRIMARY KEY (`id`),
  KEY `idx_created_date` (`created_date`),
  KEY `idx_action_type` (`action_type`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตาราง log การ backup ฐานข้อมูล';

-- Table: users
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `full_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `users` (`id`, `username`, `password`, `full_name`, `role`, `email`, `status`, `created_date`, `updated_date`, `last_login`) VALUES
('6', '063070022', '$2y$10$nzPspR8GPwb4ee.IX4TLOeYnzHRMEvtgxAXRprXtKpwSiwSQ7BrKu', 'เอกลักษณ์ สงวนสินธ์', 'Admin', '<EMAIL>', 'Active', '2025-06-01 16:09:26', '2025-06-10 10:25:18', '2025-06-10 10:25:18'),
('7', 'Test', '$2y$10$ct4kavkf.Jd09pny6vRSa.OKtVvlTGUnIN7EWndWpQMtllbEkibhO', 'Test', 'User', NULL, 'Active', '2025-06-02 00:36:07', '2025-06-05 15:19:54', '2025-06-05 15:19:54'),
('8', 'jarkapan', '$2y$10$YEVUj7DydCB3Q74Ytl3MROqzKFAq5Gd/apQl3dnXCdJryI1ot3dk2', 'jarkapan', 'Admin', NULL, 'Active', '2025-06-04 14:05:19', '2025-06-05 13:51:09', '2025-06-05 13:51:09'),
('9', 'koy', '$2y$10$8wPVPaDkw2lHD5lG0S5.se7.BsYIUpUE/fwj4Lre2/wjrzW4X3pge', 'natthamon Tha', 'User', '<EMAIL>', 'Active', '2025-06-04 14:05:33', '2025-06-04 14:23:11', NULL),
('10', 'thitiphong', '$2y$10$YcIptvJOZKSF70QLBnezHORH7FxAhqW..G3rURP5SsV/9BEgci.Pa', 'thitiphong.pa', 'User', '', 'Active', '2025-06-04 14:17:00', '2025-06-06 17:28:47', '2025-06-05 10:10:34');


SET FOREIGN_KEY_CHECKS=1;
COMMIT;
