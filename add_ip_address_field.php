<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>🌐 เพิ่มฟิลด์ IP Address ให้ตาราง Assets</h2>";

try {
    // ตรวจสอบว่ามีฟิลด์ ip_address แล้วหรือไม่
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasIpField = false;
    $hasLastIpCheckField = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'ip_address') {
            $hasIpField = true;
        }
        if ($column['Field'] === 'last_ip_check') {
            $hasLastIpCheckField = true;
        }
    }
    
    echo "<h3>1. ตรวจสอบฟิลด์ปัจจุบัน:</h3>";
    echo "<ul>";
    echo "<li>ip_address: " . ($hasIpField ? "<span style='color: green;'>✅ มีแล้ว</span>" : "<span style='color: red;'>❌ ยังไม่มี</span>") . "</li>";
    echo "<li>last_ip_check: " . ($hasLastIpCheckField ? "<span style='color: green;'>✅ มีแล้ว</span>" : "<span style='color: red;'>❌ ยังไม่มี</span>") . "</li>";
    echo "</ul>";
    
    // เพิ่มฟิลด์ถ้ายังไม่มี
    if (!$hasIpField || !$hasLastIpCheckField) {
        echo "<h3>2. กำลังเพิ่มฟิลด์:</h3>";
        
        if (!$hasIpField) {
            $pdo->exec("ALTER TABLE assets ADD COLUMN ip_address VARCHAR(45) NULL AFTER hostname");
            echo "<p style='color: green;'>✅ เพิ่มฟิลด์ ip_address สำเร็จ</p>";
        }
        
        if (!$hasLastIpCheckField) {
            $pdo->exec("ALTER TABLE assets ADD COLUMN last_ip_check DATETIME NULL AFTER ip_address");
            echo "<p style='color: green;'>✅ เพิ่มฟิลด์ last_ip_check สำเร็จ</p>";
        }
        
        // เพิ่ม index สำหรับ ip_address
        try {
            $pdo->exec("ALTER TABLE assets ADD INDEX idx_ip_address (ip_address)");
            echo "<p style='color: green;'>✅ เพิ่ม index สำหรับ ip_address สำเร็จ</p>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), "Duplicate key name") !== false) {
                echo "<p style='color: orange;'>⚠️ Index ip_address มีอยู่แล้ว</p>";
            } else {
                echo "<p style='color: red;'>❌ เกิดข้อผิดพลาดในการเพิ่ม index: " . $e->getMessage() . "</p>";
            }
        }
        
    } else {
        echo "<h3>2. สถานะ:</h3>";
        echo "<p style='color: green;'>✅ ฟิลด์ IP Address มีครบแล้ว</p>";
    }
    
    // แสดงโครงสร้างตารางใหม่
    echo "<h3>3. โครงสร้างตารางหลังเพิ่มฟิลด์:</h3>";
    $stmt = $pdo->query("DESCRIBE assets");
    $newColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th>";
    echo "</tr>";
    
    foreach ($newColumns as $column) {
        $highlight = in_array($column['Field'], ['ip_address', 'last_ip_check']) ? 'background: #fff3cd;' : '';
        echo "<tr style='{$highlight}'>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ตรวจสอบข้อมูล Assets ที่มี hostname
    echo "<h3>4. Assets ที่มี Hostname (สำหรับทดสอบ NSLOOKUP):</h3>";
    $stmt = $pdo->query("SELECT id, asset_id, hostname, ip_address, last_ip_check FROM assets WHERE hostname IS NOT NULL AND hostname != '' ORDER BY hostname");
    $assetsWithHostname = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($assetsWithHostname)) {
        echo "<p style='color: orange;'>⚠️ ไม่มี Assets ที่มี Hostname</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>Asset ID</th><th>Hostname</th><th>IP Address</th><th>Last IP Check</th><th>สถานะ</th>";
        echo "</tr>";
        
        foreach ($assetsWithHostname as $asset) {
            $ipStatus = empty($asset['ip_address']) ? 
                "<span style='color: orange;'>ยังไม่มี IP</span>" : 
                "<span style='color: green;'>มี IP แล้ว</span>";
                
            echo "<tr>";
            echo "<td>" . $asset['id'] . "</td>";
            echo "<td>" . htmlspecialchars($asset['asset_id']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($asset['hostname']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($asset['ip_address'] ?: '-') . "</td>";
            echo "<td>" . ($asset['last_ip_check'] ?: '-') . "</td>";
            echo "<td>" . $ipStatus . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>จำนวน Assets ที่มี Hostname:</strong> " . count($assetsWithHostname) . " รายการ</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<hr>
<p>
    <a href="ip_lookup_manager.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        🔍 จัดการ IP Lookup
    </a>
    <a href="test_nslookup.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🧪 ทดสอบ NSLOOKUP
    </a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th { background: #f8f9fa; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
