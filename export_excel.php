<?php
// Export ข้อมูลเป็น Excel (HTML Table format)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// เชื่อมต่อฐานข้อมูล
$host = 'localhost';
$username = 'root';
$password = 'Wxmujwsofu@1234';
$database = 'asset_management';

$conn = new mysqli($host, $username, $password, $database);
$conn->set_charset("utf8mb4");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// รับพารามิเตอร์
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';
$filter_serial = $_GET['filter_serial'] ?? '';

// สร้าง SQL query
$sql = "SELECT * FROM assets WHERE 1=1";
$params = [];
$conditions = [];

// เพิ่มเงื่อนไขการกรอง
if (!empty($search)) {
    $conditions[] = "(type LIKE ? OR brand LIKE ? OR model LIKE ? OR tag LIKE ? OR hostname LIKE ? OR department LIKE ? OR asset_id LIKE ? OR serial_number LIKE ?)";
    $searchTerm = '%' . $search . '%';
    for ($i = 0; $i < 8; $i++) {
        $params[] = $searchTerm;
    }
}

if (!empty($filter_type)) {
    $conditions[] = "type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_brand)) {
    $conditions[] = "brand = ?";
    $params[] = $filter_brand;
}

if (!empty($filter_department)) {
    $conditions[] = "department = ?";
    $params[] = $filter_department;
}

if (!empty($filter_status)) {
    $conditions[] = "status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_os)) {
    $conditions[] = "operating_system = ?";
    $params[] = $filter_os;
}

if (!empty($filter_serial)) {
    $conditions[] = "serial_number LIKE ?";
    $params[] = '%' . $filter_serial . '%';
}

// รวมเงื่อนไข
if (!empty($conditions)) {
    $sql .= " AND " . implode(" AND ", $conditions);
}

$sql .= " ORDER BY created_date DESC";

// เตรียม prepared statement
$stmt = $conn->prepare($sql);

// Bind parameters ถ้ามี
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();

// สร้างข้อมูลสำหรับแสดงเงื่อนไขที่ใช้
$filterInfo = [];
if (!empty($search)) $filterInfo[] = "ค้นหา: \"$search\"";
if (!empty($filter_type)) $filterInfo[] = "ประเภท: $filter_type";
if (!empty($filter_brand)) $filterInfo[] = "ยี่ห้อ: $filter_brand";
if (!empty($filter_department)) $filterInfo[] = "แผนก: $filter_department";
if (!empty($filter_status)) $filterInfo[] = "สถานะ: $filter_status";
if (!empty($filter_os)) $filterInfo[] = "ระบบปฏิบัติการ: $filter_os";
if (!empty($filter_serial)) $filterInfo[] = "หมายเลขเครื่อง: \"$filter_serial\"";

// สร้างชื่อไฟล์
$filename = "asset_data_" . date('Y-m-d_H-i-s');
if (!empty($filter_type)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_type);
if (!empty($filter_department)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_department);

// สร้าง Excel ในรูปแบบ HTML Table (เปิดได้ใน Excel)
header('Content-Type: application/vnd.ms-excel; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
header('Cache-Control: max-age=0');

// เริ่มต้น HTML สำหรับ Excel
echo "\xEF\xBB\xBF"; // UTF-8 BOM
echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #000; padding: 5px; text-align: left; }
        th { background-color: #4472C4; color: white; font-weight: bold; }
        tr:nth-child(even) { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h2>Asset Management Report</h2>
    <p>Generated on: ' . date('Y-m-d H:i:s') . '</p>';

if (!empty($filterInfo)) {
    echo '<p>Filters: ' . implode(", ", $filterInfo) . '</p>';
}

echo '<table>
    <thead>
        <tr>
            <th>ID</th>
            <th>Asset ID</th>
            <th>Type</th>
            <th>Brand</th>
            <th>Model</th>
            <th>Tag</th>
            <th>Department</th>
            <th>Status</th>
            <th>Hostname</th>
            <th>Operating System</th>
            <th>Serial Number</th>
            <th>Specifications</th>
            <th>Location</th>
            <th>Purchase Date</th>
            <th>Warranty Expiry</th>
            <th>Notes</th>
            <th>Created Date</th>
            <th>Updated Date</th>
        </tr>
    </thead>
    <tbody>';

// เพิ่มข้อมูล
while ($row = $result->fetch_assoc()) {
    echo '<tr>';
    echo '<td>' . htmlspecialchars($row['id'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['asset_id'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['type'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['brand'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['model'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['tag'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['department'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['status'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['hostname'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['operating_system'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['serial_number'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['specifications'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['location'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['purchase_date'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['warranty_expiry'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['notes'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['created_date'] ?? '') . '</td>';
    echo '<td>' . htmlspecialchars($row['updated_date'] ?? '') . '</td>';
    echo '</tr>';
}

echo '</tbody>
</table>
</body>
</html>';



// ปิดการเชื่อมต่อฐานข้อมูล
$stmt->close();
$conn->close();
?>
