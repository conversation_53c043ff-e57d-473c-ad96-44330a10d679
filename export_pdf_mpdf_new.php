<?php
// รายงาน PDF ด้วย mPDF - ดีไซน์ใหม่
error_reporting(E_ALL);
ini_set('display_errors', 1);

// สร้างการเชื่อมต่อฐานข้อมูลด้วย mysqli
$host = 'localhost';
$username = 'root';
$password = 'Wxmujwsofu@1234';
$database = 'asset_management';

$conn = new mysqli($host, $username, $password, $database);
$conn->set_charset("utf8mb4");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

require_once 'vendor/autoload.php';

// รับค่าการค้นหาและกรองจากหน้า index.php
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';
$filter_serial = $_GET['filter_serial'] ?? '';
$isDownload = isset($_GET['download']) && $_GET['download'] === 'true';

// สร้าง mPDF object
$mpdf = new mPDF([
    'mode' => 'utf-8',
    'format' => 'A4-L',
    'default_font_size' => 14,
    'default_font' => 'dejavusans',
    'margin_left' => 15,
    'margin_right' => 15,
    'margin_top' => 20,
    'margin_bottom' => 20,
    'margin_header' => 10,
    'margin_footer' => 10,
    'orientation' => 'L'
]);

// ตั้งค่าเอกสาร
$mpdf->SetTitle('Asset Management Report');
$mpdf->SetAuthor('Asset Management System');
$mpdf->SetCreator('Asset Management System');

// สร้าง SQL query ตามเงื่อนไขการกรอง
$sql = "SELECT * FROM assets WHERE 1=1";
$params = [];
$conditions = [];

// เพิ่มเงื่อนไขการกรอง
if (!empty($search)) {
    $conditions[] = "(type LIKE ? OR brand LIKE ? OR model LIKE ? OR tag LIKE ? OR hostname LIKE ? OR department LIKE ? OR asset_id LIKE ? OR serial_number LIKE ?)";
    $searchTerm = '%' . $search . '%';
    for ($i = 0; $i < 8; $i++) {
        $params[] = $searchTerm;
    }
}

if (!empty($filter_type)) {
    $conditions[] = "type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_brand)) {
    $conditions[] = "brand = ?";
    $params[] = $filter_brand;
}

if (!empty($filter_department)) {
    $conditions[] = "department = ?";
    $params[] = $filter_department;
}

if (!empty($filter_status)) {
    $conditions[] = "status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_os)) {
    $conditions[] = "operating_system = ?";
    $params[] = $filter_os;
}

if (!empty($filter_serial)) {
    $conditions[] = "serial_number LIKE ?";
    $params[] = '%' . $filter_serial . '%';
}

// รวมเงื่อนไข
if (!empty($conditions)) {
    $sql .= " AND " . implode(" AND ", $conditions);
}

$sql .= " ORDER BY created_date DESC";

// สร้างชื่อรายงาน
$title = "รายงานข้อมูล Asset Management";
$filterInfo = [];
if (!empty($search)) $filterInfo[] = "ค้นหา: $search";
if (!empty($filter_type)) $filterInfo[] = "ประเภท: $filter_type";
if (!empty($filter_brand)) $filterInfo[] = "Brand: $filter_brand";
if (!empty($filter_department)) $filterInfo[] = "แผนก: $filter_department";
if (!empty($filter_status)) $filterInfo[] = "สถานะ: $filter_status";
if (!empty($filter_os)) $filterInfo[] = "OS: $filter_os";
if (!empty($filter_serial)) $filterInfo[] = "Serial: $filter_serial";

// Get current date for report
$reportDate = date('d/m/Y H:i:s');
$reportDateThai = date('j', strtotime('now')) . ' ' . 
    ['', 'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
     'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'][date('n')] . 
    ' ' . (date('Y') + 543) . ' เวลา ' . date('H:i') . ' น.';

// เตรียม prepared statement
$stmt = $conn->prepare($sql);

// Bind parameters ถ้ามี
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();
$totalRecords = $result->num_rows;

// นับสถิติ
$stats = [
    'total' => $totalRecords,
    'active' => 0,
    'inactive' => 0,
    'maintenance' => 0
];

// นับสถิติสถานะ
$statsQuery = "SELECT status, COUNT(*) as count FROM assets";
if (!empty($conditions)) {
    $statsQuery .= " WHERE " . implode(" AND ", $conditions);
}
$statsQuery .= " GROUP BY status";

$statsStmt = $conn->prepare($statsQuery);
if (!empty($params)) {
    $statsStmt->bind_param($types, ...$params);
}
$statsStmt->execute();
$statsResult = $statsStmt->get_result();

while ($statRow = $statsResult->fetch_assoc()) {
    $status = strtolower($statRow['status']);
    if (strpos($status, 'ใช้งาน') !== false || strpos($status, 'active') !== false) {
        $stats['active'] = $statRow['count'];
    } elseif (strpos($status, 'ชำรุด') !== false || strpos($status, 'inactive') !== false) {
        $stats['inactive'] = $statRow['count'];
    } elseif (strpos($status, 'สำรอง') !== false || strpos($status, 'maintenance') !== false) {
        $stats['maintenance'] = $statRow['count'];
    }
}

// Start building the HTML content with modern design
$content = '
<style>
@page {
    margin: 10mm;
    footer: html_footer;
}

body {
    font-family: "TH Sarabun New", "Sarabun", "DejaVu Sans", sans-serif;
    font-size: 14pt;
    line-height: 1.4;
    color: #333;
    margin: 0;
    padding: 0;
}

.header-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    margin: -10mm -10mm 20px -10mm;
    text-align: center;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.header-title {
    font-size: 32pt;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}

.header-subtitle {
    font-size: 18pt;
    opacity: 0.9;
    margin-bottom: 8px;
}

.header-date {
    font-size: 14pt;
    opacity: 0.8;
    font-style: italic;
}

.stats-container {
    display: table;
    width: 100%;
    margin: 20px 0;
    border-spacing: 10px;
}

.stat-box {
    display: table-cell;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    width: 25%;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 28pt;
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12pt;
    opacity: 0.9;
}

.filter-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-left: 6px solid #2196f3;
    padding: 20px;
    margin: 25px 0;
    border-radius: 10px;
    box-shadow: 0 3px 8px rgba(0,0,0,0.1);
}

.filter-title {
    font-size: 16pt;
    font-weight: bold;
    color: #1976d2;
    margin-bottom: 10px;
}

.table-container {
    margin: 25px 0;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    border-radius: 15px;
    overflow: hidden;
    background: white;
}

table {
    border-collapse: collapse;
    width: 100%;
    font-size: 11pt;
}

.table-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.table-header th {
    padding: 15px 8px;
    text-align: center;
    font-weight: bold;
    font-size: 12pt;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    border: none;
}

td {
    padding: 12px 8px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.row-even {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.row-odd {
    background-color: white;
}

.sequence-col {
    text-align: center;
    font-weight: bold;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #333;
    border-radius: 8px;
    margin: 2px;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: bold;
    text-align: center;
    font-size: 10pt;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 2px solid #28a745;
}

.status-inactive {
    background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
    color: #721c24;
    border: 2px solid #dc3545;
}

.status-maintenance {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 2px solid #ffc107;
}

.summary-container {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    padding: 25px;
    margin-top: 30px;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.summary-title {
    font-size: 20pt;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.summary-grid {
    display: table;
    width: 100%;
    border-spacing: 15px;
}

.summary-item {
    display: table-cell;
    background: rgba(255,255,255,0.9);
    padding: 15px;
    border-radius: 12px;
    border-left: 6px solid #007bff;
    box-shadow: 0 3px 8px rgba(0,0,0,0.1);
    width: 50%;
}

.summary-label {
    font-weight: bold;
    color: #495057;
    font-size: 12pt;
}

.summary-value {
    color: #007bff;
    font-weight: bold;
    font-size: 13pt;
}

.page-break {
    page-break-after: always;
}

.watermark {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    font-size: 80pt;
    color: rgba(0,0,0,0.03);
    z-index: -1;
    font-weight: bold;
    letter-spacing: 5px;
}

.footer-text {
    text-align: center;
    font-size: 10pt;
    color: #666;
    border-top: 2px solid #007bff;
    padding-top: 10px;
    margin-top: 20px;
}
</style>

<div class="watermark">ASSET MANAGEMENT</div>

<div class="header-container">
    <div class="header-title">🏢 ASSET MANAGEMENT SYSTEM</div>
    <div class="header-subtitle">📊 รายงานข้อมูลทรัพย์สิน</div>
    <div class="header-date">📅 ' . $reportDateThai . '</div>
</div>
';

// เพิ่มสถิติ
$content .= '
<div class="stats-container">
    <div class="stat-box">
        <span class="stat-number">' . $stats['total'] . '</span>
        <span class="stat-label">📦 รายการทั้งหมด</span>
    </div>
    <div class="stat-box">
        <span class="stat-number">' . $stats['active'] . '</span>
        <span class="stat-label">✅ ใช้งาน</span>
    </div>
    <div class="stat-box">
        <span class="stat-number">' . $stats['inactive'] . '</span>
        <span class="stat-label">❌ ชำรุด</span>
    </div>
    <div class="stat-box">
        <span class="stat-number">' . $stats['maintenance'] . '</span>
        <span class="stat-label">🔧 สำรอง</span>
    </div>
</div>
';

// เพิ่มข้อมูลการกรอง
if (!empty($filterInfo)) {
    $content .= '
    <div class="filter-info">
        <div class="filter-title">🔍 เงื่อนไขการกรอง</div>
        <div>' . implode(" | ", $filterInfo) . '</div>
    </div>
    ';
}

// เริ่มตาราง
$content .= '
<div class="table-container">
<table>
<thead class="table-header">
<tr>
    <th width="5%">📋 ลำดับ</th>
    <th width="12%">🏢 แผนก</th>
    <th width="10%">📱 ประเภท</th>
    <th width="12%">🏷️ Asset ID</th>
    <th width="8%">🔖 Tag</th>
    <th width="12%">📦 Model</th>
    <th width="12%">🔢 S/N</th>
    <th width="12%">💻 Hostname</th>
    <th width="8%">⚙️ OS</th>
    <th width="9%">📊 Status</th>
</tr>
</thead>
<tbody>
';

$sequenceNumber = 1;
$rowsPerPage = 0;
$maxRowsPerPage = 20; // หน้าแรก 20 แถว
$isFirstPage = true;

// รีเซ็ต result pointer
$result->data_seek(0);

while ($row = $result->fetch_assoc()) {
    // ตรวจสอบการแบ่งหน้า
    $currentMaxRows = $isFirstPage ? 20 : 22;
    
    if ($rowsPerPage >= $currentMaxRows) {
        $content .= '
        </tbody>
        </table>
        </div>
        <div class="page-break"></div>
        
        <div class="header-container">
            <div class="header-title">🏢 ASSET MANAGEMENT SYSTEM</div>
            <div class="header-subtitle">📊 รายงานข้อมูลทรัพย์สิน (ต่อ)</div>
        </div>
        
        <div class="table-container">
        <table>
        <thead class="table-header">
        <tr>
            <th width="5%">📋 ลำดับ</th>
            <th width="12%">🏢 แผนก</th>
            <th width="10%">📱 ประเภท</th>
            <th width="12%">🏷️ Asset ID</th>
            <th width="8%">🔖 Tag</th>
            <th width="12%">📦 Model</th>
            <th width="12%">🔢 S/N</th>
            <th width="12%">💻 Hostname</th>
            <th width="8%">⚙️ OS</th>
            <th width="9%">📊 Status</th>
        </tr>
        </thead>
        <tbody>
        ';
        
        $rowsPerPage = 0;
        $isFirstPage = false;
    }
    
    // กำหนดสีแถว
    $rowClass = ($sequenceNumber % 2 == 0) ? 'row-even' : 'row-odd';
    
    // กำหนดสี status
    $status = strtolower($row['status'] ?? '');
    $statusClass = 'status-maintenance';
    if (strpos($status, 'ใช้งาน') !== false || strpos($status, 'active') !== false) {
        $statusClass = 'status-active';
    } elseif (strpos($status, 'ชำรุด') !== false || strpos($status, 'inactive') !== false) {
        $statusClass = 'status-inactive';
    }
    
    // ข้อมูลแถว
    $dept = htmlspecialchars(substr($row['department'] ?? '', 0, 15));
    $type = htmlspecialchars($row['type'] ?? '');
    $asset_id = htmlspecialchars($row['asset_id'] ?? '');
    $tag = htmlspecialchars($row['tag'] ?? '');
    $model = htmlspecialchars(substr($row['model'] ?? '', 0, 15));
    $serial = htmlspecialchars($row['serial_number'] ?? '');
    $hostname = htmlspecialchars(substr($row['hostname'] ?? '', 0, 15));
    $os = htmlspecialchars(substr($row['operating_system'] ?? '', 0, 10));
    $statusText = htmlspecialchars($row['status'] ?? '');
    
    $content .= '
    <tr class="' . $rowClass . '">
        <td class="sequence-col">' . $sequenceNumber . '</td>
        <td>' . $dept . '</td>
        <td>' . $type . '</td>
        <td><strong>' . $asset_id . '</strong></td>
        <td>' . $tag . '</td>
        <td>' . $model . '</td>
        <td>' . $serial . '</td>
        <td>' . $hostname . '</td>
        <td>' . $os . '</td>
        <td><span class="status-badge ' . $statusClass . '">' . $statusText . '</span></td>
    </tr>
    ';
    
    $sequenceNumber++;
    $rowsPerPage++;
}

// ปิดตาราง
$content .= '
</tbody>
</table>
</div>
';

// เพิ่มส่วนสรุป
$content .= '
<div class="summary-container">
    <div class="summary-title">📋 สรุปรายงาน</div>
    <div class="summary-grid">
        <div class="summary-item">
            <div class="summary-label">📊 จำนวนรายการทั้งหมด:</div>
            <div class="summary-value">' . $totalRecords . ' รายการ</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">🔍 เงื่อนไขการกรอง:</div>
            <div class="summary-value">' . (!empty($filterInfo) ? implode(", ", $filterInfo) : "ทั้งหมด") . '</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">📅 วันที่ออกรายงาน:</div>
            <div class="summary-value">' . $reportDateThai . '</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">👤 ผู้ออกรายงาน:</div>
            <div class="summary-value">Asset Management System</div>
        </div>
    </div>
</div>

<div class="footer-text">
    🏢 Asset Management System | 📧 <EMAIL> | 📞 02-xxx-xxxx<br>
    © ' . date('Y') . ' All Rights Reserved | Generated on ' . date('Y-m-d H:i:s') . '
</div>
';

// เขียน HTML ลง PDF
$mpdf->WriteHTML($content);

// สร้างชื่อไฟล์
$filename = "asset_report_" . date('d-m-Y_H-i-s');
if (!empty($filter_type)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_type);
if (!empty($filter_brand)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_brand);
if (!empty($filter_department)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_department);

// กำหนดรูปแบบการแสดงผล
$outputMode = $isDownload ? 'D' : 'I';
$mpdf->Output($filename.'.pdf', $outputMode);

// ปิดการเชื่อมต่อฐานข้อมูล
$stmt->close();
$statsStmt->close();
$conn->close();
?>
