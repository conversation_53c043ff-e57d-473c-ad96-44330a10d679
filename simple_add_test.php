<?php
// ทดสอบการเพิ่ม Asset แบบง่ายที่สุด
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🧪 Simple Add Asset Test</h2>";

// เชื่อมต่อฐานข้อมูลโดยตรง
try {
    $host = 'localhost';
    $dbname = 'asset_management';
    $username = 'root';
    $password = 'Wxmujwsofu@1234';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Database connected</p>";
    
} catch (Exception $e) {
    die("<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>");
}

// ตรวจสอบตาราง
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM assets");
    $count = $stmt->fetchColumn();
    echo "<p style='color: green;'>✅ Assets table exists with $count records</p>";
} catch (Exception $e) {
    die("<p style='color: red;'>❌ Assets table error: " . $e->getMessage() . "</p>");
}

// แสดงโครงสร้างตาราง
echo "<h3>Table Structure:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll();
    
    echo "<ul>";
    foreach ($columns as $col) {
        echo "<li><strong>{$col['Field']}</strong> - {$col['Type']} " . 
             ($col['Null'] == 'YES' ? '(nullable)' : '(required)') . "</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error getting table structure: " . $e->getMessage() . "</p>";
}

// ทดสอบการเพิ่มข้อมูล
if ($_POST && isset($_POST['add_asset'])) {
    echo "<h3>🧪 Adding Asset...</h3>";
    
    $type = $_POST['type'] ?? '';
    $brand = $_POST['brand'] ?? '';
    $status = $_POST['status'] ?? 'ใช้งาน';
    
    echo "<p>Data to insert:</p>";
    echo "<ul>";
    echo "<li>Type: '$type'</li>";
    echo "<li>Brand: '$brand'</li>";
    echo "<li>Status: '$status'</li>";
    echo "</ul>";
    
    if (empty($type)) {
        echo "<p style='color: red;'>❌ Type is required</p>";
    } else {
        try {
            // ใช้ SQL แบบง่าย
            $sql = "INSERT INTO assets (type, brand, status, date_added) VALUES (?, ?, ?, NOW())";
            $stmt = $pdo->prepare($sql);
            
            echo "<p>SQL: $sql</p>";
            
            $result = $stmt->execute([$type, $brand, $status]);
            
            if ($result) {
                $newId = $pdo->lastInsertId();
                echo "<p style='color: green;'>✅ Asset added successfully! ID: $newId</p>";
                
                // แสดงข้อมูลที่เพิ่ม
                $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
                $checkStmt->execute([$newId]);
                $newAsset = $checkStmt->fetch();
                
                echo "<h4>Added Asset Data:</h4>";
                echo "<pre>" . print_r($newAsset, true) . "</pre>";
                
            } else {
                echo "<p style='color: red;'>❌ Failed to add asset</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error adding asset: " . $e->getMessage() . "</p>";
            echo "<p style='color: red;'>SQL State: " . $e->getCode() . "</p>";
        }
    }
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Add Asset Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #28a745;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .form-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>

<div class="form-section">
    <h3>➕ Add New Asset</h3>
    <form method="POST">
        <div class="form-group">
            <label for="type">Type *</label>
            <select id="type" name="type" required>
                <option value="">เลือก Type</option>
                <option value="Desktop">Desktop</option>
                <option value="Laptop">Laptop</option>
                <option value="Monitor">Monitor</option>
                <option value="Printer">Printer</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="brand">Brand</label>
            <select id="brand" name="brand">
                <option value="">เลือก Brand</option>
                <option value="Dell">Dell</option>
                <option value="Lenovo">Lenovo</option>
                <option value="HP">HP</option>
                <option value="Apple">Apple</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="status">Status</label>
            <select id="status" name="status">
                <option value="ใช้งาน">ใช้งาน</option>
                <option value="ชำรุด">ชำรุด</option>
                <option value="สำรอง">สำรอง</option>
            </select>
        </div>
        
        <div class="form-group">
            <input type="hidden" name="add_asset" value="1">
            <button type="submit">เพิ่ม Asset</button>
        </div>
    </form>
</div>

<div style="margin: 20px 0;">
    <a href="add_asset.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔙 กลับหน้า Add Asset</a>
    <a href="index.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 กลับหน้าหลัก</a>
</div>

</body>
</html>
