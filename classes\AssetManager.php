<?php
// Include config file if not already included
if (!defined('DB_HOST')) {
    require_once __DIR__ . '/../includes/config.php';
}

require_once 'classes/BackupManager.php';
require_once 'classes/TelegramNotifier.php';

class AssetManager {
    private $pdo;
    private $backupManager;
    private $telegramNotifier;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->backupManager = new BackupManager($pdo);
        $this->telegramNotifier = new TelegramNotifier();
    }
    
    /**
     * สร้าง Asset ใหม่
     */
    public function createAsset($data) {
        try {
            // ตรวจสอบข้อมูลจำเป็น
            if (empty($data['type'])) {
                throw new Exception('ต้องระบุประเภท Asset');
            }
            
            // เตรียมข้อมูลสำหรับ insert (ตรงกับ database schema)
            $insertData = [
                'type' => $data['type'],
                'brand' => $data['brand'] ?? null,
                'model' => $data['model'] ?? null,
                'tag' => !empty($data['tag']) ? $data['tag'] : null,
                'department' => $data['department'] ?? null,
                'status' => $data['status'] ?? 'ใช้งาน',
                'hostname' => $data['hostname'] ?? null,
                'ip_address' => $data['ip_address'] ?? null,
                'operating_system' => $data['operating_system'] ?? null,
                'serial_number' => $data['serial_number'] ?? null,
                'asset_id' => !empty($data['asset_id']) ? $data['asset_id'] : null,
                'warranty_expire' => !empty($data['warranty_expire']) ? $data['warranty_expire'] : null,
                'description' => $data['description'] ?? null,
                'set_name' => $data['set_name'] ?? null,
                'created_by' => $data['created_by'] ?? null,
                'updated_by' => $data['updated_by'] ?? $data['created_by'] ?? null
            ];
            
            // สร้าง SQL query
            $fields = array_keys($insertData);
            $placeholders = ':' . implode(', :', $fields);
            $sql = "INSERT INTO assets (" . implode(', ', $fields) . ") VALUES (" . $placeholders . ")";
            
            $stmt = $this->pdo->prepare($sql);
            
            // Execute query
            if ($stmt->execute($insertData)) {
                $newId = $this->pdo->lastInsertId();

                // สร้าง Auto Backup หลังจากเพิ่มข้อมูล (ไม่ให้ error หยุดการทำงาน)
                try {
                    $this->backupManager->createAutoBackup(
                        'CREATE',
                        "เพิ่ม Asset ใหม่ ID: {$newId} - {$data['type']} {$data['brand']} {$data['model']}"
                    );
                } catch (Exception $e) {
                    // Log error แต่ไม่หยุดการทำงาน
                    error_log("Backup Error: " . $e->getMessage());
                }

                // ส่งแจ้งเตือน Telegram (ไม่ให้ error หยุดการทำงาน)
                try {
                    $createdBy = getCurrentUserFullName() ?? 'ไม่ระบุ';
                    $this->telegramNotifier->notifyAssetCreated($insertData, $createdBy);
                } catch (Exception $e) {
                    // Log error แต่ไม่หยุดการทำงาน
                    error_log("Telegram Error: " . $e->getMessage());
                }

                return $newId;
            } else {
                throw new Exception('ไม่สามารถสร้าง Asset ได้');
            }
            
        } catch (PDOException $e) {
            throw new Exception('Database error: ' . $e->getMessage());
        }
    }
    
    /**
     * อัพเดท Asset
     */
    public function updateAsset($id, $data) {
        try {
            // ตรวจสอบว่า Asset มีอยู่จริง
            if (!$this->assetExists($id)) {
                throw new Exception('ไม่พบ Asset ที่ต้องการแก้ไข');
            }
            
            // เตรียมข้อมูลสำหรับ update
            $updateData = [];
            $allowedFields = [
                'type', 'brand', 'model', 'tag', 'department', 'status',
                'hostname', 'operating_system', 'serial_number', 'asset_id',
                'warranty_expire', 'description', 'set_name', 'updated_by'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }
            
            // เพิ่ม updated_date
            $updateData['updated_date'] = date('Y-m-d H:i:s');
            
            if (empty($updateData)) {
                throw new Exception('ไม่มีข้อมูลที่ต้องการอัพเดท');
            }
            
            // สร้าง SQL query
            $setParts = [];
            foreach (array_keys($updateData) as $field) {
                $setParts[] = "$field = :$field";
            }
            $sql = "UPDATE assets SET " . implode(', ', $setParts) . " WHERE id = :id";
            
            $updateData['id'] = $id;
            $stmt = $this->pdo->prepare($sql);

            $result = $stmt->execute($updateData);

            if ($result) {
                // ดึงข้อมูล Asset เพื่อใช้ใน backup description
                $asset = $this->getAsset($id);

                // สร้าง Auto Backup หลังจากแก้ไขข้อมูล (ไม่ให้ error หยุดการทำงาน)
                try {
                    $this->backupManager->createAutoBackup(
                        'UPDATE',
                        "แก้ไข Asset ID: {$id} - {$asset['type']} {$asset['brand']} {$asset['model']}"
                    );
                } catch (Exception $e) {
                    // Log error แต่ไม่หยุดการทำงาน
                    error_log("Backup Error: " . $e->getMessage());
                }

                // ส่งแจ้งเตือน Telegram (ไม่ให้ error หยุดการทำงาน)
                try {
                    $updatedBy = getCurrentUserFullName() ?? 'ไม่ระบุ';
                    $this->telegramNotifier->notifyAssetUpdated($id, $asset, $updatedBy);
                } catch (Exception $e) {
                    // Log error แต่ไม่หยุดการทำงาน
                    error_log("Telegram Error: " . $e->getMessage());
                }
            }

            return $result;
            
        } catch (PDOException $e) {
            throw new Exception('Database error: ' . $e->getMessage());
        }
    }
    
    /**
     * ลบ Asset
     */
    public function deleteAsset($id) {
        try {
            // ตรวจสอบว่า Asset มีอยู่จริง
            if (!$this->assetExists($id)) {
                throw new Exception('ไม่พบ Asset ที่ต้องการลบ');
            }

            // ดึงข้อมูล Asset ก่อนลบ เพื่อใช้ใน backup description
            $asset = $this->getAsset($id);

            $stmt = $this->pdo->prepare("DELETE FROM assets WHERE id = ?");
            $result = $stmt->execute([$id]);

            if ($result && $asset) {
                // สร้าง Auto Backup หลังจากลบข้อมูล (ไม่ให้ error หยุดการทำงาน)
                try {
                    $this->backupManager->createAutoBackup(
                        'DELETE',
                        "ลบ Asset ID: {$id} - {$asset['type']} {$asset['brand']} {$asset['model']}"
                    );
                } catch (Exception $e) {
                    // Log error แต่ไม่หยุดการทำงาน
                    error_log("Backup Error: " . $e->getMessage());
                }

                // ส่งแจ้งเตือน Telegram (ไม่ให้ error หยุดการทำงาน)
                try {
                    $deletedBy = getCurrentUserFullName() ?? 'ไม่ระบุ';
                    $this->telegramNotifier->notifyAssetDeleted($asset, $deletedBy);
                } catch (Exception $e) {
                    // Log error แต่ไม่หยุดการทำงาน
                    error_log("Telegram Error: " . $e->getMessage());
                }
            }

            return $result;

        } catch (PDOException $e) {
            throw new Exception('Database error: ' . $e->getMessage());
        }
    }
    
    /**
     * ดึงข้อมูล Asset ตาม ID
     */
    public function getAsset($id) {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM assets WHERE id = ?");
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            throw new Exception('Database error: ' . $e->getMessage());
        }
    }
    
    /**
     * ดึงรายการ Assets ทั้งหมด
     */
    public function getAllAssets($search = '', $type = '', $brand = '', $status = '', $os = '', $department = '', $serial = '') {
        try {
            $sql = "SELECT * FROM assets WHERE 1=1";
            $params = [];

            // เพิ่มเงื่อนไขการกรอง
            if (!empty($search)) {
                $sql .= " AND (type LIKE ? OR brand LIKE ? OR model LIKE ? OR tag LIKE ? OR hostname LIKE ? OR department LIKE ? OR asset_id LIKE ? OR serial_number LIKE ?)";
                $searchTerm = '%' . $search . '%';
                $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
            }

            if (!empty($type)) {
                $sql .= " AND type = ?";
                $params[] = $type;
            }

            if (!empty($brand)) {
                $sql .= " AND brand = ?";
                $params[] = $brand;
            }

            if (!empty($department)) {
                $sql .= " AND department = ?";
                $params[] = $department;
            }

            if (!empty($status)) {
                $sql .= " AND status = ?";
                $params[] = $status;
            }

            if (!empty($os)) {
                $sql .= " AND operating_system = ?";
                $params[] = $os;
            }

            if (!empty($serial)) {
                $sql .= " AND serial_number LIKE ?";
                $params[] = '%' . $serial . '%';
            }

            $sql .= " ORDER BY created_date DESC";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            throw new Exception('Database error: ' . $e->getMessage());
        }
    }
    
    /**
     * ตรวจสอบว่า Asset มีอยู่จริงหรือไม่
     */
    private function assetExists($id) {
        try {
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM assets WHERE id = ?");
            $stmt->execute([$id]);
            return $stmt->fetchColumn() > 0;
            
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * ดึงสถิติ Assets
     */
    public function getAssetStats() {
        try {
            $stats = [];
            
            // นับจำนวนทั้งหมด
            $stmt = $this->pdo->query("SELECT COUNT(*) FROM assets");
            $stats['total'] = $stmt->fetchColumn();
            
            // นับตามสถานะ
            $stmt = $this->pdo->query("SELECT status, COUNT(*) as count FROM assets GROUP BY status");
            $statusCounts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            $stats['active'] = $statusCounts['ใช้งาน'] ?? 0;
            $stats['damaged'] = $statusCounts['ชำรุด'] ?? 0;
            $stats['spare'] = $statusCounts['สำรอง'] ?? 0;
            
            return $stats;
            
        } catch (PDOException $e) {
            return [
                'total' => 0,
                'active' => 0,
                'damaged' => 0,
                'spare' => 0
            ];
        }
    }
    
    /**
     * หมายเหตุ: ลบ method isAssetIdDuplicate เพราะระบบอนุญาตให้ Asset ID ซ้ำได้แล้ว
     */
    
    /**
     * ดึงรายการ options สำหรับ dropdown
     */
    public function getFieldOptions($field) {
        try {
            $stmt = $this->pdo->prepare("SELECT DISTINCT $field FROM assets WHERE $field IS NOT NULL AND $field != '' ORDER BY $field");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_COLUMN);

        } catch (PDOException $e) {
            return [];
        }
    }

    /**
     * ดึงรายการประเภท Asset
     */
    public function getAssetTypes() {
        return $this->getFieldOptions('type');
    }

    /**
     * ดึงรายการ Brand
     */
    public function getAssetBrands() {
        return $this->getFieldOptions('brand');
    }

    /**
     * ดึงรายการแผนก
     */
    public function getAssetDepartments() {
        return $this->getFieldOptions('department');
    }

    /**
     * ดึงรายการ Operating System
     */
    public function getAssetOperatingSystems() {
        return $this->getFieldOptions('operating_system');
    }
}
?>
