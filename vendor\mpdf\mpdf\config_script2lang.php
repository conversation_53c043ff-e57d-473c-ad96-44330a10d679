<?php

$this->script2lang = array(
	/* European */
	UCDN::SCRIPT_LATIN => 'und-Latn',
	UCDN::SCRIPT_ARMENIAN => 'hy',
	UCDN::SCRIPT_CYRILLIC => 'und-Cyrl',
	UCDN::SCRIPT_GEORGIAN => 'ka',
	UCDN::SCRIPT_GREEK => 'el',
	UCDN::SCRIPT_COPTIC => 'cop',
	UCDN::SCRIPT_GOTHIC => 'got',
	UCDN::SCRIPT_CYPRIOT => 'und-Cprt',
	UCDN::SCRIPT_GLAGOLITIC => 'und-Glag',
	UCDN::SCRIPT_LINEAR_B => 'und-Linb',
	UCDN::SCRIPT_OGHAM => 'und-Ogam',
	UCDN::SCRIPT_OLD_ITALIC => 'und-Ital',
	UCDN::SCRIPT_RUNIC => 'und-Runr',
	UCDN::SCRIPT_SHAVIAN => 'und-Shaw',
	/* African */
	UCDN::SCRIPT_ETHIOPIC => 'und-Ethi',
	UCDN::SCRIPT_NKO => 'nqo',
	UCDN::SCRIPT_BAMUM => 'bax',
	UCDN::SCRIPT_VAI => 'vai',
	UCDN::SCRIPT_EGYPTIAN_HIEROGLYPHS => 'und-Egyp',
	UCDN::SCRIPT_MEROITIC_CURSIVE => 'und-Merc',
	UCDN::SCRIPT_MEROITIC_HIEROGLYPHS => 'und-Mero',
	UCDN::SCRIPT_OSMANYA => 'und-Osma',
	UCDN::SCRIPT_TIFINAGH => 'und-Tfng',
	/* Middle Eastern */
	UCDN::SCRIPT_ARABIC => 'und-Arab',
	UCDN::SCRIPT_HEBREW => 'he',
	UCDN::SCRIPT_SYRIAC => 'syr',
	UCDN::SCRIPT_IMPERIAL_ARAMAIC => 'arc',
	UCDN::SCRIPT_AVESTAN => 'ae',
	UCDN::SCRIPT_CARIAN => 'xcr',
	UCDN::SCRIPT_LYCIAN => 'xlc',
	UCDN::SCRIPT_LYDIAN => 'xld',
	UCDN::SCRIPT_MANDAIC => 'mid',
	UCDN::SCRIPT_OLD_PERSIAN => 'peo',
	UCDN::SCRIPT_PHOENICIAN => 'phn',
	UCDN::SCRIPT_SAMARITAN => 'smp',
	UCDN::SCRIPT_UGARITIC => 'uga',
	UCDN::SCRIPT_CUNEIFORM => 'und-Xsux',
	UCDN::SCRIPT_OLD_SOUTH_ARABIAN => 'und-Sarb',
	UCDN::SCRIPT_INSCRIPTIONAL_PARTHIAN => 'und-Prti',
	UCDN::SCRIPT_INSCRIPTIONAL_PAHLAVI => 'und-Phli',
	/* Central Asian */
	UCDN::SCRIPT_MONGOLIAN => 'mn',
	UCDN::SCRIPT_TIBETAN => 'bo',
	UCDN::SCRIPT_OLD_TURKIC => 'und-Orkh',
	UCDN::SCRIPT_PHAGS_PA => 'und-Phag',
	/* South Asian */
	UCDN::SCRIPT_BENGALI => 'bn',
	UCDN::SCRIPT_DEVANAGARI => 'hi',
	UCDN::SCRIPT_GUJARATI => 'gu',
	UCDN::SCRIPT_GURMUKHI => 'pa',
	UCDN::SCRIPT_KANNADA => 'kn',
	UCDN::SCRIPT_MALAYALAM => 'ml',
	UCDN::SCRIPT_ORIYA => 'or',
	UCDN::SCRIPT_SINHALA => 'si',
	UCDN::SCRIPT_TAMIL => 'ta',
	UCDN::SCRIPT_TELUGU => 'te',
	UCDN::SCRIPT_CHAKMA => 'ccp',
	UCDN::SCRIPT_LEPCHA => 'lep',
	UCDN::SCRIPT_LIMBU => 'lif',
	UCDN::SCRIPT_OL_CHIKI => 'sat',
	UCDN::SCRIPT_SAURASHTRA => 'saz',
	UCDN::SCRIPT_SYLOTI_NAGRI => 'syl',
	UCDN::SCRIPT_TAKRI => 'dgo',
	UCDN::SCRIPT_THAANA => 'dv',
	UCDN::SCRIPT_BRAHMI => 'und-Brah',
	UCDN::SCRIPT_KAITHI => 'und-Kthi',
	UCDN::SCRIPT_KHAROSHTHI => 'und-Khar',
	UCDN::SCRIPT_MEETEI_MAYEK => 'und-Mtei', /* or omp-Mtei */
	UCDN::SCRIPT_SHARADA => 'und-Shrd',
	UCDN::SCRIPT_SORA_SOMPENG => 'und-Sora',
	/* South East Asian */
	UCDN::SCRIPT_KHMER => 'km',
	UCDN::SCRIPT_LAO => 'lo',
	UCDN::SCRIPT_MYANMAR => 'my',
	UCDN::SCRIPT_THAI => 'th',
	UCDN::SCRIPT_BALINESE => 'ban',
	UCDN::SCRIPT_BATAK => 'bya',
	UCDN::SCRIPT_BUGINESE => 'bug',
	UCDN::SCRIPT_CHAM => 'cjm',
	UCDN::SCRIPT_JAVANESE => 'jv',
	UCDN::SCRIPT_KAYAH_LI => 'und-Kali',
	UCDN::SCRIPT_REJANG => 'und-Rjng',
	UCDN::SCRIPT_SUNDANESE => 'su',
	UCDN::SCRIPT_TAI_LE => 'tdd',
	UCDN::SCRIPT_TAI_THAM => 'und-Lana',
	UCDN::SCRIPT_TAI_VIET => 'blt',
	UCDN::SCRIPT_NEW_TAI_LUE => 'und-Talu',
	/* Phillipine */
	UCDN::SCRIPT_BUHID => 'bku',
	UCDN::SCRIPT_HANUNOO => 'hnn',
	UCDN::SCRIPT_TAGALOG => 'tl',
	UCDN::SCRIPT_TAGBANWA => 'tbw',
	/* East Asian */
	UCDN::SCRIPT_HAN => 'und-Hans', // und-Hans (simplified) or und-Hant (Traditional)
	UCDN::SCRIPT_HANGUL => 'ko',
	UCDN::SCRIPT_HIRAGANA => 'ja',
	UCDN::SCRIPT_KATAKANA => 'ja',
	UCDN::SCRIPT_LISU => 'lis',
	UCDN::SCRIPT_BOPOMOFO => 'und-Bopo', // zh-CN, zh-TW, zh-HK
	UCDN::SCRIPT_MIAO => 'und-Plrd',
	UCDN::SCRIPT_YI => 'und-Yiii',
	/* American */
	UCDN::SCRIPT_CHEROKEE => 'chr',
	UCDN::SCRIPT_CANADIAN_ABORIGINAL => 'cr',
	UCDN::SCRIPT_DESERET => 'und-Dsrt',
	/* Other */
	UCDN::SCRIPT_BRAILLE => 'und-Brai',
);

// Used in mpdf.php function AutoFont() to detect specific languages:
// VIETNAMESE
$this->viet = "\x{01A0}\x{01A1}\x{01AF}\x{01B0}\x{1EA0}-\x{1EF1}";

// PASHTO, SINDHI, URDU, ARABIC, PERSIAN
$this->persian = "\x{067E}\x{0686}\x{0698}\x{06AF}";
$this->urdu = "\x{0679}\x{0688}\x{0691}\x{06BA}\x{06BE}\x{06C1}\x{06D2}";
$this->pashto = "\x{067C}\x{0681}\x{0685}\x{0689}\x{0693}\x{0696}\x{069A}\x{06BC}\x{06D0}"; // ? and U+06AB, U+06CD
$this->sindhi = "\x{067A}\x{067B}\x{067D}\x{067F}\x{0680}\x{0684}\x{068D}\x{068A}\x{068F}\x{068C}\x{0687}\x{0683}\x{0699}\x{06AA}\x{06A6}\x{06BB}\x{06B1}\x{06B3}";
