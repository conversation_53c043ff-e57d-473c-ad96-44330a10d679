<?php
require_once 'includes/auth.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>🧪 ทดสอบ IPv4 Validation</h2>";

// ฟังก์ชันทดสอบ IP validation
function testIPValidation($ip, $description = '') {
    echo "<div style='background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4>" . htmlspecialchars($description ?: $ip) . "</h4>";
    echo "<p><strong>IP:</strong> " . htmlspecialchars($ip) . "</p>";
    
    // ทดสอบ filter_var แบบต่างๆ
    $tests = [
        'FILTER_VALIDATE_IP' => filter_var($ip, FILTER_VALIDATE_IP),
        'FILTER_VALIDATE_IP + IPV4' => filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4),
        'FILTER_VALIDATE_IP + IPV6' => filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6),
        'is_string' => is_string($ip),
        'preg_match IPv4' => preg_match('/^\d+\.\d+\.\d+\.\d+$/', $ip)
    ];
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #e9ecef;'><th>Test</th><th>Result</th><th>Value</th></tr>";
    
    foreach ($tests as $testName => $result) {
        $resultText = $result ? '✅ TRUE' : '❌ FALSE';
        $resultValue = $result === false ? 'false' : ($result === true ? 'true' : htmlspecialchars($result));
        $rowColor = $result ? '#d4edda' : '#f8d7da';
        
        echo "<tr style='background: {$rowColor};'>";
        echo "<td><strong>{$testName}</strong></td>";
        echo "<td>{$resultText}</td>";
        echo "<td>{$resultValue}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
}

// ทดสอบ IP addresses ต่างๆ
echo "<h3>📋 ทดสอบ IP Addresses ต่างๆ:</h3>";

$testIPs = [
    // Valid IPv4
    ['***********', 'Valid IPv4 - Private'],
    ['*******', 'Valid IPv4 - Google DNS'],
    ['*******', 'Valid IPv4 - Cloudflare DNS'],
    ['127.0.0.1', 'Valid IPv4 - Localhost'],
    ['0.0.0.0', 'Valid IPv4 - All zeros'],
    ['***************', 'Valid IPv4 - Broadcast'],
    
    // Invalid IPv4
    ['256.1.1.1', 'Invalid IPv4 - Out of range'],
    ['192.168.1', 'Invalid IPv4 - Incomplete'],
    ['***********.1', 'Invalid IPv4 - Too many octets'],
    ['************', 'Invalid IPv4 - Leading zeros'],
    
    // IPv6
    ['2001:db8::1', 'IPv6 Address'],
    ['::1', 'IPv6 Localhost'],
    
    // Non-IP
    ['google.com', 'Hostname'],
    ['not-an-ip', 'Random string'],
    ['', 'Empty string'],
    ['***********/24', 'CIDR notation'],
];

foreach ($testIPs as $test) {
    testIPValidation($test[0], $test[1]);
}

// ทดสอบ gethostbyname กับ hostnames ต่างๆ
echo "<h3>🔍 ทดสอบ gethostbyname กับ Hostnames:</h3>";

$testHostnames = [
    'google.com',
    'facebook.com',
    'github.com',
    'nonexistent-hostname-12345.com',
    'localhost'
];

foreach ($testHostnames as $hostname) {
    echo "<div style='background: #fff3cd; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4>Hostname: " . htmlspecialchars($hostname) . "</h4>";
    
    $start = microtime(true);
    $result = gethostbyname($hostname);
    $time = microtime(true) - $start;
    
    echo "<p><strong>gethostbyname result:</strong> " . htmlspecialchars($result) . "</p>";
    echo "<p><strong>Time:</strong> " . number_format($time * 1000, 2) . " ms</p>";
    echo "<p><strong>Same as hostname:</strong> " . ($result === $hostname ? '✅ Yes (not resolved)' : '❌ No (resolved)') . "</p>";
    
    if ($result !== $hostname) {
        echo "<p><strong>Valid IPv4:</strong> " . (filter_var($result, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Valid IP (any):</strong> " . (filter_var($result, FILTER_VALIDATE_IP) ? '✅ Yes' : '❌ No') . "</p>";
    }
    echo "</div>";
}

// ทดสอบ regex patterns
echo "<h3>🔧 ทดสอบ Regex Patterns สำหรับ IPv4:</h3>";

$regexPatterns = [
    '/^\d+\.\d+\.\d+\.\d+$/' => 'Simple IPv4 pattern',
    '/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/' => 'Strict IPv4 pattern',
    '/\b(?:\d{1,3}\.){3}\d{1,3}\b/' => 'IPv4 anywhere in string'
];

$testStrings = [
    '***********',
    '256.1.1.1',
    'Address: ***********',
    'Server: *******#53',
    'Name: google.com\nAddress: **************'
];

foreach ($regexPatterns as $pattern => $description) {
    echo "<div style='background: #e7f3ff; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4>" . htmlspecialchars($description) . "</h4>";
    echo "<p><strong>Pattern:</strong> <code>" . htmlspecialchars($pattern) . "</code></p>";
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #e9ecef;'><th>Test String</th><th>Match</th><th>Captured</th></tr>";
    
    foreach ($testStrings as $testString) {
        $matches = [];
        $result = preg_match($pattern, $testString, $matches);
        $resultText = $result ? '✅ Match' : '❌ No match';
        $captured = isset($matches[0]) ? htmlspecialchars($matches[0]) : '-';
        $rowColor = $result ? '#d4edda' : '#f8d7da';
        
        echo "<tr style='background: {$rowColor};'>";
        echo "<td>" . htmlspecialchars($testString) . "</td>";
        echo "<td>{$resultText}</td>";
        echo "<td>{$captured}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
}

// แสดงข้อมูลระบบ
echo "<h3>ℹ️ ข้อมูลระบบ:</h3>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>Operating System:</strong> " . PHP_OS . "</li>";
echo "<li><strong>filter_var available:</strong> " . (function_exists('filter_var') ? '✅ Yes' : '❌ No') . "</li>";
echo "<li><strong>FILTER_VALIDATE_IP:</strong> " . (defined('FILTER_VALIDATE_IP') ? '✅ Defined' : '❌ Not defined') . "</li>";
echo "<li><strong>FILTER_FLAG_IPV4:</strong> " . (defined('FILTER_FLAG_IPV4') ? '✅ Defined' : '❌ Not defined') . "</li>";
echo "<li><strong>FILTER_FLAG_IPV6:</strong> " . (defined('FILTER_FLAG_IPV6') ? '✅ Defined' : '❌ Not defined') . "</li>";
echo "</ul>";

// สรุปการใช้งาน
echo "<h3>📝 สรุปการใช้งาน IPv4 Validation:</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<h4>✅ แนะนำ:</h4>";
echo "<p><code>filter_var(\$ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)</code></p>";
echo "<p><strong>เหตุผล:</strong></p>";
echo "<ul>";
echo "<li>ตรวจสอบ IPv4 เท่านั้น</li>";
echo "<li>ไม่รับ IPv6</li>";
echo "<li>ตรวจสอบ range ที่ถูกต้อง (0-255)</li>";
echo "<li>ไม่รับ leading zeros</li>";
echo "<li>Built-in PHP function</li>";
echo "</ul>";

echo "<h4>❌ ไม่แนะนำ:</h4>";
echo "<p><code>filter_var(\$ip, FILTER_VALIDATE_IP)</code> - รับทั้ง IPv4 และ IPv6</p>";
echo "<p><code>preg_match('/^\d+\.\d+\.\d+\.\d+$/', \$ip)</code> - ไม่ตรวจสอบ range</p>";
echo "</div>";
?>

<hr>
<p>
    <a href="simple_nslookup_test.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        🧪 ทดสอบ NSLOOKUP
    </a>
    <a href="test_nslookup.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🔍 ทดสอบ NSLOOKUP แบบเต็ม
    </a>
    <a href="ip_lookup_manager.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🔧 จัดการ IP Lookup
    </a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th { background: #f8f9fa; }
code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
