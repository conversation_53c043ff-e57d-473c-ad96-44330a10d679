<?php
// Database configuration for Asset Management System

// Include config file if not already included
if (!defined('DB_HOST')) {
    require_once __DIR__ . '/../includes/config.php';
}

class Database {
    private $host;
    private $db_name;
    private $username;
    private $password;
    private $charset;
    public $pdo;

    public function __construct() {
        // ใช้ constants จาก config.php
        $this->host = DB_HOST;
        $this->db_name = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;

        $this->connect();
    }

    private function connect() {
        try {
            // ลองเชื่อมต่อฐานข้อมูลก่อน
            $dsn = "mysql:host={$this->host};dbname={$this->db_name};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);

            // Set timezone to Thailand
            $this->pdo->exec("SET time_zone = '+07:00'");
        } catch (PDOException $e) {
            // ถ้าฐานข้อมูลไม่มี ให้สร้างใหม่
            if ($e->getCode() == 1049) {
                $this->createDatabase();
            } else {
                die("Connection failed: " . $e->getMessage());
            }
        }
    }

    private function createDatabase() {
        try {
            // เชื่อมต่อโดยไม่ระบุฐานข้อมูล
            $dsn = "mysql:host={$this->host};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $pdo = new PDO($dsn, $this->username, $this->password, $options);

            // สร้างฐานข้อมูล
            $pdo->exec("CREATE DATABASE IF NOT EXISTS {$this->db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

            // เชื่อมต่อฐานข้อมูลที่เพิ่งสร้าง
            $dsn = "mysql:host={$this->host};dbname={$this->db_name};charset={$this->charset}";
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);

            // Set timezone to Thailand
            $this->pdo->exec("SET time_zone = '+07:00'");

            // สร้างตารางจากไฟล์ SQL
            $this->createTables();

        } catch (PDOException $e) {
            die("Database creation failed: " . $e->getMessage());
        }
    }

    private function createTables() {
        $sql = file_get_contents(__DIR__ . '/../sql/setup.sql');

        // แยก SQL statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));

        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
                try {
                    $this->pdo->exec($statement);
                } catch (PDOException $e) {
                    // ข้าม error ถ้าตารางมีอยู่แล้ว
                    if ($e->getCode() != '42S01') {
                        throw $e;
                    }
                }
            }
        }
    }

    public function getConnection() {
        return $this->pdo;
    }
}

// Global database connection
$database = new Database();
$pdo = $database->getConnection();
?>
