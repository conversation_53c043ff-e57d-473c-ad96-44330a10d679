<?php
require_once 'includes/auth.php';
require_once 'classes/TelegramNotifier.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

$telegramNotifier = new TelegramNotifier();
$message = '';
$messageType = '';

// ประมวลผลการบันทึกการตั้งค่า
if ($_POST && isset($_POST['save_settings'])) {
    try {
        $config = [
            'enabled' => isset($_POST['enabled']),
            'bot_token' => trim($_POST['bot_token']),
            'chat_ids' => array_filter(array_map('trim', explode(',', $_POST['chat_ids'])))
        ];
        
        // ตรวจสอบ Bot Token ถ้าเปิดใช้งาน
        if ($config['enabled'] && !empty($config['bot_token'])) {
            if (!$telegramNotifier->validateBotToken($config['bot_token'])) {
                throw new Exception('Bot Token ไม่ถูกต้อง กรุณาตรวจสอบอีกครั้ง');
            }
        }
        
        $telegramNotifier->updateConfig($config);
        $message = 'บันทึกการตั้งค่า Telegram เรียบร้อยแล้ว';
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// ประมวลผลการทดสอบส่งข้อความ
if ($_POST && isset($_POST['test_message'])) {
    try {
        $result = $telegramNotifier->testMessage();
        
        if ($result && !empty(array_filter($result))) {
            $message = 'ส่งข้อความทดสอบสำเร็จ!';
            $messageType = 'success';
        } else {
            $message = 'ไม่สามารถส่งข้อความทดสอบได้ กรุณาตรวจสอบการตั้งค่า';
            $messageType = 'warning';
        }
        
    } catch (Exception $e) {
        $message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

$config = $telegramNotifier->getConfig();
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ตั้งค่า Telegram - Asset Management System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ระบบจัดการ Asset</h1>
            <p class="subtitle">ตั้งค่าการแจ้งเตือน Telegram</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="index.php"><i class="fas fa-list"></i> รายการ Assets</a></li>
                <?php if (isAdmin()): ?>
                    <li><a href="users.php"><i class="fas fa-users"></i> จัดการผู้ใช้</a></li>
                <?php endif; ?>
                <li><a href="#" onclick="openToolsModal()"><i class="fas fa-tools"></i> เครื่องมือ</a></li>
                <li><a href="#" onclick="openProfileModal()"><i class="fas fa-user"></i> โปรไฟล์</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> ออกจากระบบ</a></li>
            </ul>
        </nav>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- คำแนะนำการตั้งค่า -->
        <div class="card">
            <div class="card-header">
                <h2><i class="fab fa-telegram"></i> วิธีการตั้งค่า Telegram Bot</h2>
            </div>
            <div class="card-body">
                <div class="setup-guide">
                    <h4>ขั้นตอนการสร้าง Telegram Bot:</h4>
                    <ol>
                        <li>เปิด Telegram และค้นหา <strong>@BotFather</strong></li>
                        <li>ส่งคำสั่ง <code>/newbot</code> เพื่อสร้าง Bot ใหม่</li>
                        <li>ตั้งชื่อ Bot และ Username (ต้องลงท้ายด้วย "bot")</li>
                        <li>คัดลอก <strong>Bot Token</strong> ที่ได้รับมาใส่ในช่องด้านล่าง</li>
                        <li>เพิ่ม Bot เข้ากลุ่มหรือแชทที่ต้องการรับแจ้งเตือน</li>
                        <li>ส่งข้อความใดๆ ให้ Bot เพื่อเริ่มการสนทนา</li>
                        <li>ใช้ <a href="get_chat_ids.php" target="_blank" style="color: #007bff; font-weight: bold;">🔍 เครื่องมือหา Chat IDs</a> ของระบบ หรือ <a href="https://api.telegram.org/bot<strong>YOUR_BOT_TOKEN</strong>/getUpdates" target="_blank">API</a> เพื่อหา Chat ID</li>
                    </ol>
                    
                    <div class="alert alert-info">
                        <strong>หมายเหตุ:</strong> Chat ID สามารถเป็นตัวเลขบวกหรือลบ สำหรับกลุ่มจะเป็นตัวเลขลบ
                    </div>
                </div>
            </div>
        </div>

        <!-- การตั้งค่า Telegram -->
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-cog"></i> การตั้งค่า Telegram Bot</h2>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="enabled" <?= $config['enabled'] ? 'checked' : '' ?>>
                                เปิดใช้งานการแจ้งเตือน Telegram
                            </label>
                            <small class="form-text">เมื่อเปิดใช้งาน ระบบจะส่งแจ้งเตือนผ่าน Telegram เมื่อมีการเพิ่ม แก้ไข ลบ Asset และ Auto Backup</small>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="bot_token">Bot Token</label>
                            <input type="text" id="bot_token" name="bot_token" class="form-control" 
                                   value="<?= htmlspecialchars($config['bot_token']) ?>" 
                                   placeholder="1234567890:ABCdefGHIjklMNOpqrsTUVwxyz">
                            <small class="form-text">Bot Token ที่ได้จาก @BotFather</small>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="chat_ids">Chat IDs</label>
                            <div class="input-group">
                                <input type="text" id="chat_ids" name="chat_ids" class="form-control"
                                       value="<?= htmlspecialchars(implode(', ', $config['chat_ids'])) ?>"
                                       placeholder="123456789, -987654321">
                                <div class="input-group-append">
                                    <a href="get_chat_ids.php" target="_blank" class="btn btn-outline-info" title="หา Chat IDs">
                                        <i class="fas fa-search"></i> หา Chat IDs
                                    </a>
                                </div>
                            </div>
                            <small class="form-text">Chat ID ของผู้รับแจ้งเตือน คั่นด้วยเครื่องหมายจุลภาค (สำหรับกลุ่มใช้เครื่องหมายลบนำหน้า)</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" name="save_settings" class="btn btn-primary">
                            <i class="fas fa-save"></i> บันทึกการตั้งค่า
                        </button>
                        <button type="submit" name="test_message" class="btn btn-success" 
                                <?= !$config['enabled'] || empty($config['bot_token']) || empty($config['chat_ids']) ? 'disabled' : '' ?>>
                            <i class="fab fa-telegram"></i> ทดสอบส่งข้อความ
                        </button>
                        <a href="backup_settings.php" class="btn btn-secondary">
                            <i class="fas fa-database"></i> ตั้งค่า Backup
                        </a>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> กลับหน้าหลัก
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- ตัวอย่างข้อความแจ้งเตือน -->
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-eye"></i> ตัวอย่างข้อความแจ้งเตือน</h2>
            </div>
            <div class="card-body">
                <div class="notification-examples">
                    <div class="example-item">
                        <h5>🆕 เพิ่ม Asset ใหม่</h5>
                        <div class="example-message">
                            🆕 <strong>เพิ่ม Asset ใหม่</strong><br><br>
                            📋 <strong>รายละเอียด:</strong><br>
                            • <strong>Type:</strong> Desktop<br>
                            • <strong>Brand:</strong> Dell<br>
                            • <strong>Model:</strong> OptiPlex 7090<br>
                            • <strong>Department:</strong> IT<br>
                            • <strong>Status:</strong> ใช้งาน<br><br>
                            👤 <strong>ผู้เพิ่ม:</strong> Admin User<br>
                            🕐 <strong>เวลา:</strong> 2024-01-15 14:30:25
                        </div>
                    </div>

                    <div class="example-item">
                        <h5>💾 Auto Backup สำเร็จ</h5>
                        <div class="example-message">
                            💾 <strong>Auto Backup สำเร็จ</strong><br><br>
                            📁 <strong>ไฟล์:</strong> asset_backup_2024-01-15_14-30-25_CREATE.sql<br>
                            🔧 <strong>การดำเนินการ:</strong> CREATE<br>
                            📝 <strong>รายละเอียด:</strong> เพิ่ม Asset ใหม่ ID: 123<br>
                            📊 <strong>ขนาดไฟล์:</strong> 2.5 MB<br>
                            🕐 <strong>เวลา:</strong> 2024-01-15 14:30:25
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ตรวจสอบการเปิด/ปิดใช้งาน telegram
        document.querySelector('input[name="enabled"]').addEventListener('change', function() {
            const telegramFields = document.querySelectorAll('#bot_token, #chat_ids');
            const testButton = document.querySelector('button[name="test_message"]');
            
            telegramFields.forEach(field => {
                field.disabled = !this.checked;
            });
            
            if (!this.checked) {
                testButton.disabled = true;
            }
        });

        // เรียกใช้เมื่อโหลดหน้า
        document.addEventListener('DOMContentLoaded', function() {
            const telegramEnabled = document.querySelector('input[name="enabled"]');
            if (telegramEnabled) {
                telegramEnabled.dispatchEvent(new Event('change'));
            }
        });
    </script>

    <style>
        .setup-guide ol {
            padding-left: 20px;
        }
        
        .setup-guide li {
            margin-bottom: 8px;
        }
        
        .setup-guide code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
        
        .notification-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        
        .example-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
        
        .example-item h5 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .example-message {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 13px;
            line-height: 1.4;
            white-space: pre-line;
        }
    </style>
</body>
</html>
