<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>🔍 จัดการ IP Address Lookup</h2>";

// ฟังก์ชันสำหรับ NSLOOKUP
function performNslookup($hostname) {
    $result = [
        'success' => false,
        'ip_address' => null,
        'method' => '',
        'error' => '',
        'execution_time' => 0
    ];

    $startTime = microtime(true);

    if (empty(trim($hostname))) {
        $result['error'] = 'Hostname is empty';
        return $result;
    }

    $hostname = trim($hostname);

    // วิธีที่ 1: ใช้ gethostbyname - IPv4 เท่านั้น
    $ip = gethostbyname($hostname);
    if ($ip !== $hostname && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
        $result['success'] = true;
        $result['ip_address'] = $ip;
        $result['method'] = 'gethostbyname';
        $result['execution_time'] = microtime(true) - $startTime;
        return $result;
    }

    // วิธีที่ 2: ใช้ dns_get_record - IPv4 เท่านั้น
    if (function_exists('dns_get_record')) {
        $records = @dns_get_record($hostname, DNS_A);
        if ($records && isset($records[0]['ip'])) {
            $ip = $records[0]['ip'];
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                $result['success'] = true;
                $result['ip_address'] = $ip;
                $result['method'] = 'dns_get_record';
                $result['execution_time'] = microtime(true) - $startTime;
                return $result;
            }
        }
    }

    // วิธีที่ 3: ใช้ nslookup command แบบปรับปรุง
    if (function_exists('exec')) {
        $output = [];
        $return_var = 0;

        // สำหรับ Windows
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            exec("nslookup $hostname 2>&1", $output, $return_var);
        } else {
            // สำหรับ Linux/Unix
            exec("nslookup $hostname 2>&1", $output, $return_var);
        }

        if ($return_var === 0 && !empty($output)) {
            foreach ($output as $line) {
                $line = trim($line);

                // ข้าม DNS Server address
                if (preg_match('/Server:|Address:.*#53/', $line)) {
                    continue;
                }

                // หา IPv4 Address ที่ไม่ใช่ DNS Server
                if (preg_match('/Address:\s*(\d+\.\d+\.\d+\.\d+)/', $line, $matches)) {
                    if (filter_var($matches[1], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                        $result['success'] = true;
                        $result['ip_address'] = $matches[1];
                        $result['method'] = 'nslookup command';
                        $result['execution_time'] = microtime(true) - $startTime;
                        return $result;
                    }
                }

                // สำหรับ Windows format
                if (preg_match('/Name:\s*' . preg_quote($hostname, '/') . '/i', $line)) {
                    // หาบรรทัดถัดไปที่มี Address
                    $nextLineIndex = array_search($line, $output) + 1;
                    if (isset($output[$nextLineIndex])) {
                        $nextLine = trim($output[$nextLineIndex]);
                        if (preg_match('/Address(?:es)?:\s*(\d+\.\d+\.\d+\.\d+)/', $nextLine, $matches)) {
                            if (filter_var($matches[1], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                                $result['success'] = true;
                                $result['ip_address'] = $matches[1];
                                $result['method'] = 'nslookup command (Windows)';
                                $result['execution_time'] = microtime(true) - $startTime;
                                return $result;
                            }
                        }
                    }
                }
            }
        }
    }

    // วิธีที่ 4: ใช้ dig command (สำหรับ Linux/Unix)
    if (function_exists('exec') && strtoupper(substr(PHP_OS, 0, 3)) !== 'WIN') {
        $output = [];
        $return_var = 0;

        exec("dig +short $hostname A 2>&1", $output, $return_var);

        if ($return_var === 0 && !empty($output)) {
            foreach ($output as $line) {
                $line = trim($line);
                if (filter_var($line, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                    $result['success'] = true;
                    $result['ip_address'] = $line;
                    $result['method'] = 'dig command';
                    $result['execution_time'] = microtime(true) - $startTime;
                    return $result;
                }
            }
        }
    }

    $result['error'] = 'Unable to resolve hostname';
    $result['execution_time'] = microtime(true) - $startTime;
    return $result;
}

// อัปเดต IP Address ทั้งหมด
if (isset($_POST['update_all_ips'])) {
    echo "<h3>🔄 กำลังอัปเดต IP Address ทั้งหมด:</h3>";
    
    try {
        $stmt = $pdo->query("SELECT id, asset_id, hostname FROM assets WHERE hostname IS NOT NULL AND hostname != ''");
        $assets = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $successCount = 0;
        $failCount = 0;
        $totalTime = 0;
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>กำลังประมวลผล " . count($assets) . " รายการ...</strong></p>";
        
        foreach ($assets as $asset) {
            $lookupResult = performNslookup($asset['hostname']);
            $totalTime += $lookupResult['execution_time'];
            
            if ($lookupResult['success']) {
                // อัปเดต IP Address
                $updateStmt = $pdo->prepare("UPDATE assets SET ip_address = ?, last_ip_check = NOW() WHERE id = ?");
                $updateStmt->execute([$lookupResult['ip_address'], $asset['id']]);
                
                echo "<p style='color: green;'>✅ {$asset['asset_id']} ({$asset['hostname']}) → {$lookupResult['ip_address']}</p>";
                $successCount++;
            } else {
                echo "<p style='color: red;'>❌ {$asset['asset_id']} ({$asset['hostname']}) → {$lookupResult['error']}</p>";
                $failCount++;
            }
        }
        
        echo "<hr>";
        echo "<p><strong>สรุปผลลัพธ์:</strong></p>";
        echo "<ul>";
        echo "<li style='color: green;'>สำเร็จ: {$successCount} รายการ</li>";
        echo "<li style='color: red;'>ล้มเหลว: {$failCount} รายการ</li>";
        echo "<li>เวลาทั้งหมด: " . number_format($totalTime, 2) . " วินาที</li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// อัปเดต IP Address เฉพาะรายการที่เลือก
if (isset($_POST['update_selected_ips']) && isset($_POST['selected_assets'])) {
    echo "<h3>🔄 กำลังอัปเดต IP Address รายการที่เลือก:</h3>";
    
    try {
        $selectedIds = $_POST['selected_assets'];
        $placeholders = str_repeat('?,', count($selectedIds) - 1) . '?';
        
        $stmt = $pdo->prepare("SELECT id, asset_id, hostname FROM assets WHERE id IN ($placeholders) AND hostname IS NOT NULL AND hostname != ''");
        $stmt->execute($selectedIds);
        $assets = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $successCount = 0;
        $failCount = 0;
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        
        foreach ($assets as $asset) {
            $lookupResult = performNslookup($asset['hostname']);
            
            if ($lookupResult['success']) {
                $updateStmt = $pdo->prepare("UPDATE assets SET ip_address = ?, last_ip_check = NOW() WHERE id = ?");
                $updateStmt->execute([$lookupResult['ip_address'], $asset['id']]);
                
                echo "<p style='color: green;'>✅ {$asset['asset_id']} ({$asset['hostname']}) → {$lookupResult['ip_address']}</p>";
                $successCount++;
            } else {
                echo "<p style='color: red;'>❌ {$asset['asset_id']} ({$asset['hostname']}) → {$lookupResult['error']}</p>";
                $failCount++;
            }
        }
        
        echo "<p><strong>สำเร็จ: {$successCount} รายการ, ล้มเหลว: {$failCount} รายการ</strong></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

try {
    // แสดงสถิติ
    echo "<h3>📊 สถิติ IP Address:</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM assets WHERE hostname IS NOT NULL AND hostname != ''");
    $totalWithHostname = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as with_ip FROM assets WHERE hostname IS NOT NULL AND hostname != '' AND ip_address IS NOT NULL AND ip_address != ''");
    $withIp = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as without_ip FROM assets WHERE hostname IS NOT NULL AND hostname != '' AND (ip_address IS NULL OR ip_address = '')");
    $withoutIp = $stmt->fetchColumn();
    
    echo "<div style='display: flex; gap: 20px; flex-wrap: wrap; margin: 20px 0;'>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; min-width: 120px;'>";
    echo "<h4 style='margin: 0; color: #007bff;'>{$totalWithHostname}</h4>";
    echo "<p style='margin: 5px 0 0 0;'>มี Hostname</p>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; text-align: center; min-width: 120px;'>";
    echo "<h4 style='margin: 0; color: #28a745;'>{$withIp}</h4>";
    echo "<p style='margin: 5px 0 0 0;'>มี IP Address</p>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; text-align: center; min-width: 120px;'>";
    echo "<h4 style='margin: 0; color: #856404;'>{$withoutIp}</h4>";
    echo "<p style='margin: 5px 0 0 0;'>ยังไม่มี IP</p>";
    echo "</div>";
    echo "</div>";
    
    // แสดงรายการ Assets
    echo "<h3>📋 รายการ Assets ที่มี Hostname:</h3>";
    
    if ($totalWithHostname > 0) {
        // ปุ่มอัปเดตทั้งหมด
        echo "<div style='margin: 15px 0;'>";
        echo "<form method='POST' style='display: inline;' onsubmit='return confirm(\"คุณต้องการอัปเดต IP Address ทั้งหมดหรือไม่?\");'>";
        echo "<button type='submit' name='update_all_ips' style='background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-right: 10px;'>🔄 อัปเดต IP ทั้งหมด</button>";
        echo "</form>";
        
        echo "<button onclick='toggleSelectAll()' style='background: #17a2b8; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-right: 10px;'>☑️ เลือก/ยกเลิกทั้งหมด</button>";
        echo "</div>";
        
        // ฟอร์มสำหรับอัปเดตรายการที่เลือก
        echo "<form method='POST' id='selectedForm'>";
        
        $stmt = $pdo->query("SELECT id, asset_id, hostname, ip_address, last_ip_check FROM assets WHERE hostname IS NOT NULL AND hostname != '' ORDER BY last_ip_check ASC, hostname");
        $assets = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th><input type='checkbox' id='selectAll' onchange='toggleSelectAll()'></th>";
        echo "<th>Asset ID</th><th>Hostname</th><th>IP Address</th><th>Last Check</th><th>สถานะ</th>";
        echo "</tr>";
        
        foreach ($assets as $asset) {
            $hasIp = !empty($asset['ip_address']);
            $rowStyle = $hasIp ? '' : 'background: #fff3cd;';
            $ipDisplay = $hasIp ? htmlspecialchars($asset['ip_address']) : '<span style="color: orange;">ยังไม่มี IP</span>';
            $lastCheck = $asset['last_ip_check'] ? date('d/m/Y H:i', strtotime($asset['last_ip_check'])) : '-';
            
            // คำนวณสถานะ
            $status = '';
            if (!$hasIp) {
                $status = '<span style="color: orange;">ต้องอัปเดต</span>';
            } else {
                $checkTime = strtotime($asset['last_ip_check']);
                $daysSinceCheck = (time() - $checkTime) / (24 * 60 * 60);
                
                if ($daysSinceCheck > 7) {
                    $status = '<span style="color: red;">เก่าเกิน 7 วัน</span>';
                } elseif ($daysSinceCheck > 3) {
                    $status = '<span style="color: orange;">เก่าเกิน 3 วัน</span>';
                } else {
                    $status = '<span style="color: green;">ล่าสุด</span>';
                }
            }
            
            echo "<tr style='{$rowStyle}'>";
            echo "<td><input type='checkbox' name='selected_assets[]' value='{$asset['id']}' class='asset-checkbox'></td>";
            echo "<td>" . htmlspecialchars($asset['asset_id']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($asset['hostname']) . "</strong></td>";
            echo "<td>" . $ipDisplay . "</td>";
            echo "<td>" . $lastCheck . "</td>";
            echo "<td>" . $status . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='margin: 15px 0;'>";
        echo "<button type='submit' name='update_selected_ips' style='background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;' onclick='return confirmSelectedUpdate();'>🔄 อัปเดต IP รายการที่เลือก</button>";
        echo "</div>";
        
        echo "</form>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ ไม่มี Assets ที่มี Hostname</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<script>
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const assetCheckboxes = document.querySelectorAll('.asset-checkbox');
    
    assetCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
}

function confirmSelectedUpdate() {
    const selectedCheckboxes = document.querySelectorAll('.asset-checkbox:checked');
    
    if (selectedCheckboxes.length === 0) {
        alert('กรุณาเลือกรายการที่ต้องการอัปเดต');
        return false;
    }
    
    return confirm(`คุณต้องการอัปเดต IP Address สำหรับ ${selectedCheckboxes.length} รายการที่เลือกหรือไม่?`);
}
</script>

<hr>
<p>
    <a href="test_nslookup.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        🧪 ทดสอบ NSLOOKUP
    </a>
    <a href="add_ip_address_field.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🔧 เพิ่มฟิลด์ IP Address
    </a>
    <a href="index.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th { background: #f8f9fa; }
button:hover { opacity: 0.8; }
</style>
