"""
Database Configuration for Asset Management System
"""

import mysql.connector
from mysql.connector import Error
import logging

class DatabaseConfig:
    """Database configuration and connection management"""
    
    # Database connection parameters
    DB_CONFIG = {
        'host': 'localhost',
        'database': 'asset_management',
        'user': 'root',
        'password': 'Wxmujwsofu@1234',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci',
        'autocommit': True,
        'time_zone': '+07:00'
    }
    
    @staticmethod
    def get_connection():
        """Create and return database connection"""
        try:
            connection = mysql.connector.connect(**DatabaseConfig.DB_CONFIG)
            if connection.is_connected():
                # Set timezone
                cursor = connection.cursor()
                cursor.execute("SET time_zone = '+07:00'")
                cursor.close()
                return connection
        except Error as e:
            logging.error(f"Database connection error: {e}")
            raise e
    
    @staticmethod
    def test_connection():
        """Test database connection"""
        try:
            connection = DatabaseConfig.get_connection()
            if connection.is_connected():
                db_info = connection.get_server_info()
                connection.close()
                return True, f"Connected to MySQL Server version {db_info}"
        except Error as e:
            return False, f"Connection failed: {e}"
    
    @staticmethod
    def create_database_if_not_exists():
        """Create database if it doesn't exist"""
        try:
            # Connect without specifying database
            config = DatabaseConfig.DB_CONFIG.copy()
            database_name = config.pop('database')
            
            connection = mysql.connector.connect(**config)
            cursor = connection.cursor()
            
            # Create database
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {database_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            cursor.close()
            connection.close()
            
            return True, "Database created successfully"
        except Error as e:
            return False, f"Database creation failed: {e}"
