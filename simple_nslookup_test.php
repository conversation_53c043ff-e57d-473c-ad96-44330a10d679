<?php
require_once 'includes/auth.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>🧪 ทดสอบ NSLOOKUP แบบง่าย</h2>";

// ฟังก์ชันทดสอบ NSLOOKUP แบบง่าย
function simpleNslookup($hostname) {
    echo "<h3>🔍 ทดสอบ: " . htmlspecialchars($hostname) . "</h3>";
    
    // Method 1: gethostbyname
    echo "<h4>1. gethostbyname:</h4>";
    $start = microtime(true);
    $ip1 = gethostbyname($hostname);
    $time1 = microtime(true) - $start;
    
    echo "<p><strong>Result:</strong> " . htmlspecialchars($ip1) . "</p>";
    echo "<p><strong>Time:</strong> " . number_format($time1 * 1000, 2) . " ms</p>";
    echo "<p><strong>Valid IPv4:</strong> " . (filter_var($ip1, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) ? '✅ Yes' : '❌ No') . "</p>";
    echo "<p><strong>Different from hostname:</strong> " . ($ip1 !== $hostname ? '✅ Yes' : '❌ No') . "</p>";
    
    // Method 2: dns_get_record
    echo "<h4>2. dns_get_record:</h4>";
    if (function_exists('dns_get_record')) {
        $start = microtime(true);
        $records = @dns_get_record($hostname, DNS_A);
        $time2 = microtime(true) - $start;
        
        echo "<p><strong>Records found:</strong> " . (is_array($records) ? count($records) : 0) . "</p>";
        echo "<p><strong>Time:</strong> " . number_format($time2 * 1000, 2) . " ms</p>";
        
        if ($records && isset($records[0]['ip'])) {
            echo "<p><strong>First IP:</strong> " . htmlspecialchars($records[0]['ip']) . "</p>";
            echo "<p><strong>Valid IPv4:</strong> " . (filter_var($records[0]['ip'], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) ? '✅ Yes' : '❌ No') . "</p>";
        } else {
            echo "<p style='color: red;'>❌ No A records found</p>";
        }
        
        if ($records) {
            echo "<details><summary>All records:</summary>";
            echo "<pre>" . htmlspecialchars(print_r($records, true)) . "</pre>";
            echo "</details>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ dns_get_record function not available</p>";
    }
    
    // Method 3: nslookup command
    echo "<h4>3. nslookup command:</h4>";
    if (function_exists('exec')) {
        $start = microtime(true);
        $output = [];
        $return_var = 0;
        
        exec("nslookup $hostname 2>&1", $output, $return_var);
        $time3 = microtime(true) - $start;
        
        echo "<p><strong>Return code:</strong> " . $return_var . "</p>";
        echo "<p><strong>Time:</strong> " . number_format($time3 * 1000, 2) . " ms</p>";
        echo "<p><strong>Output lines:</strong> " . count($output) . "</p>";
        
        echo "<details><summary>Raw output:</summary>";
        echo "<pre>" . htmlspecialchars(implode("\n", $output)) . "</pre>";
        echo "</details>";
        
        // Parse output
        $foundIPs = [];
        $isDnsServer = false;
        
        foreach ($output as $line) {
            $line = trim($line);
            
            // Check if this is DNS server info
            if (preg_match('/Server:|Address:.*#53/', $line)) {
                $isDnsServer = true;
                continue;
            }
            
            // Look for IPv4 addresses
            if (preg_match('/Address(?:es)?:\s*(\d+\.\d+\.\d+\.\d+)/', $line, $matches)) {
                if (filter_var($matches[1], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                    $foundIPs[] = $matches[1];
                }
            }
            
            // Windows format
            if (preg_match('/Name:\s*' . preg_quote($hostname, '/') . '/i', $line)) {
                $nextLineIndex = array_search($line, $output) + 1;
                if (isset($output[$nextLineIndex])) {
                    $nextLine = trim($output[$nextLineIndex]);
                    if (preg_match('/Address(?:es)?:\s*(\d+\.\d+\.\d+\.\d+)/', $nextLine, $matches)) {
                        if (filter_var($matches[1], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                            $foundIPs[] = $matches[1];
                        }
                    }
                }
            }
        }
        
        if (!empty($foundIPs)) {
            echo "<p><strong>Found IPs:</strong></p>";
            echo "<ul>";
            foreach ($foundIPs as $ip) {
                echo "<li>" . htmlspecialchars($ip) . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ No IP addresses found in output</p>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ exec function not available</p>";
    }
    
    // Method 4: dig command (Linux/Unix only)
    if (strtoupper(substr(PHP_OS, 0, 3)) !== 'WIN') {
        echo "<h4>4. dig command:</h4>";
        if (function_exists('exec')) {
            $start = microtime(true);
            $output = [];
            $return_var = 0;
            
            exec("dig +short $hostname A 2>&1", $output, $return_var);
            $time4 = microtime(true) - $start;
            
            echo "<p><strong>Return code:</strong> " . $return_var . "</p>";
            echo "<p><strong>Time:</strong> " . number_format($time4 * 1000, 2) . " ms</p>";
            echo "<p><strong>Output lines:</strong> " . count($output) . "</p>";
            
            if (!empty($output)) {
                echo "<p><strong>Results:</strong></p>";
                echo "<ul>";
                foreach ($output as $line) {
                    $line = trim($line);
                    if (filter_var($line, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                        echo "<li style='color: green;'>✅ " . htmlspecialchars($line) . " (Valid IPv4)</li>";
                    } else {
                        echo "<li style='color: orange;'>⚠️ " . htmlspecialchars($line) . " (Not IPv4)</li>";
                    }
                }
                echo "</ul>";
            } else {
                echo "<p style='color: red;'>❌ No output from dig command</p>";
            }
        }
    }
    
    echo "<hr>";
}

// ทดสอบ hostname ต่างๆ
$testHostnames = [
    'google.com',
    'facebook.com', 
    'github.com',
    'stackoverflow.com',
    'localhost'
];

// ทดสอบ hostname ที่ผู้ใช้ส่งมา
if (isset($_GET['test']) && !empty($_GET['test'])) {
    $userHostname = $_GET['test'];
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎯 ทดสอบ Hostname ที่คุณระบุ</h3>";
    simpleNslookup($userHostname);
    echo "</div>";
}

echo "<h3>📋 ทดสอบ Hostname ตัวอย่าง:</h3>";

foreach ($testHostnames as $hostname) {
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    simpleNslookup($hostname);
    echo "</div>";
}

// แสดงข้อมูลระบบ
echo "<h3>ℹ️ ข้อมูลระบบ:</h3>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>Operating System:</strong> " . PHP_OS . "</li>";
echo "<li><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</li>";
echo "<li><strong>gethostbyname:</strong> " . (function_exists('gethostbyname') ? '✅ Available' : '❌ Not Available') . "</li>";
echo "<li><strong>dns_get_record:</strong> " . (function_exists('dns_get_record') ? '✅ Available' : '❌ Not Available') . "</li>";
echo "<li><strong>exec:</strong> " . (function_exists('exec') ? '✅ Available' : '❌ Not Available') . "</li>";
echo "</ul>";

// ฟอร์มทดสอบ hostname กำหนดเอง
echo "<h3>🧪 ทดสอบ Hostname กำหนดเอง:</h3>";
echo "<form method='GET' style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='test'><strong>Hostname:</strong></label><br>";
echo "<input type='text' id='test' name='test' placeholder='เช่น example.com' style='width: 300px; padding: 8px; margin-top: 5px;' value='" . htmlspecialchars($_GET['test'] ?? '') . "'>";
echo "</div>";
echo "<button type='submit' style='background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>🔍 ทดสอบ</button>";
echo "</form>";
?>

<hr>
<p>
    <a href="test_nslookup.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        🧪 ทดสอบ NSLOOKUP แบบเต็ม
    </a>
    <a href="ip_lookup_manager.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🔍 จัดการ IP Lookup
    </a>
    <a href="add_ip_address_field.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🔧 เพิ่มฟิลด์ IP Address
    </a>
    <a href="index.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
details { margin: 10px 0; }
summary { font-weight: bold; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
