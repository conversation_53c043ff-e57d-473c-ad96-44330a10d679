<?php
// ไฟล์ทดสอบระบบ Auto Backup
require_once 'includes/auth.php';
require_once 'config/database.php';
require_once 'classes/BackupManager.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>ทดสอบระบบ Auto Backup</h2>";

try {
    // สร้าง BackupManager instance
    $backupManager = new BackupManager($pdo);
    
    // ดึงการตั้งค่าปัจจุบัน
    $config = $backupManager->getConfig();
    
    echo "<h3>การตั้งค่าปัจจุบัน:</h3>";
    echo "<ul>";
    echo "<li><strong>เปิดใช้งาน:</strong> " . ($config['enabled'] ? 'ใช่' : 'ไม่') . "</li>";
    echo "<li><strong>โฟลเดอร์ Backup:</strong> " . htmlspecialchars($config['backup_path']) . "</li>";
    echo "<li><strong>จำนวน Backup สูงสุด:</strong> " . $config['max_backups'] . "</li>";
    echo "<li><strong>Email แจ้งเตือน:</strong> " . ($config['email_enabled'] ? 'เปิด' : 'ปิด') . "</li>";
    echo "</ul>";
    
    // ตรวจสอบโฟลเดอร์ backup
    $backupPath = rtrim($config['backup_path'], '/\\');
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        $backupPath .= '\\';
    } else {
        $backupPath .= '/';
    }
    
    echo "<h3>ตรวจสอบโฟลเดอร์ Backup:</h3>";
    echo "<ul>";
    echo "<li><strong>Path:</strong> " . htmlspecialchars($backupPath) . "</li>";
    echo "<li><strong>มีอยู่จริง:</strong> " . (is_dir($backupPath) ? 'ใช่' : 'ไม่') . "</li>";
    echo "<li><strong>สามารถเขียนได้:</strong> " . (is_writable($backupPath) ? 'ใช่' : 'ไม่') . "</li>";
    
    if (is_dir($backupPath)) {
        $realPath = realpath($backupPath);
        echo "<li><strong>Real Path:</strong> " . htmlspecialchars($realPath) . "</li>";
        
        // นับไฟล์ backup ที่มีอยู่
        $backupFiles = glob($backupPath . 'asset_backup_*.sql');
        echo "<li><strong>ไฟล์ Backup ที่มีอยู่:</strong> " . count($backupFiles) . " ไฟล์</li>";
        
        if (!empty($backupFiles)) {
            echo "<li><strong>ไฟล์ล่าสุด:</strong>";
            usort($backupFiles, function($a, $b) {
                return filemtime($b) - filemtime($a);
            });
            $latestFile = basename($backupFiles[0]);
            $fileTime = date('Y-m-d H:i:s', filemtime($backupFiles[0]));
            $fileSize = filesize($backupFiles[0]);
            echo " {$latestFile} ({$fileTime}, " . formatFileSize($fileSize) . ")</li>";
        }
    }
    echo "</ul>";
    
    // ทดสอบสร้าง backup
    if (isset($_GET['test']) && $_GET['test'] === 'create') {
        echo "<h3>กำลังทดสอบสร้าง Backup...</h3>";
        
        $testDescription = "ทดสอบระบบ Auto Backup - " . date('Y-m-d H:i:s');
        $result = $backupManager->createAutoBackup('TEST', $testDescription);
        
        if ($result) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
            echo "<strong>✅ สร้าง Backup สำเร็จ!</strong><br>";
            echo "ไฟล์: {$result}<br>";
            echo "Path: " . htmlspecialchars($backupPath . $result);
            echo "</div>";
            
            // ตรวจสอบไฟล์ที่สร้าง
            $filePath = $backupPath . $result;
            if (file_exists($filePath)) {
                $fileSize = filesize($filePath);
                echo "<p><strong>ขนาดไฟล์:</strong> " . formatFileSize($fileSize) . "</p>";
                
                // อ่านบรรทัดแรกของไฟล์
                $firstLine = file_get_contents($filePath, false, null, 0, 100);
                echo "<p><strong>เนื้อหาเริ่มต้น:</strong> " . htmlspecialchars($firstLine) . "...</p>";
            }
        } else {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
            echo "<strong>❌ ไม่สามารถสร้าง Backup ได้</strong>";
            echo "</div>";
        }
    }
    
    // ดึง backup logs ล่าสุด
    echo "<h3>Backup Logs ล่าสุด (5 รายการ):</h3>";
    $logs = $backupManager->getBackupLogs(5);
    
    if (empty($logs)) {
        echo "<p>ยังไม่มี backup logs</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ไฟล์</th><th>การดำเนินการ</th><th>รายละเอียด</th><th>ขนาด</th><th>วันที่</th><th>ผู้สร้าง</th>";
        echo "</tr>";
        
        foreach ($logs as $log) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($log['filename']) . "</td>";
            echo "<td>" . htmlspecialchars($log['action_type']) . "</td>";
            echo "<td>" . htmlspecialchars($log['description']) . "</td>";
            echo "<td>" . formatFileSize($log['file_size']) . "</td>";
            echo "<td>" . formatDateTime($log['created_date']) . "</td>";
            echo "<td>" . htmlspecialchars($log['created_by']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "<strong>เกิดข้อผิดพลาด:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

// ฟังก์ชันช่วย
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

function formatDateTime($datetime) {
    return date('Y-m-d H:i:s', strtotime($datetime));
}
?>

<hr>
<p>
    <a href="?test=create" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        🧪 ทดสอบสร้าง Backup
    </a>
    <a href="backup_settings.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        ⚙️ ตั้งค่า Backup
    </a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
table { margin: 10px 0; }
th { background: #f8f9fa; }
</style>
