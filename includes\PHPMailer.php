<?php
/**
 * Simple PHPMailer Class
 * สำหรับส่ง email แจ้งเตือน backup
 */

class PHPMailer {
    private $host;
    private $port;
    private $username;
    private $password;
    private $secure;
    private $from;
    private $fromName;
    private $to = [];
    private $subject;
    private $body;
    private $isHTML = false;
    private $charset = 'UTF-8';
    
    public function __construct($exceptions = false) {
        // Constructor
    }
    
    /**
     * ตั้งค่าการใช้ SMTP
     */
    public function isSMTP() {
        // Set to use SMTP
    }
    
    /**
     * ตั้งค่า SMTP Host
     */
    public function __set($name, $value) {
        switch($name) {
            case 'Host':
                $this->host = $value;
                break;
            case 'SMTPAuth':
                // SMTP Authentication
                break;
            case 'Username':
                $this->username = $value;
                break;
            case 'Password':
                $this->password = $value;
                break;
            case 'SMTPSecure':
                $this->secure = $value;
                break;
            case 'Port':
                $this->port = $value;
                break;
            case 'CharSet':
                $this->charset = $value;
                break;
            case 'Subject':
                $this->subject = $value;
                break;
            case 'Body':
                $this->body = $value;
                break;
        }
    }
    
    /**
     * ตั้งค่าผู้ส่ง
     */
    public function setFrom($email, $name = '') {
        $this->from = $email;
        $this->fromName = $name;
    }
    
    /**
     * เพิ่มผู้รับ
     */
    public function addAddress($email, $name = '') {
        $this->to[] = ['email' => $email, 'name' => $name];
    }
    
    /**
     * ตั้งค่าเป็น HTML
     */
    public function isHTML($isHTML = true) {
        $this->isHTML = $isHTML;
    }
    
    /**
     * ส่ง email
     */
    public function send() {
        try {
            // ใช้ PHP mail() function แทน SMTP สำหรับความง่าย
            $headers = [];
            $headers[] = 'MIME-Version: 1.0';
            $headers[] = 'Content-type: ' . ($this->isHTML ? 'text/html' : 'text/plain') . '; charset=' . $this->charset;
            $headers[] = 'From: ' . $this->fromName . ' <' . $this->from . '>';
            $headers[] = 'X-Mailer: PHP/' . phpversion();
            
            $success = true;
            foreach ($this->to as $recipient) {
                $result = mail(
                    $recipient['email'],
                    $this->subject,
                    $this->body,
                    implode("\r\n", $headers)
                );
                
                if (!$result) {
                    $success = false;
                }
            }
            
            return $success;
            
        } catch (Exception $e) {
            throw new Exception('Email sending failed: ' . $e->getMessage());
        }
    }
}

/**
 * Alternative: ใช้ SMTP จริงถ้าต้องการ
 * ต้องติดตั้ง PHPMailer library ผ่าน Composer
 */
class SMTPMailer {
    private $config;
    
    public function __construct($config) {
        $this->config = $config;
    }
    
    /**
     * ส่ง email ผ่าน SMTP
     */
    public function sendEmail($to, $subject, $body, $isHTML = true) {
        // ตรวจสอบว่ามี PHPMailer library หรือไม่
        if (!class_exists('PHPMailer\PHPMailer\PHPMailer')) {
            // ใช้ PHP mail() function แทน
            return $this->sendWithPHPMail($to, $subject, $body, $isHTML);
        }
        
        try {
            $mail = new \PHPMailer\PHPMailer\PHPMailer(true);
            
            // Server settings
            $mail->isSMTP();
            $mail->Host = $this->config['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $this->config['smtp_username'];
            $mail->Password = $this->config['smtp_password'];
            $mail->SMTPSecure = $this->config['smtp_secure'];
            $mail->Port = $this->config['smtp_port'];
            $mail->CharSet = 'UTF-8';
            
            // Recipients
            $mail->setFrom($this->config['smtp_username'], 'Asset Management System');
            
            if (is_array($to)) {
                foreach ($to as $email) {
                    $mail->addAddress($email);
                }
            } else {
                $mail->addAddress($to);
            }
            
            // Content
            $mail->isHTML($isHTML);
            $mail->Subject = $subject;
            $mail->Body = $body;
            
            $mail->send();
            return true;
            
        } catch (Exception $e) {
            error_log("SMTP Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * ส่ง email ด้วย PHP mail() function
     */
    private function sendWithPHPMail($to, $subject, $body, $isHTML = true) {
        $headers = [];
        $headers[] = 'MIME-Version: 1.0';
        $headers[] = 'Content-type: ' . ($isHTML ? 'text/html' : 'text/plain') . '; charset=UTF-8';
        $headers[] = 'From: Asset Management System <' . $this->config['smtp_username'] . '>';
        $headers[] = 'X-Mailer: PHP/' . phpversion();
        
        if (is_array($to)) {
            $success = true;
            foreach ($to as $email) {
                $result = mail($email, $subject, $body, implode("\r\n", $headers));
                if (!$result) {
                    $success = false;
                }
            }
            return $success;
        } else {
            return mail($to, $subject, $body, implode("\r\n", $headers));
        }
    }
}
?>
