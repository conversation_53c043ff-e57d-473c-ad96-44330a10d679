<?php
// รับพารามิเตอร์จาก URL
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';
$filter_serial = $_GET['filter_serial'] ?? '';

// สร้าง query string สำหรับส่งต่อไปยังรายงาน
$queryParams = [];
if (!empty($search)) $queryParams['search'] = $search;
if (!empty($filter_type)) $queryParams['filter_type'] = $filter_type;
if (!empty($filter_brand)) $queryParams['filter_brand'] = $filter_brand;
if (!empty($filter_department)) $queryParams['filter_department'] = $filter_department;
if (!empty($filter_status)) $queryParams['filter_status'] = $filter_status;
if (!empty($filter_os)) $queryParams['filter_os'] = $filter_os;
if (!empty($filter_serial)) $queryParams['filter_serial'] = $filter_serial;

$queryString = !empty($queryParams) ? '?' . http_build_query($queryParams) : '';

// สร้างข้อมูลสำหรับแสดงเงื่อนไขที่ใช้
$filterInfo = [];
if (!empty($search)) $filterInfo[] = "ค้นหา: \"$search\"";
if (!empty($filter_type)) $filterInfo[] = "ประเภท: $filter_type";
if (!empty($filter_brand)) $filterInfo[] = "ยี่ห้อ: $filter_brand";
if (!empty($filter_department)) $filterInfo[] = "แผนก: $filter_department";
if (!empty($filter_status)) $filterInfo[] = "สถานะ: $filter_status";
if (!empty($filter_os)) $filterInfo[] = "ระบบปฏิบัติการ: $filter_os";
if (!empty($filter_serial)) $filterInfo[] = "หมายเลขเครื่อง: \"$filter_serial\"";
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เลือกรูปแบบรายงาน - Asset Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .report-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
        }
        
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .report-preview {
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .preview-professional {
            background: linear-gradient(45deg, #2c3e50, #34495e);
        }
        
        .preview-thai {
            background: linear-gradient(45deg, #1a252f, #2c3e50);
        }
        
        .preview-minimal {
            background: linear-gradient(45deg, #ecf0f1, #bdc3c7);
        }
        
        .preview-icon {
            font-size: 4rem;
            color: rgba(255,255,255,0.8);
        }
        
        .preview-minimal .preview-icon {
            color: rgba(0,0,0,0.3);
        }
        
        .report-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .report-description {
            color: #7f8c8d;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .btn-select {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-select:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
            transform: scale(1.05);
            color: white;
        }
        
        .container-custom {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header-section {
            text-align: center;
            margin-bottom: 50px;
            color: white;
        }
        
        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        
        .feature-list li {
            padding: 5px 0;
            color: #6c757d;
            font-size: 0.85rem;
        }
        
        .feature-list li i {
            color: #28a745;
            margin-right: 8px;
            width: 12px;
        }
    </style>
</head>
<body>
    <div class="container-custom">
        <!-- Header Section -->
        <div class="header-section">
            <h1 class="header-title">
                <i class="fas fa-file-pdf me-3"></i>
                เลือกรูปแบบรายงาน
            </h1>
            <p class="header-subtitle">เลือกรูปแบบรายงาน PDF ที่เหมาะสมกับความต้องการของคุณ</p>

            <?php if (!empty($filterInfo)): ?>
            <div class="alert alert-info mt-3" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; border-radius: 10px;">
                <h6><i class="fas fa-filter me-2"></i>เงื่อนไขการกรองที่ใช้:</h6>
                <p class="mb-0"><?= implode(" | ", $filterInfo) ?></p>
            </div>
            <?php else: ?>
            <div class="alert alert-warning mt-3" style="background: rgba(255,193,7,0.2); border: 1px solid rgba(255,193,7,0.3); color: white; border-radius: 10px;">
                <p class="mb-0"><i class="fas fa-info-circle me-2"></i>รายงานจะแสดงข้อมูลทั้งหมดในระบบ</p>
            </div>
            <?php endif; ?>
        </div>

        <!-- Report Options -->
        <div class="row g-4">
            <!-- Professional Report -->
            <div class="col-lg-4 col-md-6">
                <div class="card report-card h-100">
                    <div class="report-preview preview-professional">
                        <i class="fas fa-building preview-icon"></i>
                    </div>
                    <div class="card-body p-4">
                        <h5 class="report-title">Professional Report</h5>
                        <p class="report-description">
                            รายงานแบบเป็นทางการสำหรับองค์กรขนาดใหญ่ มีการออกแบบที่สวยงามและเป็นมืออาชีพ
                        </p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> ออกแบบเป็นทางการ</li>
                            <li><i class="fas fa-check"></i> สีสันที่เหมาะสมกับองค์กร</li>
                            <li><i class="fas fa-check"></i> รายละเอียดครบถ้วน</li>
                            <li><i class="fas fa-check"></i> เหมาะสำหรับการนำเสนอ</li>
                        </ul>
                        <div class="text-center mt-4">
                            <button class="btn btn-select me-2" onclick="generateReport('professional')">
                                <i class="fas fa-eye me-2"></i>ดูรายงาน
                            </button>
                            <button class="btn btn-outline-secondary" onclick="downloadReport('professional')">
                                <i class="fas fa-download me-2"></i>ดาวน์โหลด
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Thai Professional Report -->
            <div class="col-lg-4 col-md-6">
                <div class="card report-card h-100">
                    <div class="report-preview preview-thai">
                        <i class="fas fa-language preview-icon"></i>
                    </div>
                    <div class="card-body p-4">
                        <h5 class="report-title">รายงานไทยเป็นทางการ</h5>
                        <p class="report-description">
                            รายงานภาษาไทยแบบเป็นทางการ เหมาะสำหรับหน่วยงานราชการและองค์กรไทย
                        </p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> ฟอนต์ไทยสวยงาม</li>
                            <li><i class="fas fa-check"></i> รูปแบบเป็นทางการ</li>
                            <li><i class="fas fa-check"></i> เหมาะกับหน่วยงานไทย</li>
                            <li><i class="fas fa-check"></i> ข้อมูลครบถ้วนเป็นภาษาไทย</li>
                        </ul>
                        <div class="text-center mt-4">
                            <button class="btn btn-select me-2" onclick="generateReport('thai')">
                                <i class="fas fa-eye me-2"></i>ดูรายงาน
                            </button>
                            <button class="btn btn-outline-secondary" onclick="downloadReport('thai')">
                                <i class="fas fa-download me-2"></i>ดาวน์โหลด
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Minimal Report -->
            <div class="col-lg-4 col-md-6">
                <div class="card report-card h-100">
                    <div class="report-preview preview-minimal">
                        <i class="fas fa-minimize preview-icon"></i>
                    </div>
                    <div class="card-body p-4">
                        <h5 class="report-title">Minimal Design</h5>
                        <p class="report-description">
                            รายงานแบบเรียบง่าย เน้นข้อมูลเป็นหลัก เหมาะสำหรับการใช้งานภายใน
                        </p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> ดีไซน์เรียบง่าย</li>
                            <li><i class="fas fa-check"></i> เน้นข้อมูลเป็นหลัก</li>
                            <li><i class="fas fa-check"></i> ประหยัดหมึก</li>
                            <li><i class="fas fa-check"></i> เหมาะสำหรับการพิมพ์</li>
                        </ul>
                        <div class="text-center mt-4">
                            <button class="btn btn-select me-2" onclick="generateReport('minimal')">
                                <i class="fas fa-eye me-2"></i>ดูรายงาน
                            </button>
                            <button class="btn btn-outline-secondary" onclick="downloadReport('minimal')">
                                <i class="fas fa-download me-2"></i>ดาวน์โหลด
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="text-center mt-5">
            <a href="index.php" class="btn btn-outline-light btn-lg">
                <i class="fas fa-arrow-left me-2"></i>กลับไปหน้าหลัก
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // ข้อมูลพารามิเตอร์จาก PHP
        const filterParams = <?= json_encode($queryParams) ?>;
        const queryString = <?= json_encode($queryString) ?>;

        function generateReport(type) {
            // สร้าง URL สำหรับรายงาน
            let reportUrl = '';
            switch(type) {
                case 'professional':
                    reportUrl = 'export_pdf_professional.php';
                    break;
                case 'thai':
                    reportUrl = 'export_pdf_thai_professional.php';
                    break;
                case 'minimal':
                    reportUrl = 'export_pdf_minimal.php';
                    break;
            }

            // เพิ่มพารามิเตอร์การกรองไปยัง URL รายงาน
            if (queryString) {
                reportUrl += queryString;
            }

            // แสดงข้อความยืนยัน
            const filterCount = Object.keys(filterParams).length;
            let confirmMessage = 'สร้างรายงาน PDF แบบ ' + getReportTypeName(type);

            if (filterCount > 0) {
                confirmMessage += '\nพร้อมเงื่อนไขการกรอง ' + filterCount + ' รายการ';
            } else {
                confirmMessage += '\nจะแสดงข้อมูลทั้งหมดในระบบ';
            }

            if (confirm(confirmMessage + '\n\nต้องการดำเนินการต่อหรือไม่?')) {
                // แสดง loading
                showLoading(type);

                // เปิดรายงานในหน้าต่างใหม่
                window.open(reportUrl, '_blank');

                // ซ่อน loading หลังจาก 2 วินาที
                setTimeout(() => hideLoading(type), 2000);
            }
        }

        function getReportTypeName(type) {
            switch(type) {
                case 'professional': return 'Professional';
                case 'thai': return 'ไทยเป็นทางการ';
                case 'minimal': return 'Minimal Design';
                default: return 'ไม่ทราบ';
            }
        }

        function showLoading(type) {
            const button = document.querySelector(`button[onclick="generateReport('${type}')"]`);
            if (button) {
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังสร้างรายงาน...';
                button.disabled = true;
            }
        }

        function hideLoading(type) {
            const button = document.querySelector(`button[onclick="generateReport('${type}')"]`);
            if (button) {
                button.innerHTML = '<i class="fas fa-eye me-2"></i>ดูรายงาน';
                button.disabled = false;
            }
        }

        function downloadReport(type) {
            // สร้าง URL สำหรับรายงาน
            let reportUrl = '';
            switch(type) {
                case 'professional':
                    reportUrl = 'export_pdf_professional.php';
                    break;
                case 'thai':
                    reportUrl = 'export_pdf_thai_professional.php';
                    break;
                case 'minimal':
                    reportUrl = 'export_pdf_minimal.php';
                    break;
            }

            // เพิ่มพารามิเตอร์การกรองและ download=true
            let downloadParams = {...filterParams, download: 'true'};
            const downloadQuery = '?' + new URLSearchParams(downloadParams).toString();
            reportUrl += downloadQuery;

            // แสดงข้อความยืนยัน
            const filterCount = Object.keys(filterParams).length;
            let confirmMessage = 'ดาวน์โหลดรายงาน PDF แบบ ' + getReportTypeName(type);

            if (filterCount > 0) {
                confirmMessage += '\nพร้อมเงื่อนไขการกรอง ' + filterCount + ' รายการ';
            } else {
                confirmMessage += '\nจะแสดงข้อมูลทั้งหมดในระบบ';
            }

            if (confirm(confirmMessage + '\n\nต้องการดำเนินการต่อหรือไม่?')) {
                // สร้าง link สำหรับดาวน์โหลด
                const link = document.createElement('a');
                link.href = reportUrl;
                link.download = '';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }
        
        // เพิ่มเอฟเฟกต์ hover สำหรับการ์ด
        document.querySelectorAll('.report-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
