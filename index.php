<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบการล็อกอิน
requireLogin();

$assetManager = new AssetManager($pdo);

// รับค่าการค้นหาและกรอง
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';

// รับข้อความแจ้งเตือน
$message = $_GET['message'] ?? '';
$messageType = $_GET['type'] ?? 'success';

// Pagination settings
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 25; // จำนวนแถวต่อหน้า
$offset = ($page - 1) * $limit;

// ดึงข้อมูล assets ทั้งหมดเพื่อนับจำนวน
$allAssets = $assetManager->getAllAssets($search, $filter_type, $filter_brand, $filter_status, $filter_os, $filter_department);
$totalAssets = count($allAssets);
$totalPages = ceil($totalAssets / $limit);

// ดึงข้อมูล assets สำหรับหน้าปัจจุบัน
$assets = array_slice($allAssets, $offset, $limit);

$assetTypes = $assetManager->getAssetTypes();
$assetBrands = $assetManager->getAssetBrands();
$assetDepartments = $assetManager->getAssetDepartments();
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Management System</title>

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ระบบจัดการ Asset</h1>
            <p class="subtitle">Asset Management System - จัดการทรัพย์สินองค์กรอย่างมีประสิทธิภาพ</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="index.php" class="active"><i class="fas fa-list"></i> รายการ Assets</a></li>
                <?php if (isAdmin()): ?>
                    <li><a href="users.php"><i class="fas fa-users"></i> จัดการผู้ใช้</a></li>
                <?php endif; ?>
                <li><a href="#" onclick="openToolsModal()"><i class="fas fa-tools"></i> เครื่องมือ</a></li>
                <li><a href="#" onclick="openProfileModal()"><i class="fas fa-user"></i> โปรไฟล์</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> ออกจากระบบ</a></li>
            </ul>
        </nav>



        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Dashboard Stats -->
        <?php
        $totalAssetsCount = count($assetManager->getAllAssets());
        $activeAssets = count($assetManager->getAllAssets('', '', '', 'ใช้งาน'));
        $brokenAssets = count($assetManager->getAllAssets('', '', '', 'ชำรุด'));
        $spareAssets = count($assetManager->getAllAssets('', '', '', 'สำรอง'));
        ?>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= $totalAssetsCount ?></div>
                    <div class="stat-label">รวมทั้งหมด</div>
                </div>
            </div>
            <div class="stat-card success">
                <div class="stat-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= $activeAssets ?></div>
                    <div class="stat-label">ใช้งาน</div>
                </div>
            </div>
            <div class="stat-card danger">
                <div class="stat-icon danger">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= $brokenAssets ?></div>
                    <div class="stat-label">ชำรุด</div>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon warning">
                    <i class="fas fa-box"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= $spareAssets ?></div>
                    <div class="stat-label">สำรอง</div>
                </div>
            </div>
        </div>



        <!-- Search and Filter -->
        <div class="search-filter">
            <h3>ค้นหาและกรองข้อมูล</h3>
            <form method="GET" action="">
                <div class="search-row">
                    <div class="form-group">
                        <label for="search">ค้นหา</label>
                        <input type="text" id="search" name="search" class="form-control"
                               placeholder="ค้นหาจาก Type, Brand, Model, Tag, Department, Hostname, Asset ID, Serial Number"
                               value="<?= htmlspecialchars($search) ?>">
                    </div>
                    <div class="form-group">
                        <label for="filter_type">ประเภท</label>
                        <select id="filter_type" name="filter_type" class="form-control">
                            <option value="">ทั้งหมด</option>
                            <?php foreach ($assetTypes as $type): ?>
                                <option value="<?= htmlspecialchars($type) ?>"
                                        <?= $filter_type === $type ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($type) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filter_brand">Brand</label>
                        <select id="filter_brand" name="filter_brand" class="form-control">
                            <option value="">ทั้งหมด</option>
                            <?php foreach ($assetBrands as $brand): ?>
                                <option value="<?= htmlspecialchars($brand) ?>"
                                        <?= $filter_brand === $brand ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($brand) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filter_department">แผนก</label>
                        <select id="filter_department" name="filter_department" class="form-control">
                            <option value="">ทั้งหมด</option>
                            <?php foreach ($assetDepartments as $department): ?>
                                <option value="<?= htmlspecialchars($department) ?>"
                                        <?= $filter_department === $department ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($department) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filter_status">สถานะ</label>
                        <select id="filter_status" name="filter_status" class="form-control">
                            <option value="">ทั้งหมด</option>
                            <option value="ใช้งาน" <?= $filter_status === 'ใช้งาน' ? 'selected' : '' ?>>ใช้งาน</option>
                            <option value="ชำรุด" <?= $filter_status === 'ชำรุด' ? 'selected' : '' ?>>ชำรุด</option>
                            <option value="สำรอง" <?= $filter_status === 'สำรอง' ? 'selected' : '' ?>>สำรอง</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filter_os">Operating System</label>
                        <select id="filter_os" name="filter_os" class="form-control">
                            <option value="">ทั้งหมด</option>
                            <option value="-" <?= $filter_os === '-' ? 'selected' : '' ?>>-</option>
                            <option value="Windows 7" <?= $filter_os === 'Windows 7' ? 'selected' : '' ?>>Windows 7</option>
                            <option value="Windows 10" <?= $filter_os === 'Windows 10' ? 'selected' : '' ?>>Windows 10</option>
                            <option value="Windows 11" <?= $filter_os === 'Windows 11' ? 'selected' : '' ?>>Windows 11</option>
                            <option value="MacOS" <?= $filter_os === 'MacOS' ? 'selected' : '' ?>>MacOS</option>
                        </select>
                    </div>
                </div>

                <div class="search-actions">
                    <button type="submit" class="btn btn-search">
                        <i class="fas fa-search"></i>
                        <span>ค้นหา</span>
                    </button>
                    <a href="index.php" class="btn btn-reset">
                        <i class="fas fa-undo-alt"></i>
                        <span>รีเซ็ต</span>
                    </a>
                </div>
            </form>
        </div>

        <!-- Assets Table -->
        <div class="card">
            <div class="card-header">
                <div class="header-content">
                    <h2>รายการ Assets (<?= count($assets) ?> รายการ)</h2>
                    <div class="header-actions">
                        <!-- ปุ่มส่งออก PDF -->
                        <button onclick="selectReportFormat()" class="btn btn-primary" title="เลือกรูปแบบรายงาน PDF">
                            <i class="fas fa-file-pdf"></i> สร้างรายงาน PDF
                        </button>
                        <?php if (isAdmin()): ?>
                            <button onclick="openAddAssetModal()" class="btn btn-success">
                                <i class="fas fa-plus"></i>
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($assets)): ?>
                    <div class="empty-state">
                        <div class="icon">📦</div>
                        <h3>ไม่พบข้อมูล Asset</h3>
                        <p>ไม่มี Asset ที่ตรงกับเงื่อนไขการค้นหา หรือยังไม่มีข้อมูล Asset ในระบบ</p>
                        <?php if (isAdmin()): ?>
                            <button onclick="openAddAssetModal()" class="btn btn-primary">➕ เพิ่ม Asset แรก</button>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Department</th>
                                    <th>Type</th>
                                    <th>Brand</th>
                                    <th>Model</th>
                                    <th>Tag</th>
                                    <th>Serial Number</th>
                                    <th>Hostname</th>
                                    <th>Set</th>
                                    <th>Description</th>
                                    <th>Status</th>
                                    <th>วันที่เพิ่ม</th>
                                    <th>การจัดการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($assets as $asset): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($asset['department']) ?></td>
                                        <td><?= htmlspecialchars($asset['type']) ?></td>
                                        <td><?= htmlspecialchars($asset['brand']) ?></td>
                                        <td><?= htmlspecialchars($asset['model']) ?></td>
                                        <td><?= htmlspecialchars($asset['tag']) ?></td>
                                        <td><?= htmlspecialchars($asset['serial_number']) ?></td>
                                        <td><?= htmlspecialchars($asset['hostname']) ?></td>
                                        <td><?= htmlspecialchars($asset['set_name']) ?></td>
                                        <td><?= htmlspecialchars($asset['description']) ?></td>
                                        <td><?= getStatusBadge($asset['status']) ?></td>
                                        <td><?= formatDateTime($asset['created_date']) ?></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button onclick="viewAssetDetails(<?= $asset['id'] ?>)" class="btn btn-primary" title="ดูรายละเอียด">👁️</button>
                                                <button onclick="openEditAssetModal(<?= $asset['id'] ?>)" class="btn btn-warning" title="แก้ไข">✏️</button>
                                                <a href="delete_asset.php?id=<?= $asset['id'] ?>" class="btn btn-danger" title="ลบ"
                                                   onclick="return confirm('คุณแน่ใจหรือไม่ที่จะลบ Asset นี้?')">🗑️</a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination-container">
                            <div class="pagination-info">
                                <span>แสดง <?= $offset + 1 ?> - <?= min($offset + $limit, $totalAssets) ?> จาก <?= $totalAssets ?> รายการ</span>
                            </div>
                            <div class="pagination">
                                <?php if ($page > 1): ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => 1])) ?>" class="pagination-btn">«</a>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>" class="pagination-btn">‹</a>
                                <?php endif; ?>

                                <?php
                                $start = max(1, $page - 2);
                                $end = min($totalPages, $page + 2);

                                for ($i = $start; $i <= $end; $i++):
                                ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"
                                       class="pagination-btn <?= $i == $page ? 'active' : '' ?>"><?= $i ?></a>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>" class="pagination-btn">›</a>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $totalPages])) ?>" class="pagination-btn">»</a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Auto submit form when filter changes
        document.getElementById('filter_type').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('filter_status').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('filter_os').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('filter_brand').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('filter_department').addEventListener('change', function() {
            this.form.submit();
        });





        // Add loading animation for stats
        document.addEventListener('DOMContentLoaded', function() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = parseInt(stat.textContent);
                let currentValue = 0;
                const increment = Math.ceil(finalValue / 20);

                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    stat.textContent = currentValue;
                }, 50);
            });
        });

        // Add search highlight
        function highlightSearch() {
            const searchTerm = document.getElementById('search').value.toLowerCase();
            if (!searchTerm) return;

            const tableRows = document.querySelectorAll('.table tbody tr');
            tableRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                cells.forEach(cell => {
                    const text = cell.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        cell.style.backgroundColor = '#fff3cd';
                    }
                });
            });
        }

        // Call highlight on page load if search term exists
        document.addEventListener('DOMContentLoaded', highlightSearch);

        // ฟังก์ชันเลือกรูปแบบรายงาน
        function selectReportFormat() {
            // รวบรวมพารามิเตอร์การกรองปัจจุบัน
            const params = new URLSearchParams();

            // เพิ่มพารามิเตอร์การค้นหาและกรอง
            const search = document.getElementById('search').value;
            const filterType = document.getElementById('filter_type').value;
            const filterBrand = document.getElementById('filter_brand').value;
            const filterDepartment = document.getElementById('filter_department').value;
            const filterStatus = document.getElementById('filter_status').value;
            const filterOs = document.getElementById('filter_os').value;

            if (search) params.append('search', search);
            if (filterType) params.append('filter_type', filterType);
            if (filterBrand) params.append('filter_brand', filterBrand);
            if (filterDepartment) params.append('filter_department', filterDepartment);
            if (filterStatus) params.append('filter_status', filterStatus);
            if (filterOs) params.append('filter_os', filterOs);

            // เปิดหน้าเลือกรูปแบบรายงาน
            const url = 'report_selector.php?' + params.toString();
            window.open(url, '_blank');
        }

        // Export to PDF function (แสดงในเบราว์เซอร์) - เก็บไว้สำหรับใช้งานเดิม
        function exportToPDF() {
            // รวบรวมพารามิเตอร์การกรองปัจจุบัน
            const params = new URLSearchParams();

            // เพิ่มพารามิเตอร์การค้นหาและกรอง
            const search = document.getElementById('search').value;
            const filterType = document.getElementById('filter_type').value;
            const filterBrand = document.getElementById('filter_brand').value;
            const filterDepartment = document.getElementById('filter_department').value;
            const filterStatus = document.getElementById('filter_status').value;
            const filterOs = document.getElementById('filter_os').value;
            const filterSerial = document.getElementById('filter_serial').value;

            if (search) params.append('search', search);
            if (filterType) params.append('filter_type', filterType);
            if (filterBrand) params.append('filter_brand', filterBrand);
            if (filterDepartment) params.append('filter_department', filterDepartment);
            if (filterStatus) params.append('filter_status', filterStatus);
            if (filterOs) params.append('filter_os', filterOs);
            if (filterSerial) params.append('filter_serial', filterSerial);

            // เปิดหน้าต่างใหม่เพื่อแสดง PDF
            const url = 'export_pdf.php?' + params.toString();
            window.open(url, '_blank');
        }

        // Download PDF function (ดาวน์โหลดไฟล์)
        function downloadPDF() {
            // รวบรวมพารามิเตอร์การกรองปัจจุบัน
            const params = new URLSearchParams();

            // เพิ่มพารามิเตอร์การค้นหาและกรอง
            const search = document.getElementById('search').value;
            const filterType = document.getElementById('filter_type').value;
            const filterBrand = document.getElementById('filter_brand').value;
            const filterDepartment = document.getElementById('filter_department').value;
            const filterStatus = document.getElementById('filter_status').value;
            const filterOs = document.getElementById('filter_os').value;

            if (search) params.append('search', search);
            if (filterType) params.append('filter_type', filterType);
            if (filterBrand) params.append('filter_brand', filterBrand);
            if (filterDepartment) params.append('filter_department', filterDepartment);
            if (filterStatus) params.append('filter_status', filterStatus);
            if (filterOs) params.append('filter_os', filterOs);

            // เพิ่มพารามิเตอร์สำหรับดาวน์โหลด
            params.append('download', 'true');

            // สร้าง URL และดาวน์โหลด
            const url = 'export_pdf.php?' + params.toString();
            window.location.href = url;
        }

        // ฟังก์ชันเลือกรูปแบบการ Export
        function selectExportFormat() {
            // รวบรวมพารามิเตอร์การกรองปัจจุบัน
            const params = new URLSearchParams();

            // เพิ่มพารามิเตอร์การค้นหาและกรอง
            const search = document.getElementById('search').value;
            const filterType = document.getElementById('filter_type').value;
            const filterBrand = document.getElementById('filter_brand').value;
            const filterDepartment = document.getElementById('filter_department').value;
            const filterStatus = document.getElementById('filter_status').value;
            const filterOs = document.getElementById('filter_os').value;

            if (search) params.append('search', search);
            if (filterType) params.append('filter_type', filterType);
            if (filterBrand) params.append('filter_brand', filterBrand);
            if (filterDepartment) params.append('filter_department', filterDepartment);
            if (filterStatus) params.append('filter_status', filterStatus);
            if (filterOs) params.append('filter_os', filterOs);

            // เปิดหน้าเลือกรูปแบบการ Export
            const url = 'export_selector.php?' + params.toString();
            window.open(url, '_blank');
        }

        // Export data function (CSV) - เก็บไว้สำหรับใช้งานเดิม
        function exportData() {
            selectExportFormat();
        }

        // ฟังก์ชันเปิด Export Modal
        function openExportModal() {
            // รวบรวมข้อมูลการกรอง
            const search = document.getElementById('search').value;
            const filterType = document.getElementById('filter_type').value;
            const filterBrand = document.getElementById('filter_brand').value;
            const filterDepartment = document.getElementById('filter_department').value;
            const filterStatus = document.getElementById('filter_status').value;
            const filterOs = document.getElementById('filter_os').value;

            // สร้างข้อมูลการกรอง
            const filterInfo = [];
            if (search) filterInfo.push(`ค้นหา: "${search}"`);
            if (filterType) filterInfo.push(`ประเภท: ${filterType}`);
            if (filterBrand) filterInfo.push(`ยี่ห้อ: ${filterBrand}`);
            if (filterDepartment) filterInfo.push(`แผนก: ${filterDepartment}`);
            if (filterStatus) filterInfo.push(`สถานะ: ${filterStatus}`);
            if (filterOs) filterInfo.push(`ระบบปฏิบัติการ: ${filterOs}`);

            // แสดงข้อมูลการกรอง
            const filterInfoDiv = document.getElementById('exportFilterInfo');
            const filterTextDiv = document.getElementById('exportFilterText');
            const noFilterDiv = document.getElementById('exportNoFilter');

            if (filterInfo.length > 0) {
                filterTextDiv.textContent = filterInfo.join(' | ');
                filterInfoDiv.style.display = 'block';
                noFilterDiv.style.display = 'none';
            } else {
                filterInfoDiv.style.display = 'none';
                noFilterDiv.style.display = 'block';
            }

            // แสดง Modal
            document.getElementById('exportModal').style.display = 'block';
        }

        // ฟังก์ชันปิด Export Modal
        function closeExportModal() {
            document.getElementById('exportModal').style.display = 'none';
        }

        // ฟังก์ชัน Export ข้อมูล
        function exportData(format) {
            // รวบรวมพารามิเตอร์การกรอง
            const params = new URLSearchParams();

            const search = document.getElementById('search').value;
            const filterType = document.getElementById('filter_type').value;
            const filterBrand = document.getElementById('filter_brand').value;
            const filterDepartment = document.getElementById('filter_department').value;
            const filterStatus = document.getElementById('filter_status').value;
            const filterOs = document.getElementById('filter_os').value;

            if (search) params.append('search', search);
            if (filterType) params.append('filter_type', filterType);
            if (filterBrand) params.append('filter_brand', filterBrand);
            if (filterDepartment) params.append('filter_department', filterDepartment);
            if (filterStatus) params.append('filter_status', filterStatus);
            if (filterOs) params.append('filter_os', filterOs);

            // กำหนด URL ตามรูปแบบ
            let exportUrl = '';
            switch(format) {
                case 'csv':
                    exportUrl = 'export_csv.php';
                    break;
                case 'excel':
                    exportUrl = 'export_excel_simple.php';
                    break;
                case 'json':
                    exportUrl = 'export_json.php';
                    break;
            }

            // เพิ่มพารามิเตอร์การกรอง
            if (params.toString()) {
                exportUrl += '?' + params.toString();
            }

            // แสดงข้อความยืนยัน
            const filterCount = params.toString().split('&').filter(p => p).length;
            let confirmMessage = `ส่งออกข้อมูลเป็น ${format.toUpperCase()}`;

            if (filterCount > 0) {
                confirmMessage += `\nพร้อมเงื่อนไขการกรอง ${filterCount} รายการ`;
            } else {
                confirmMessage += '\nจะส่งออกข้อมูลทั้งหมดในระบบ';
            }

            if (confirm(confirmMessage + '\n\nต้องการดำเนินการต่อหรือไม่?')) {
                // ปิด Modal
                closeExportModal();

                // ดาวน์โหลดไฟล์
                const link = document.createElement('a');
                link.href = exportUrl;
                link.download = '';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // ฟังก์ชันดูข้อมูล JSON
        function viewData(format) {
            if (format === 'json') {
                // รวบรวมพารามิเตอร์การกรอง
                const params = new URLSearchParams();

                const search = document.getElementById('search').value;
                const filterType = document.getElementById('filter_type').value;
                const filterBrand = document.getElementById('filter_brand').value;
                const filterDepartment = document.getElementById('filter_department').value;
                const filterStatus = document.getElementById('filter_status').value;
                const filterOs = document.getElementById('filter_os').value;

                if (search) params.append('search', search);
                if (filterType) params.append('filter_type', filterType);
                if (filterBrand) params.append('filter_brand', filterBrand);
                if (filterDepartment) params.append('filter_department', filterDepartment);
                if (filterStatus) params.append('filter_status', filterStatus);
                if (filterOs) params.append('filter_os', filterOs);

                // เพิ่มพารามิเตอร์สำหรับดูข้อมูล
                params.append('format', 'view');

                // เปิดในหน้าต่างใหม่
                const viewUrl = 'export_json.php?' + params.toString();
                window.open(viewUrl, '_blank');
            }
        }

        // ปิด Modal เมื่อคลิกนอก Modal
        window.onclick = function(event) {
            const exportModal = document.getElementById('exportModal');
            if (event.target === exportModal) {
                closeExportModal();
            }
        }



        // Backup data function
        function backupData() {
            if (confirm('คุณต้องการสำรองข้อมูลหรือไม่?')) {
                alert('ฟีเจอร์สำรองข้อมูลจะพัฒนาในอนาคต');
            }
        }

        // Tools Modal functions
        function openToolsModal() {
            document.getElementById('toolsModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeToolsModal() {
            document.getElementById('toolsModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // ล้าง System Cache จาก Tools Menu
        function clearSystemCacheFromTools() {
            if (confirm('คุณต้องการล้าง System Cache และ Browser Cache หรือไม่?\n\nการดำเนินการนี้จะ:\n- ล้าง PHP OpCache\n- ล้าง Application Cache\n- ล้าง Browser Cache\n- รีเฟรชหน้าเว็บ')) {

                // ล้าง System Cache ก่อน
                fetch('clear_system_cache.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ action: 'clear_cache' })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // ล้าง Browser Cache
                        try {
                            if (typeof(Storage) !== "undefined" && localStorage) {
                                localStorage.clear();
                            }
                            if (typeof(Storage) !== "undefined" && sessionStorage) {
                                sessionStorage.clear();
                            }

                            alert('ล้าง Cache เรียบร้อยแล้ว\nหน้าเว็บจะรีเฟรชอัตโนมัติ');
                            window.location.reload(true);
                        } catch (error) {
                            alert('ล้าง System Cache สำเร็จ\nแต่เกิดข้อผิดพลาดในการล้าง Browser Cache');
                            window.location.reload();
                        }
                    } else {
                        alert('เกิดข้อผิดพลาด: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการล้าง System Cache');
                });
            }
        }



        // Check Font Awesome loading
        function checkFontAwesome() {
            const closeBtn = document.querySelector('.close-btn');
            if (closeBtn) {
                // Check if Font Awesome is loaded
                const testElement = document.createElement('i');
                testElement.className = 'fas fa-times';
                testElement.style.position = 'absolute';
                testElement.style.left = '-9999px';
                document.body.appendChild(testElement);

                const computedStyle = window.getComputedStyle(testElement, '::before');
                const content = computedStyle.getPropertyValue('content');

                document.body.removeChild(testElement);

                // If Font Awesome loaded, content should not be 'none' or empty
                if (content && content !== 'none' && content !== '""') {
                    closeBtn.classList.add('fa-loaded');
                } else {
                    // Show fallback
                    const span = closeBtn.querySelector('span');
                    if (span) {
                        span.style.display = 'inline';
                    }
                    const icon = closeBtn.querySelector('i');
                    if (icon) {
                        icon.style.display = 'none';
                    }
                }
            }
        }

        // Resizable columns functionality
        function initResizableColumns() {
            const table = document.querySelector('.table');
            if (!table) return;

            const headers = table.querySelectorAll('th');
            let isResizing = false;
            let currentColumn = null;
            let startX = 0;
            let startWidth = 0;

            headers.forEach((header, index) => {
                // Skip last column (actions)
                if (index === headers.length - 1) return;

                const resizeHandle = document.createElement('div');
                resizeHandle.className = 'resize-handle';
                resizeHandle.style.cssText = `
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 5px;
                    height: 100%;
                    cursor: col-resize;
                    background: transparent;
                    z-index: 20;
                `;

                header.style.position = 'relative';
                header.appendChild(resizeHandle);

                resizeHandle.addEventListener('mousedown', function(e) {
                    isResizing = true;
                    currentColumn = header;
                    startX = e.pageX;
                    startWidth = parseInt(window.getComputedStyle(header).width, 10);

                    document.addEventListener('mousemove', handleMouseMove);
                    document.addEventListener('mouseup', handleMouseUp);

                    e.preventDefault();
                });

                resizeHandle.addEventListener('mouseenter', function() {
                    this.style.background = 'rgba(0, 123, 255, 0.3)';
                });

                resizeHandle.addEventListener('mouseleave', function() {
                    if (!isResizing) {
                        this.style.background = 'transparent';
                    }
                });
            });

            function handleMouseMove(e) {
                if (!isResizing || !currentColumn) return;

                const diff = e.pageX - startX;
                const newWidth = Math.max(50, startWidth + diff); // Minimum width 50px

                currentColumn.style.width = newWidth + 'px';

                // Update corresponding td elements
                const columnIndex = Array.from(currentColumn.parentNode.children).indexOf(currentColumn);
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const cell = row.children[columnIndex];
                    if (cell) {
                        cell.style.width = newWidth + 'px';
                    }
                });
            }

            function handleMouseUp() {
                if (isResizing) {
                    isResizing = false;
                    currentColumn = null;

                    // Remove highlight from resize handle
                    const activeHandle = document.querySelector('.resize-handle[style*="rgba(0, 123, 255, 0.3)"]');
                    if (activeHandle) {
                        activeHandle.style.background = 'transparent';
                    }
                }

                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
            }
        }

        // Initialize resizable columns when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initResizableColumns();

            // Check Font Awesome after a short delay
            setTimeout(checkFontAwesome, 100);
        });
    </script>

    <!-- Add Asset Modal -->
    <div id="addAssetModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="header-text">
                    <h2>➕ เพิ่ม Asset ใหม่</h2>
                </div>
                <div class="header-actions">
                    <button type="submit" form="addAssetForm" class="btn btn-success" title="บันทึก">
                        บันทึก
                    </button>
                    <span class="close" onclick="closeAddAssetModal()" title="ปิด">&times;</span>
                </div>
            </div>
            <div class="modal-body">
                <form id="addAssetForm" method="POST" action="add_asset.php">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_type">Type <span class="required">*</span></label>
                            <select id="modal_type" name="type" class="form-control" required>
                                <option value="">เลือก Type</option>
                                <option value="Desktop">Desktop</option>
                                <option value="Laptop">Laptop</option>
                                <option value="Monitor">Monitor</option>
                                <option value="All-in-one">All-in-one</option>
                                <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                                <option value="Barcode Printer">Barcode Printer</option>
                                <option value="Barcode Scanner">Barcode Scanner</option>
                                <option value="Tablet">Tablet</option>
                                <option value="UPS">UPS</option>
                                <option value="Queue">Queue</option>
                                <option value="IP Phone">IP Phone</option>
                                <option value="Teleconference">Teleconference</option>
                                <option value="Switch">Switch</option>
                                <option value="Access Point">Access Point</option>
                                <option value="Peripheral">Peripheral</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="modal_brand">Brand</label>
                            <select id="modal_brand" name="brand" class="form-control">
                                <option value="">เลือก Brand</option>
                                <option value="-">-</option>
                                <option value="Dell">Dell</option>
                                <option value="Lenovo">Lenovo</option>
                                <option value="Microsoft">Microsoft</option>
                                <option value="Apple">Apple</option>
                                <option value="Zebra">Zebra</option>
                                <option value="HP">HP</option>
                                <option value="Philips">Philips</option>
                                <option value="Acer">Acer</option>
                                <option value="LG">LG</option>
                                <option value="Cisco">Cisco</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_model">Model</label>
                            <input type="text" id="modal_model" name="model" class="form-control" placeholder="ระบุรุ่น">
                        </div>

                        <div class="form-group">
                            <label for="modal_tag">Tag</label>
                            <input type="text" id="modal_tag" name="tag" class="form-control" placeholder="ระบุ Tag">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_department">Department</label>
                            <select id="modal_department" name="department" class="form-control">
                                <option value="">เลือก Department</option>
                                <option value="Accounting">Accounting</option>
                                <option value="Admission">Admission</option>
                                <option value="Anesthetic">Anesthetic</option>
                                <option value="BME">BME</option>
                                <option value="Cath Lab">Cath Lab</option>
                                <option value="Cardiac Care Unit">Cardiac Care Unit</option>
                                <option value="CEO">CEO</option>
                                <option value="Chivawattana">Chivawattana</option>
                                <option value="Cardiology & Neurological Clinic">Cardiology & Neurological Clinic</option>
                                <option value="Collection">Collection</option>
                                <option value="Contact Center">Contact Center</option>
                                <option value="Cashier IPD">Cashier IPD</option>
                                <option value="Cashier OPD">Cashier OPD</option>
                                <option value="Customer Service">Customer Service</option>
                                <option value="Dental">Dental</option>
                                <option value="Doctor Room">Doctor Room</option>
                                <option value="EMT">EMT</option>
                                <option value="ENT">ENT</option>
                                <option value="Endoscopy Center">Endoscopy Center</option>
                                <option value="Eye Center">Eye Center</option>
                                <option value="Emergency Room">Emergency Room</option>
                                <option value="Financial">Financial</option>
                                <option value="General Support">General Support</option>
                                <option value="Hemodialysis">Hemodialysis</option>
                                <option value="Hemodialysis VIP">Hemodialysis VIP</option>
                                <option value="Health Promotion Center">Health Promotion Center</option>
                                <option value="Human Resources">Human Resources</option>
                                <option value="Intensive Care Unit">Intensive Care Unit</option>
                                <option value="IT">IT</option>
                                <option value="Laboratory">Laboratory</option>
                                <option value="Labour Room">Labour Room</option>
                                <option value="Linen Service">Linen Service</option>
                                <option value="MAO">MAO</option>
                                <option value="Medicine Unit">Medicine Unit</option>
                                <option value="Marketing">Marketing</option>
                                <option value="Medical Record">Medical Record</option>
                                <option value="N-Health">N-Health</option>
                                <option value="Nursery">Nursery</option>
                                <option value="NSO">NSO</option>
                                <option value="Obstetrics & Gynecology">Obstetrics & Gynecology</option>
                                <option value="Operating Room">Operating Room</option>
                                <option value="Orthopedic">Orthopedic</option>
                                <option value="Pharmacy OPD">Pharmacy OPD</option>
                                <option value="Pharmacy OPD (Floor 2)">Pharmacy OPD (Floor 2)</option>
                                <option value="Pharmacy IPD">Pharmacy IPD</option>
                                <option value="Pediatrics">Pediatrics</option>
                                <option value="PUR">PUR</option>
                                <option value="Referral">Referral</option>
                                <option value="Registration">Registration</option>
                                <option value="Repair & Maintenance">Repair & Maintenance</option>
                                <option value="Rehabilitation">Rehabilitation</option>
                                <option value="Secretary">Secretary</option>
                                <option value="Sterlie">Sterlie</option>
                                <option value="Supervisor">Supervisor</option>
                                <option value="Supply">Supply</option>
                                <option value="Surgery Unit">Surgery Unit</option>
                                <option value="Quality Center">Quality Center</option>
                                <option value="Utilization Management">Utilization Management</option>
                                <option value="W5A">W5A</option>
                                <option value="W5B">W5B</option>
                                <option value="W6A">W6A</option>
                                <option value="W6B">W6B</option>
                                <option value="W7A">W7A</option>
                                <option value="W7B">W7B</option>
                                <option value="W8A">W8A</option>
                                <option value="W8B">W8B</option>
                                <option value="W9A">W9A</option>
                                <option value="W9B">W9B</option>
                                <option value="Wound Care Unit">Wound Care Unit</option>
                                <option value="Wellness Center">Wellness Center</option>
                                <option value="X-ray">X-ray</option>
                                <option value="X-ray NP">X-ray NP</option>
                                <option value="เวรเปล">เวรเปล</option>
                                <option value="Food House">Food House</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="modal_status">Status</label>
                            <select id="modal_status" name="status" class="form-control">
                                <option value="ใช้งาน" selected>ใช้งาน</option>
                                <option value="ชำรุด">ชำรุด</option>
                                <option value="สำรอง">สำรอง</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_hostname">Hostname</label>
                            <input type="text" id="modal_hostname" name="hostname" class="form-control" placeholder="ระบุ Hostname">
                        </div>

                        <div class="form-group">
                            <label for="modal_operating_system">Operating System</label>
                            <select id="modal_operating_system" name="operating_system" class="form-control">
                                <option value="">เลือก Operating System</option>
                                <option value="-">-</option>
                                <option value="Windows 7">Windows 7</option>
                                <option value="Windows 10">Windows 10</option>
                                <option value="Windows 11">Windows 11</option>
                                <option value="MacOS">MacOS</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_serial_number">Serial Number</label>
                            <input type="text" id="modal_serial_number" name="serial_number" class="form-control" placeholder="ระบุ Serial Number">
                        </div>

                        <div class="form-group">
                            <label for="modal_asset_id">Asset ID</label>
                            <input type="text" id="modal_asset_id" name="asset_id" class="form-control" placeholder="ระบุ Asset ID">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_warranty_expire">วันหมดประกัน</label>
                            <input type="date" id="modal_warranty_expire" name="warranty_expire" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="modal_set_name">Set</label>
                            <input type="text" id="modal_set_name" name="set_name" class="form-control" placeholder="ระบุ Set">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="modal_description">รายละเอียด</label>
                        <textarea id="modal_description" name="description" class="form-control" rows="3" placeholder="ระบุรายละเอียดเพิ่มเติม"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="modal_created_by">👤 ผู้เพิ่ม</label>
                        <input type="text" id="modal_created_by" name="created_by" class="form-control" readonly value="<?= htmlspecialchars(getCurrentUserFullName()) ?>" style="background-color: #f8f9fa; cursor: not-allowed;">
                    </div>


                </form>
            </div>
        </div>
    </div>

    <script>
        // Modal functions

        // Add Asset Modal functions
        function openAddAssetModal() {
            document.getElementById('addAssetModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeAddAssetModal() {
            document.getElementById('addAssetModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            document.getElementById('addAssetForm').reset();
            // Restore the created_by field value after reset
            document.getElementById('modal_created_by').value = '<?= htmlspecialchars(getCurrentUserFullName()) ?>';
        }

        // Handle Add Asset Form Submit
        document.getElementById('addAssetForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            // หาปุ่ม submit ใน header หรือในฟอร์ม
            const submitBtn = document.querySelector('button[form="addAssetForm"]') || this.querySelector('button[type="submit"]');
            const originalText = submitBtn ? submitBtn.innerHTML : 'บันทึก';

            // แสดง loading
            if (submitBtn) {
                submitBtn.innerHTML = '⏳ กำลังบันทึก...';
                submitBtn.disabled = true;
            }

            fetch('ajax_add_asset.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                // ตรวจสอบ Content-Type
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('Response is not JSON');
                }

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // แสดงข้อความสำเร็จ
                    alert('✅ ' + data.message + ' (ID: ' + data.asset_id + ')');

                    // ปิด modal
                    closeAddAssetModal();

                    // รีโหลดหน้าเพื่อแสดงข้อมูลใหม่
                    setTimeout(() => {
                        location.reload();
                    }, 500);
                } else {
                    alert('❌ ' + (data.message || 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('❌ เกิดข้อผิดพลาด: ' + error.message);
            })
            .finally(() => {
                // คืนค่าปุ่ม
                if (submitBtn) {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            });
        });

        function openProfileModal() {
            document.getElementById('profileModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
            loadProfileData();
        }

        function closeProfileModal() {
            document.getElementById('profileModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function loadProfileData() {
            fetch('get_profile_data.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // อัพเดทข้อมูลในส่วน header
                        document.getElementById('profile_username_display').textContent = data.user.username;
                        document.getElementById('profile_role_text').textContent = data.user.role || 'User';

                        // อัพเดทข้อมูลในตาราง
                        document.getElementById('profile_username').textContent = data.user.username;
                        document.getElementById('profile_full_name').textContent = data.user.full_name || '-';
                        document.getElementById('profile_email').textContent = data.user.email || '-';

                        // อัพเดท role badge
                        const roleBadge = document.getElementById('profile_role');
                        roleBadge.textContent = data.user.role || '-';
                        roleBadge.className = 'role-badge' + (data.user.role === 'Admin' ? ' admin' : '');

                        // อัพเดท status badge
                        const statusBadge = document.getElementById('profile_status');
                        statusBadge.textContent = data.user.status || '-';
                        statusBadge.className = 'status-badge' + (data.user.status === 'Inactive' ? ' inactive' : '');

                        document.getElementById('profile_last_login').textContent = data.user.last_login || '-';
                        document.getElementById('profile_created_date').textContent = data.user.created_date || '-';

                        // Set form values for editing
                        document.getElementById('edit_username').value = data.user.username;
                        document.getElementById('edit_full_name').value = data.user.full_name || '';
                        document.getElementById('edit_email').value = data.user.email || '';

                        // อัพเดทไอคอน role ใน header
                        const roleDisplay = document.getElementById('profile_role_display');
                        const roleIcon = data.user.role === 'Admin' ? '👑' : '👤';
                        roleDisplay.querySelector('span:first-child').textContent = roleIcon;
                    }
                })
                .catch(error => {
                    console.error('Error loading profile:', error);
                    alert('เกิดข้อผิดพลาดในการโหลดข้อมูลโปรไฟล์');
                });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const addModal = document.getElementById('addAssetModal');
            const profileModal = document.getElementById('profileModal');

            if (event.target == addModal) {
                closeAddAssetModal();
            }
            if (event.target == profileModal) {
                closeProfileModal();
            }
        }

        function toggleEditMode() {
            const viewMode = document.getElementById('profileViewMode');
            const editMode = document.getElementById('profileEditMode');

            if (viewMode.style.display === 'none') {
                viewMode.style.display = 'block';
                editMode.style.display = 'none';
            } else {
                viewMode.style.display = 'none';
                editMode.style.display = 'block';
            }
        }

        function saveProfile() {
            const formData = new FormData(document.getElementById('editProfileForm'));

            fetch('update_profile.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('อัพเดทโปรไฟล์สำเร็จ');
                    loadProfileData();
                    toggleEditMode();
                } else {
                    alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถอัพเดทได้'));
                }
            })
            .catch(error => {
                console.error('Error updating profile:', error);
                alert('เกิดข้อผิดพลาดในการอัพเดทโปรไฟล์');
            });
        }

        // Asset Modal functions
        function viewAssetDetails(assetId) {
            console.log('viewAssetDetails called with ID:', assetId);

            // เปิด modal
            document.getElementById('viewAssetModal').style.display = 'block';
            document.body.style.overflow = 'hidden';

            // แสดง loading
            const modalBody = document.getElementById('viewAssetModal').querySelector('.modal-body');
            modalBody.innerHTML = '<div style="text-align: center; padding: 40px;"><div style="font-size: 1.2rem;">🔄 กำลังโหลดข้อมูล...</div></div>';

            // โหลดข้อมูล Asset
            fetch('get_asset_data.php?id=' + assetId)
                .then(function(response) {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error('HTTP error! status: ' + response.status);
                    }
                    return response.json();
                })
                .then(function(data) {
                    console.log('Data received:', data);
                    if (data.success) {
                        // กู้คืน modal structure ก่อน
                        restoreViewModalStructure();
                        displayAssetDetails(data.asset);
                    } else {
                        alert('เกิดข้อผิดพลาดในการโหลดข้อมูล: ' + data.message);
                        closeViewAssetModal();
                    }
                })
                .catch(function(error) {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการเชื่อมต่อกับเซิร์ฟเวอร์: ' + error.message);
                    closeViewAssetModal();
                });
        }

        function closeViewAssetModal() {
            document.getElementById('viewAssetModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function openEditAssetModalFromView() {
            if (window.currentViewAssetId) {
                closeViewAssetModal();
                openEditAssetModal(window.currentViewAssetId);
            } else {
                alert('ไม่พบข้อมูล Asset ID');
            }
        }

        function restoreViewModalStructure() {
            const modalBody = document.getElementById('viewAssetModal').querySelector('.modal-body');
            modalBody.innerHTML = `
                <div class="form-container">
                    <!-- Status Display -->
                    <div class="status-display-row">
                        <div class="status-badge-container">
                            <span class="status-badge" id="view_status_badge">-</span>
                        </div>
                        <div class="asset-id-container">
                            <label>Asset ID:</label>
                            <span class="asset-id-display" id="view_asset_id_display">-</span>
                        </div>
                    </div>

                    <!-- Form Fields -->
                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_type">Type</label>
                            <input type="text" id="view_type" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_brand">Brand</label>
                            <input type="text" id="view_brand" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_model">Model</label>
                            <input type="text" id="view_model" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_tag">Tag</label>
                            <input type="text" id="view_tag" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_department">Department</label>
                            <input type="text" id="view_department" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_status">Status</label>
                            <input type="text" id="view_status" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_hostname">Hostname</label>
                            <input type="text" id="view_hostname" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_operating_system">Operating System</label>
                            <input type="text" id="view_operating_system" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_ip_address">IP Address</label>
                            <input type="text" id="view_ip_address" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_last_ip_check">Last IP Check</label>
                            <input type="text" id="view_last_ip_check" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_serial_number">Serial Number</label>
                            <input type="text" id="view_serial_number" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_asset_id">Asset ID</label>
                            <input type="text" id="view_asset_id" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_warranty_expire">Warranty Expire</label>
                            <input type="text" id="view_warranty_expire" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_set_name">Set</label>
                            <input type="text" id="view_set_name" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="view_description">Description</label>
                        <textarea id="view_description" class="form-control" rows="3" readonly></textarea>
                    </div>

                    <!-- Audit Information -->
                    <div class="form-section">
                        <h4><i class="fas fa-history"></i> ข้อมูลการจัดการ</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="view_created_info">สร้างเมื่อ</label>
                                <input type="text" id="view_created_info" class="form-control" readonly>
                            </div>
                            <div class="form-group">
                                <label for="view_updated_info">แก้ไขล่าสุด</label>
                                <input type="text" id="view_updated_info" class="form-control" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function displayAssetDetails(asset) {
            console.log('displayAssetDetails called with:', asset);

            // อัพเดท header info
            const assetTitleInfo = document.getElementById('asset-title-info');
            if (assetTitleInfo) {
                assetTitleInfo.textContent = `${asset.type || 'Asset'} - ${asset.brand || ''} ${asset.model || ''}`.trim();
            }

            // ฟังก์ชันช่วยในการกำหนดค่าอย่างปลอดภัย
            function safeSetValue(elementId, value) {
                const element = document.getElementById(elementId);
                if (element) {
                    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                        element.value = value || '';
                    } else {
                        element.textContent = value || '-';
                    }
                } else {
                    console.warn(`Element with ID '${elementId}' not found`);
                }
            }

            // ใช้ฟังก์ชันช่วยเพื่อกำหนดค่าให้แต่ละ element
            safeSetValue('view_type', asset.type);
            safeSetValue('view_brand', asset.brand);
            safeSetValue('view_model', asset.model);
            safeSetValue('view_tag', asset.tag);
            safeSetValue('view_department', asset.department);
            safeSetValue('view_status', asset.status);
            safeSetValue('view_hostname', asset.hostname);
            safeSetValue('view_ip_address', asset.ip_address);
            safeSetValue('view_operating_system', asset.operating_system);
            safeSetValue('view_serial_number', asset.serial_number);
            safeSetValue('view_asset_id', asset.asset_id);
            safeSetValue('view_asset_id_display', asset.asset_id);
            safeSetValue('view_warranty_expire', asset.warranty_expire);
            safeSetValue('view_set_name', asset.set_name);
            safeSetValue('view_description', asset.description || '');

            // แสดงเวลาตรวจสอบ IP ล่าสุด
            const lastIpCheck = asset.last_ip_check;
            if (lastIpCheck) {
                const date = new Date(lastIpCheck);
                const formattedDate = date.toLocaleString('th-TH', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                safeSetValue('view_last_ip_check', formattedDate);
            } else {
                safeSetValue('view_last_ip_check', 'ยังไม่เคยตรวจสอบ');
            }

            // อัพเดท audit information
            const createdInfo = `${asset.created_date || '-'} โดย ${asset.created_by || '-'}`;
            const updatedInfo = `${asset.updated_date || '-'} โดย ${asset.updated_by || '-'}`;
            safeSetValue('view_created_info', createdInfo);
            safeSetValue('view_updated_info', updatedInfo);

            // อัพเดท status badge
            const statusBadgeElement = document.getElementById('view_status_badge');
            if (statusBadgeElement) {
                statusBadgeElement.textContent = asset.status || '-';
                statusBadgeElement.className = 'status-badge';

                // เพิ่ม class ตามสถานะ
                if (asset.status === 'ใช้งาน') {
                    statusBadgeElement.classList.add('active');
                } else if (asset.status === 'ชำรุด') {
                    statusBadgeElement.classList.add('damaged');
                } else if (asset.status === 'สำรอง') {
                    statusBadgeElement.classList.add('spare');
                }
            }

            // เก็บ asset ID สำหรับใช้ในการแก้ไข
            window.currentViewAssetId = asset.id;

            console.log('Asset details populated successfully');
        }

        function saveAsset() {
            // ตัวอย่าง function สำหรับบันทึกข้อมูล
            // สามารถเปิด edit modal หรือทำการบันทึกข้อมูลได้
            if (window.currentViewAssetId) {
                openEditAssetModal(window.currentViewAssetId);
                closeViewAssetModal();
            } else {
                alert('ไม่พบข้อมูล Asset ID');
            }
        }

        function saveEditAsset() {
            // ตัวอย่าง function สำหรับบันทึกการแก้ไข asset
            const form = document.getElementById('editAssetForm');
            if (form) {
                // ตรวจสอบข้อมูลในฟอร์ม
                const formData = new FormData(form);

                // ส่งข้อมูลไปยังเซิร์ฟเวอร์
                fetch('update_asset.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('บันทึกข้อมูลสำเร็จ');
                        closeEditAssetModal();
                        // รีเฟรชตาราง
                        location.reload();
                    } else {
                        alert('เกิดข้อผิดพลาด: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการเชื่อมต่อ');
                });
            }
        }

        function updateStatusIndicator(status) {
            const statusDot = document.getElementById('status-dot');
            const statusText = document.getElementById('status-text');

            if (statusDot && statusText) {
                // ลบ class เก่า
                statusDot.className = 'status-dot';

                // เพิ่ม class ใหม่ตามสถานะ
                switch(status) {
                    case 'ใช้งาน':
                        statusDot.classList.add('active');
                        statusText.textContent = 'ใช้งาน';
                        statusText.style.color = '#28a745';
                        break;
                    case 'ชำรุด':
                        statusDot.classList.add('damaged');
                        statusText.textContent = 'ชำรุด';
                        statusText.style.color = '#dc3545';
                        break;
                    case 'สำรอง':
                        statusDot.classList.add('spare');
                        statusText.textContent = 'สำรอง';
                        statusText.style.color = '#ffc107';
                        break;
                    default:
                        statusText.textContent = status || '-';
                        statusText.style.color = '#6c757d';
                }
            }
        }

        function loadRecentLogs(assetId) {
            console.log('Loading recent logs for asset ID:', assetId);

            const logsPreview = document.getElementById('asset-logs-preview');
            if (!logsPreview) {
                console.error('Logs preview container not found');
                return;
            }

            // แสดง loading
            logsPreview.innerHTML = `
                <div class="loading-logs">
                    <div class="spinner-small"></div>
                    <span>กำลังโหลด Log...</span>
                </div>
            `;

            // โหลดข้อมูล logs ล่าสุด (5 รายการ)
            fetch(`get_asset_logs.php?asset_id=${assetId}&limit=5`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.logs && data.logs.length > 0) {
                        displayRecentLogs(data.logs, assetId);
                    } else {
                        logsPreview.innerHTML = `
                            <div class="no-logs">
                                <div class="no-logs-icon">📝</div>
                                <div class="no-logs-text">ยังไม่มีประวัติการเปลี่ยนแปลง</div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error loading recent logs:', error);
                    logsPreview.innerHTML = `
                        <div class="error-logs">
                            <div class="error-icon">⚠️</div>
                            <div class="error-text">เกิดข้อผิดพลาดในการโหลด Log</div>
                        </div>
                    `;
                });
        }

        function displayRecentLogs(logs, assetId) {
            const logsPreview = document.getElementById('asset-logs-preview');
            if (!logsPreview) return;

            let logsHtml = '<div class="recent-logs-list">';

            logs.forEach(log => {
                const actionIcon = getLogActionIcon(log.action);
                const actionClass = getLogActionClass(log.action);
                const timeAgo = getTimeAgo(log.created_at);

                logsHtml += `
                    <div class="recent-log-item ${actionClass}">
                        <div class="log-icon">${actionIcon}</div>
                        <div class="log-content">
                            <div class="log-action">${log.action}</div>
                            <div class="log-user">โดย ${log.user_name || 'ไม่ระบุ'}</div>
                            <div class="log-time">${timeAgo}</div>
                        </div>
                    </div>
                `;
            });

            logsHtml += '</div>';

            // เพิ่มลิงก์ดู log ทั้งหมด
            logsHtml += `
                <div class="view-all-logs">
                    <button onclick="viewAssetLogs(${assetId})" class="btn btn-outline btn-sm">
                        📋 ดู Log ทั้งหมด (${logs.length > 0 ? 'มีข้อมูลเพิ่มเติม' : ''})
                    </button>
                </div>
            `;

            logsPreview.innerHTML = logsHtml;
        }

        function getLogActionIcon(action) {
            switch(action) {
                case 'CREATE': return '➕';
                case 'Update': return '✏️';
                case 'UPDATE': return '✏️';
                case 'Delete': return '🗑️';
                case 'DELETE': return '🗑️';
                case 'View': return '👁️';
                case 'VIEW': return '👁️';
                default: return '📝';
            }
        }

        function getLogActionClass(action) {
            switch(action) {
                case 'CREATE': return 'log-create';
                case 'Update': return 'log-update';
                case 'UPDATE': return 'log-update';
                case 'Delete': return 'log-delete';
                case 'DELETE': return 'log-delete';
                case 'View': return 'log-view';
                case 'VIEW': return 'log-view';
                default: return 'log-other';
            }
        }

        function getTimeAgo(dateString) {
            const now = new Date();
            const logDate = new Date(dateString);
            const diffInSeconds = Math.floor((now - logDate) / 1000);

            if (diffInSeconds < 60) {
                return 'เมื่อสักครู่';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `${minutes} นาทีที่แล้ว`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `${hours} ชั่วโมงที่แล้ว`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `${days} วันที่แล้ว`;
            }
        }

        function openEditAssetModal(assetId) {
            document.getElementById('editAssetModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
            loadAssetData(assetId, 'edit');
        }

        function closeEditAssetModal() {
            document.getElementById('editAssetModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function loadAssetData(assetId, mode) {
            // แสดง loading indicator
            if (mode === 'edit') {
                document.getElementById('editAssetModal').querySelector('.modal-body').innerHTML = '<div style="text-align: center; padding: 40px;"><div style="font-size: 1.2rem;">🔄 กำลังโหลดข้อมูล...</div></div>';
            }

            fetch(`get_asset_data.php?id=${assetId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Asset data received:', data);

                    if (data.success) {
                        if (mode === 'edit') {
                            // กู้คืน HTML structure สำหรับ edit modal
                            restoreEditModalStructure();
                            console.log('About to populate edit modal with:', data.asset);
                            populateEditModal(data.asset);
                        }
                    } else {
                        console.error('API returned error:', data);
                        alert('เกิดข้อผิดพลาดในการโหลดข้อมูล Asset: ' + (data.message || 'ไม่ทราบสาเหตุ'));
                        if (mode === 'edit') {
                            closeEditAssetModal();
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading asset data:', error);
                    alert('เกิดข้อผิดพลาดในการเชื่อมต่อกับเซิร์ฟเวอร์');
                    if (mode === 'edit') {
                        closeEditAssetModal();
                    }
                });
        }

        function restoreEditModalStructure() {
            const editModalBody = document.getElementById('editAssetModal').querySelector('.modal-body');
            editModalBody.innerHTML = `
                <form id="editAssetForm">
                    <input type="hidden" id="edit_asset_id_hidden" name="id">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_type">Type <span class="required">*</span></label>
                            <select id="edit_type" name="type" class="form-control" required>
                                <option value="">เลือก Type</option>
                                <option value="Desktop">Desktop</option>
                                <option value="Laptop">Laptop</option>
                                <option value="Monitor">Monitor</option>
                                <option value="All-in-one">All-in-one</option>
                                <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                                <option value="Barcode Printer">Barcode Printer</option>
                                <option value="Barcode Scanner">Barcode Scanner</option>
                                <option value="Tablet">Tablet</option>
                                <option value="UPS">UPS</option>
                                <option value="Queue">Queue</option>
                                <option value="IP Phone">IP Phone</option>
                                <option value="Teleconference">Teleconference</option>
                                <option value="Switch">Switch</option>
                                <option value="Access Point">Access Point</option>
                                <option value="Peripheral">Peripheral</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_brand">Brand</label>
                            <select id="edit_brand" name="brand" class="form-control">
                                <option value="">เลือก Brand</option>
                                <option value="-">-</option>
                                <option value="Dell">Dell</option>
                                <option value="Lenovo">Lenovo</option>
                                <option value="Microsoft">Microsoft</option>
                                <option value="Apple">Apple</option>
                                <option value="Zebra">Zebra</option>
                                <option value="HP">HP</option>
                                <option value="Philips">Philips</option>
                                <option value="Acer">Acer</option>
                                <option value="LG">LG</option>
                                <option value="Cisco">Cisco</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_model">Model</label>
                            <input type="text" id="edit_model" name="model" class="form-control" placeholder="กรอก Model">
                        </div>

                        <div class="form-group">
                            <label for="edit_tag">Tag</label>
                            <input type="text" id="edit_tag" name="tag" class="form-control" placeholder="กรอก Tag">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_department">Department</label>
                            <select id="edit_department" name="department" class="form-control">
                                <option value="">เลือก Department</option>
                                <option value="Accounting">Accounting</option>
                                <option value="Admission">Admission</option>
                                <option value="Anesthetic">Anesthetic</option>
                                <option value="BME">BME</option>
                                <option value="Cath Lab">Cath Lab</option>
                                <option value="Cardiac Care Unit">Cardiac Care Unit</option>
                                <option value="CEO">CEO</option>
                                <option value="Chivawattana">Chivawattana</option>
                                <option value="Cardiology & Neurological Clinic">Cardiology & Neurological Clinic</option>
                                <option value="Collection">Collection</option>
                                <option value="Contact Center">Contact Center</option>
                                <option value="Cashier IPD">Cashier IPD</option>
                                <option value="Cashier OPD">Cashier OPD</option>
                                <option value="Customer Service">Customer Service</option>
                                <option value="Dental">Dental</option>
                                <option value="Doctor Room">Doctor Room</option>
                                <option value="EMT">EMT</option>
                                <option value="ENT">ENT</option>
                                <option value="Endoscopy Center">Endoscopy Center</option>
                                <option value="Eye Center">Eye Center</option>
                                <option value="Emergency Room">Emergency Room</option>
                                <option value="Financial">Financial</option>
                                <option value="General Support">General Support</option>
                                <option value="Hemodialysis">Hemodialysis</option>
                                <option value="Hemodialysis VIP">Hemodialysis VIP</option>
                                <option value="Health Promotion Center">Health Promotion Center</option>
                                <option value="Human Resources">Human Resources</option>
                                <option value="Intensive Care Unit">Intensive Care Unit</option>
                                <option value="IT">IT</option>
                                <option value="Laboratory">Laboratory</option>
                                <option value="Labour Room">Labour Room</option>
                                <option value="Linen Service">Linen Service</option>
                                <option value="MAO">MAO</option>
                                <option value="Medicine Unit">Medicine Unit</option>
                                <option value="Marketing">Marketing</option>
                                <option value="Medical Record">Medical Record</option>
                                <option value="N-Health">N-Health</option>
                                <option value="Nursery">Nursery</option>
                                <option value="NSO">NSO</option>
                                <option value="Obstetrics & Gynecology">Obstetrics & Gynecology</option>
                                <option value="Operating Room">Operating Room</option>
                                <option value="Orthopedic">Orthopedic</option>
                                <option value="Pharmacy OPD">Pharmacy OPD</option>
                                <option value="Pharmacy OPD (Floor 2)">Pharmacy OPD (Floor 2)</option>
                                <option value="Pharmacy IPD">Pharmacy IPD</option>
                                <option value="Pediatrics">Pediatrics</option>
                                <option value="PUR">PUR</option>
                                <option value="Referral">Referral</option>
                                <option value="Registration">Registration</option>
                                <option value="Repair & Maintenance">Repair & Maintenance</option>
                                <option value="Rehabilitation">Rehabilitation</option>
                                <option value="Secretary">Secretary</option>
                                <option value="Sterlie">Sterlie</option>
                                <option value="Supervisor">Supervisor</option>
                                <option value="Supply">Supply</option>
                                <option value="Surgery Unit">Surgery Unit</option>
                                <option value="Quality Center">Quality Center</option>
                                <option value="Utilization Management">Utilization Management</option>
                                <option value="W5A">W5A</option>
                                <option value="W5B">W5B</option>
                                <option value="W6A">W6A</option>
                                <option value="W6B">W6B</option>
                                <option value="W7A">W7A</option>
                                <option value="W7B">W7B</option>
                                <option value="W8A">W8A</option>
                                <option value="W8B">W8B</option>
                                <option value="W9A">W9A</option>
                                <option value="W9B">W9B</option>
                                <option value="Wound Care Unit">Wound Care Unit</option>
                                <option value="Wellness Center">Wellness Center</option>
                                <option value="X-ray">X-ray</option>
                                <option value="X-ray NP">X-ray NP</option>
                                <option value="เวรเปล">เวรเปล</option>
                                <option value="Food House">Food House</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_status">Status <span class="required">*</span></label>
                            <select id="edit_status" name="status" class="form-control" required>
                                <option value="ใช้งาน">ใช้งาน</option>
                                <option value="ชำรุด">ชำรุด</option>
                                <option value="สำรอง">สำรอง</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_hostname">Hostname</label>
                            <input type="text" id="edit_hostname" name="hostname" class="form-control" placeholder="กรอก Hostname">
                        </div>

                        <div class="form-group">
                            <label for="edit_operating_system">Operating System</label>
                            <select id="edit_operating_system" name="operating_system" class="form-control">
                                <option value="-">-</option>
                                <option value="Windows 7">Windows 7</option>
                                <option value="Windows 10">Windows 10</option>
                                <option value="Windows 11">Windows 11</option>
                                <option value="MacOS">MacOS</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_ip_address">IP Address</label>
                            <div class="input-group">
                                <input type="text" id="edit_ip_address" name="ip_address" class="form-control" placeholder="กรอก IP Address หรือใช้ปุ่มอัปเดต" readonly>
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-info" onclick="updateIPAddress()" title="อัปเดต IP Address จาก Hostname">
                                        🔄 อัปเดต IP
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">IP Address จะถูกอัปเดตอัตโนมัติจาก Hostname</small>
                        </div>

                        <div class="form-group">
                            <label for="edit_last_ip_check">Last IP Check</label>
                            <input type="text" id="edit_last_ip_check" class="form-control" readonly placeholder="ยังไม่เคยตรวจสอบ">
                            <small class="form-text text-muted">เวลาที่ตรวจสอบ IP Address ล่าสุด</small>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_serial_number">Serial Number</label>
                            <input type="text" id="edit_serial_number" name="serial_number" class="form-control" placeholder="กรอก Serial Number">
                        </div>

                        <div class="form-group">
                            <label for="edit_asset_id">Asset ID</label>
                            <input type="text" id="edit_asset_id" name="asset_id" class="form-control" placeholder="กรอก Asset ID">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_warranty_expire">Warranty Expire</label>
                            <input type="date" id="edit_warranty_expire" name="warranty_expire" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="edit_set_name">Set</label>
                            <input type="text" id="edit_set_name" name="set_name" class="form-control" placeholder="กรอกชื่อ Set">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_description">Description</label>
                        <textarea id="edit_description" name="description" class="form-control" rows="3" placeholder="กรอก Description"></textarea>
                    </div>
                </form>
            `;
        }



        function populateEditModal(asset) {
            document.getElementById('edit_asset_id_hidden').value = asset.id || '';
            document.getElementById('edit_type').value = asset.type || '';
            document.getElementById('edit_brand').value = asset.brand || '';
            document.getElementById('edit_model').value = asset.model || '';
            document.getElementById('edit_tag').value = asset.tag || '';
            document.getElementById('edit_department').value = asset.department || '';
            document.getElementById('edit_status').value = asset.status || '';
            document.getElementById('edit_hostname').value = asset.hostname || '';
            document.getElementById('edit_ip_address').value = asset.ip_address || '';
            document.getElementById('edit_operating_system').value = asset.operating_system || '';
            document.getElementById('edit_serial_number').value = asset.serial_number || '';
            document.getElementById('edit_asset_id').value = asset.asset_id || '';
            document.getElementById('edit_warranty_expire').value = asset.warranty_expire || '';
            document.getElementById('edit_description').value = asset.description || '';
            document.getElementById('edit_set_name').value = asset.set_name || '';

            // แสดงเวลาตรวจสอบ IP ล่าสุด
            const lastIpCheck = asset.last_ip_check;
            if (lastIpCheck) {
                const date = new Date(lastIpCheck);
                const formattedDate = date.toLocaleString('th-TH', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                document.getElementById('edit_last_ip_check').value = formattedDate;
            } else {
                document.getElementById('edit_last_ip_check').value = 'ยังไม่เคยตรวจสอบ';
            }
        }

        function saveAsset() {
            const formData = new FormData(document.getElementById('editAssetForm'));

            fetch('update_asset.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('อัพเดท Asset สำเร็จ');
                    closeEditAssetModal();
                    location.reload(); // รีโหลดหน้าเพื่อแสดงข้อมูลใหม่
                } else {
                    alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถอัพเดทได้'));
                }
            })
            .catch(error => {
                console.error('Error updating asset:', error);
                alert('เกิดข้อผิดพลาดในการอัพเดท Asset');
            });
        }

        // ฟังก์ชันอัปเดต IP Address จาก Hostname
        function updateIPAddress() {
            const hostname = document.getElementById('edit_hostname').value.trim();
            const assetId = document.getElementById('edit_asset_id_hidden').value;

            if (!hostname) {
                alert('กรุณากรอก Hostname ก่อนอัปเดต IP Address');
                return;
            }

            // แสดงสถานะกำลังโหลด
            const ipField = document.getElementById('edit_ip_address');
            const lastCheckField = document.getElementById('edit_last_ip_check');
            const updateButton = document.querySelector('button[onclick="updateIPAddress()"]');

            const originalButtonText = updateButton.innerHTML;
            updateButton.innerHTML = '🔄 กำลังอัปเดต...';
            updateButton.disabled = true;

            ipField.value = 'กำลังค้นหา IP Address...';

            // ส่งคำขอไปยัง server
            const formData = new FormData();
            formData.append('action', 'update_ip');
            formData.append('asset_id', assetId);
            formData.append('hostname', hostname);

            fetch('update_asset_ip.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    ipField.value = data.ip_address;

                    // อัปเดตเวลาตรวจสอบ
                    const now = new Date();
                    const formattedDate = now.toLocaleString('th-TH', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                    lastCheckField.value = formattedDate;

                    alert(`อัปเดต IP Address สำเร็จ!\nIP Address: ${data.ip_address}\nMethod: ${data.method}\nTime: ${data.execution_time}ms`);
                } else {
                    ipField.value = '';
                    alert('ไม่สามารถหา IP Address ได้: ' + (data.error || 'ไม่ทราบสาเหตุ'));
                }
            })
            .catch(error => {
                console.error('Error updating IP:', error);
                ipField.value = '';
                alert('เกิดข้อผิดพลาดในการอัปเดต IP Address');
            })
            .finally(() => {
                // คืนค่าปุ่มเดิม
                updateButton.innerHTML = originalButtonText;
                updateButton.disabled = false;
            });
        }


    </script>

    <!-- Profile Modal -->
    <div id="profileModal" class="modal">
        <div class="modal-content profile-modal" style="max-width: 650px;">
            <div class="modal-header">
                <h2>
                    <span style="font-size: 1.8rem;">👤</span>
                    โปรไฟล์ผู้ใช้
                </h2>
                <span class="close" onclick="closeProfileModal()">&times;</span>
            </div>

            <!-- View Mode -->
            <div id="profileViewMode" class="modal-body">
                <div class="profile-info">
                    <div class="profile-avatar">
                        <div class="avatar-circle">
                            <span style="font-size: 3.5rem;">👤</span>
                        </div>
                    </div>

                    <div class="profile-user-info">
                        <div class="profile-username" id="profile_username_display">-</div>
                        <div class="profile-role-display" id="profile_role_display">
                            <span>👑</span>
                            <span id="profile_role_text">-</span>
                        </div>
                    </div>

                    <div class="profile-details">
                        <div class="detail-row">
                            <label>🏷️ Username</label>
                            <span id="profile_username">-</span>
                        </div>

                        <div class="detail-row">
                            <label>👤 ชื่อ-นามสกุล</label>
                            <span id="profile_full_name">-</span>
                        </div>

                        <div class="detail-row">
                            <label>📧 Email</label>
                            <span id="profile_email">-</span>
                        </div>

                        <div class="detail-row">
                            <label>🎭 บทบาท</label>
                            <span class="badge-container">
                                <span id="profile_role" class="role-badge">-</span>
                            </span>
                        </div>

                        <div class="detail-row">
                            <label>🟢 สถานะ</label>
                            <span class="badge-container">
                                <span id="profile_status" class="status-badge">-</span>
                            </span>
                        </div>

                        <div class="detail-row">
                            <label>🕐 เข้าสู่ระบบล่าสุด</label>
                            <span id="profile_last_login">-</span>
                        </div>

                        <div class="detail-row">
                            <label>📅 วันที่สร้างบัญชี</label>
                            <span id="profile_created_date">-</span>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="toggleEditMode()">
                        ✏️ แก้ไขโปรไฟล์
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeProfileModal()">ปิด</button>
                </div>
            </div>

            <!-- Edit Mode -->
            <div id="profileEditMode" class="modal-body" style="display: none;">
                <div class="profile-edit-form">
                    <form id="editProfileForm">
                        <div class="form-group">
                            <label for="edit_username">🏷️ Username</label>
                            <input type="text" id="edit_username" name="username" class="form-control" readonly>
                            <small class="form-text">Username ไม่สามารถเปลี่ยนแปลงได้</small>
                        </div>

                        <div class="form-group">
                            <label for="edit_full_name">👤 ชื่อ-นามสกุล</label>
                            <input type="text" id="edit_full_name" name="full_name" class="form-control" required placeholder="กรอกชื่อ-นามสกุลของคุณ">
                        </div>

                        <div class="form-group">
                            <label for="edit_email">📧 Email</label>
                            <input type="email" id="edit_email" name="email" class="form-control" placeholder="กรอกอีเมลของคุณ">
                        </div>

                        <div class="form-group">
                            <label for="edit_current_password">🔐 รหัสผ่านปัจจุบัน</label>
                            <input type="password" id="edit_current_password" name="current_password" class="form-control" placeholder="กรอกรหัสผ่านปัจจุบัน">
                            <small class="form-text">กรอกเฉพาะเมื่อต้องการเปลี่ยนรหัสผ่าน</small>
                        </div>

                        <div class="form-group">
                            <label for="edit_new_password">🔑 รหัสผ่านใหม่</label>
                            <input type="password" id="edit_new_password" name="new_password" class="form-control" placeholder="กรอกรหัสผ่านใหม่">
                        </div>

                        <div class="form-group">
                            <label for="edit_confirm_password">✅ ยืนยันรหัสผ่านใหม่</label>
                            <input type="password" id="edit_confirm_password" name="confirm_password" class="form-control" placeholder="กรอกรหัสผ่านใหม่อีกครั้ง">
                        </div>
                    </form>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-success" onclick="saveProfile()">
                        💾 บันทึก
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="toggleEditMode()">ยกเลิก</button>
                </div>
            </div>
        </div>
    </div>



    <!-- View Asset Modal -->
    <div id="viewAssetModal" class="modal">
        <div class="modal-content asset-modal">
            <div class="modal-header">
                <div class="header-text">
                    <h2>👁️ รายละเอียด Asset</h2>
                    <p id="asset-title-info">กำลังโหลดข้อมูล...</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-warning" onclick="openEditAssetModalFromView()" title="แก้ไข">
                        ✏️ แก้ไข
                    </button>
                    <span class="close" onclick="closeViewAssetModal()" title="ปิด">&times;</span>
                </div>
            </div>

            <div class="modal-body">
                <div class="form-container">
                    <!-- Status Display -->
                    <div class="status-display-row">
                        <div class="status-badge-container">
                            <span class="status-badge" id="view_status_badge">-</span>
                        </div>
                        <div class="asset-id-container">
                            <label>Asset ID:</label>
                            <span class="asset-id-display" id="view_asset_id_display">-</span>
                        </div>
                    </div>

                    <!-- Form Fields -->
                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_type">Type</label>
                            <input type="text" id="view_type" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_brand">Brand</label>
                            <input type="text" id="view_brand" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_model">Model</label>
                            <input type="text" id="view_model" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_tag">Tag</label>
                            <input type="text" id="view_tag" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_department">Department</label>
                            <input type="text" id="view_department" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_status">Status</label>
                            <input type="text" id="view_status" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_hostname">Hostname</label>
                            <input type="text" id="view_hostname" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_operating_system">Operating System</label>
                            <input type="text" id="view_operating_system" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_ip_address">IP Address</label>
                            <input type="text" id="view_ip_address" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_last_ip_check">Last IP Check</label>
                            <input type="text" id="view_last_ip_check" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_serial_number">Serial Number</label>
                            <input type="text" id="view_serial_number" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_asset_id">Asset ID</label>
                            <input type="text" id="view_asset_id" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="view_warranty_expire">Warranty Expire</label>
                            <input type="text" id="view_warranty_expire" class="form-control" readonly>
                        </div>
                        <div class="form-group">
                            <label for="view_set_name">Set</label>
                            <input type="text" id="view_set_name" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="view_description">Description</label>
                        <textarea id="view_description" class="form-control" rows="3" readonly></textarea>
                    </div>

                    <!-- Audit Information -->
                    <div class="form-section">
                        <h4><i class="fas fa-history"></i> ข้อมูลการจัดการ</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="view_created_info">สร้างเมื่อ</label>
                                <input type="text" id="view_created_info" class="form-control" readonly>
                            </div>
                            <div class="form-group">
                                <label for="view_updated_info">แก้ไขล่าสุด</label>
                                <input type="text" id="view_updated_info" class="form-control" readonly>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Edit Asset Modal -->
    <div id="editAssetModal" class="modal">
        <div class="modal-content asset-modal">
            <div class="modal-header">
                <div class="header-text">
                    <h2>✏️ แก้ไข Asset</h2>
                </div>
                <div class="header-actions">
                    <button class="btn btn-success" onclick="saveEditAsset()" title="บันทึก">
                        บันทึก
                    </button>
                    <span class="close" onclick="closeEditAssetModal()" title="ปิด">&times;</span>
                </div>
            </div>
            <div class="modal-body">
                <form id="editAssetForm">
                    <input type="hidden" id="edit_asset_id_hidden" name="id">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_type">Type <span class="required">*</span></label>
                            <select id="edit_type" name="type" class="form-control" required>
                                <option value="">เลือก Type</option>
                                <option value="Desktop">Desktop</option>
                                <option value="Laptop">Laptop</option>
                                <option value="Monitor">Monitor</option>
                                <option value="All-in-one">All-in-one</option>
                                <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                                <option value="Barcode Printer">Barcode Printer</option>
                                <option value="Barcode Scanner">Barcode Scanner</option>
                                <option value="Tablet">Tablet</option>
                                <option value="UPS">UPS</option>
                                <option value="Queue">Queue</option>
                                <option value="IP Phone">IP Phone</option>
                                <option value="Teleconference">Teleconference</option>
                                <option value="Switch">Switch</option>
                                <option value="Access Point">Access Point</option>
                                <option value="Peripheral">Peripheral</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_brand">Brand</label>
                            <select id="edit_brand" name="brand" class="form-control">
                                <option value="">เลือก Brand</option>
                                <option value="-">-</option>
                                <option value="Dell">Dell</option>
                                <option value="Lenovo">Lenovo</option>
                                <option value="Microsoft">Microsoft</option>
                                <option value="Apple">Apple</option>
                                <option value="Zebra">Zebra</option>
                                <option value="HP">HP</option>
                                <option value="Philips">Philips</option>
                                <option value="Acer">Acer</option>
                                <option value="LG">LG</option>
                                <option value="Cisco">Cisco</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_model">Model</label>
                            <input type="text" id="edit_model" name="model" class="form-control" placeholder="กรอก Model">
                        </div>

                        <div class="form-group">
                            <label for="edit_tag">Tag</label>
                            <input type="text" id="edit_tag" name="tag" class="form-control" placeholder="กรอก Tag">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_department">Department</label>
                            <select id="edit_department" name="department" class="form-control">
                                <option value="">เลือก Department</option>
                                <option value="Accounting">Accounting</option>
                                <option value="Admission">Admission</option>
                                <option value="Anesthetic">Anesthetic</option>
                                <option value="BME">BME</option>
                                <option value="Cath Lab">Cath Lab</option>
                                <option value="Cardiac Care Unit">Cardiac Care Unit</option>
                                <option value="CEO">CEO</option>
                                <option value="Chivawattana">Chivawattana</option>
                                <option value="Cardiology & Neurological Clinic">Cardiology & Neurological Clinic</option>
                                <option value="Collection">Collection</option>
                                <option value="Contact Center">Contact Center</option>
                                <option value="Cashier IPD">Cashier IPD</option>
                                <option value="Cashier OPD">Cashier OPD</option>
                                <option value="Customer Service">Customer Service</option>
                                <option value="Dental">Dental</option>
                                <option value="Doctor Room">Doctor Room</option>
                                <option value="EMT">EMT</option>
                                <option value="ENT">ENT</option>
                                <option value="Endoscopy Center">Endoscopy Center</option>
                                <option value="Eye Center">Eye Center</option>
                                <option value="Emergency Room">Emergency Room</option>
                                <option value="Financial">Financial</option>
                                <option value="General Support">General Support</option>
                                <option value="Hemodialysis">Hemodialysis</option>
                                <option value="Hemodialysis VIP">Hemodialysis VIP</option>
                                <option value="Health Promotion Center">Health Promotion Center</option>
                                <option value="Human Resources">Human Resources</option>
                                <option value="Intensive Care Unit">Intensive Care Unit</option>
                                <option value="IT">IT</option>
                                <option value="Laboratory">Laboratory</option>
                                <option value="Labour Room">Labour Room</option>
                                <option value="Linen Service">Linen Service</option>
                                <option value="MAO">MAO</option>
                                <option value="Medicine Unit">Medicine Unit</option>
                                <option value="Marketing">Marketing</option>
                                <option value="Medical Record">Medical Record</option>
                                <option value="N-Health">N-Health</option>
                                <option value="Nursery">Nursery</option>
                                <option value="NSO">NSO</option>
                                <option value="Obstetrics & Gynecology">Obstetrics & Gynecology</option>
                                <option value="Operating Room">Operating Room</option>
                                <option value="Orthopedic">Orthopedic</option>
                                <option value="Pharmacy OPD">Pharmacy OPD</option>
                                <option value="Pharmacy OPD (Floor 2)">Pharmacy OPD (Floor 2)</option>
                                <option value="Pharmacy IPD">Pharmacy IPD</option>
                                <option value="Pediatrics">Pediatrics</option>
                                <option value="PUR">PUR</option>
                                <option value="Referral">Referral</option>
                                <option value="Registration">Registration</option>
                                <option value="Repair & Maintenance">Repair & Maintenance</option>
                                <option value="Rehabilitation">Rehabilitation</option>
                                <option value="Secretary">Secretary</option>
                                <option value="Sterlie">Sterlie</option>
                                <option value="Supervisor">Supervisor</option>
                                <option value="Supply">Supply</option>
                                <option value="Surgery Unit">Surgery Unit</option>
                                <option value="Quality Center">Quality Center</option>
                                <option value="Utilization Management">Utilization Management</option>
                                <option value="W5A">W5A</option>
                                <option value="W5B">W5B</option>
                                <option value="W6A">W6A</option>
                                <option value="W6B">W6B</option>
                                <option value="W7A">W7A</option>
                                <option value="W7B">W7B</option>
                                <option value="W8A">W8A</option>
                                <option value="W8B">W8B</option>
                                <option value="W9A">W9A</option>
                                <option value="W9B">W9B</option>
                                <option value="Wound Care Unit">Wound Care Unit</option>
                                <option value="Wellness Center">Wellness Center</option>
                                <option value="X-ray">X-ray</option>
                                <option value="X-ray NP">X-ray NP</option>
                                <option value="เวรเปล">เวรเปล</option>
                                <option value="Food House">Food House</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_status">Status <span class="required">*</span></label>
                            <select id="edit_status" name="status" class="form-control" required>
                                <option value="ใช้งาน">ใช้งาน</option>
                                <option value="ชำรุด">ชำรุด</option>
                                <option value="สำรอง">สำรอง</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_hostname">Hostname</label>
                            <input type="text" id="edit_hostname" name="hostname" class="form-control" placeholder="กรอก Hostname">
                        </div>

                        <div class="form-group">
                            <label for="edit_operating_system">Operating System</label>
                            <select id="edit_operating_system" name="operating_system" class="form-control">
                                <option value="-">-</option>
                                <option value="Windows 7">Windows 7</option>
                                <option value="Windows 10">Windows 10</option>
                                <option value="Windows 11">Windows 11</option>
                                <option value="MacOS">MacOS</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_ip_address">IP Address</label>
                            <div class="input-group">
                                <input type="text" id="edit_ip_address" name="ip_address" class="form-control" placeholder="กรอก IP Address หรือใช้ปุ่มอัปเดต" readonly>
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-info" onclick="updateIPAddress()" title="อัปเดต IP Address จาก Hostname">
                                        🔄 อัปเดต IP
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">IP Address จะถูกอัปเดตอัตโนมัติจาก Hostname</small>
                        </div>

                        <div class="form-group">
                            <label for="edit_last_ip_check">Last IP Check</label>
                            <input type="text" id="edit_last_ip_check" class="form-control" readonly placeholder="ยังไม่เคยตรวจสอบ">
                            <small class="form-text text-muted">เวลาที่ตรวจสอบ IP Address ล่าสุด</small>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_serial_number">Serial Number</label>
                            <input type="text" id="edit_serial_number" name="serial_number" class="form-control" placeholder="กรอก Serial Number">
                        </div>

                        <div class="form-group">
                            <label for="edit_asset_id">Asset ID</label>
                            <input type="text" id="edit_asset_id" name="asset_id" class="form-control" placeholder="กรอก Asset ID">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_warranty_expire">Warranty Expire</label>
                            <input type="date" id="edit_warranty_expire" name="warranty_expire" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="edit_set_name">Set</label>
                            <input type="text" id="edit_set_name" name="set_name" class="form-control" placeholder="กรอกชื่อ Set">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_description">Description</label>
                        <textarea id="edit_description" name="description" class="form-control" rows="3" placeholder="กรอก Description"></textarea>
                    </div>
                </form>
            </div>

        </div>
    </div>

    <!-- Tools Modal -->
    <div id="toolsModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-tools"></i> เครื่องมือ</h2>
                <span class="close" onclick="closeToolsModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="tools-grid">
                    <?php if (isAdmin()): ?>
                        <div class="tool-item">
                            <a href="import_sample.php" class="tool-link">
                                <div class="tool-icon">
                                    <i class="fas fa-file-import"></i>
                                </div>
                                <div class="tool-info">
                                    <h3>Import Sample CSV</h3>
                                    <p>นำเข้าข้อมูล Asset จากไฟล์ CSV</p>
                                </div>
                            </a>
                        </div>
                    <?php endif; ?>

                    <div class="tool-item">
                        <a href="#" onclick="openExportModal(); closeToolsModal();" class="tool-link">
                            <div class="tool-icon">
                                <i class="fas fa-file-export"></i>
                            </div>
                            <div class="tool-info">
                                <h3>Export ข้อมูล</h3>
                                <p>ส่งออกข้อมูล Asset เป็นไฟล์</p>
                            </div>
                        </a>
                    </div>

                    <?php if (isAdmin()): ?>
                        <div class="tool-item">
                            <a href="backup_settings.php" class="tool-link">
                                <div class="tool-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="tool-info">
                                    <h3>Auto Backup SQL</h3>
                                    <p>ตั้งค่าระบบสำรองข้อมูลอัตโนมัติ</p>
                                </div>
                            </a>
                        </div>

                        <div class="tool-item">
                            <a href="#" onclick="clearSystemCacheFromTools(); closeToolsModal();" class="tool-link">
                                <div class="tool-icon">
                                    <i class="fas fa-broom"></i>
                                </div>
                                <div class="tool-info">
                                    <h3>ล้าง System Cache</h3>
                                    <p>ล้างแคชระบบและเบราว์เซอร์</p>
                                </div>
                            </a>
                        </div>

                        <div class="tool-item">
                            <a href="telegram_settings.php" class="tool-link">
                                <div class="tool-icon">
                                    <i class="fab fa-telegram"></i>
                                </div>
                                <div class="tool-info">
                                    <h3>ตั้งค่า Telegram</h3>
                                    <p>ตั้งค่าการแจ้งเตือนผ่าน Telegram</p>
                                </div>
                            </a>
                        </div>

                        <div class="tool-item">
                            <a href="import_update_assets.php" class="tool-link">
                                <div class="tool-icon">
                                    <i class="fas fa-file-import"></i>
                                </div>
                                <div class="tool-info">
                                    <h3>Import Update Assets</h3>
                                    <p>อัปเดต Assets แบบ Import จากไฟล์</p>
                                </div>
                            </a>
                        </div>
                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div id="exportModal" class="modal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <div class="header-text">
                    <h2><i class="fas fa-download me-3"></i>เลือกรูปแบบการ Export</h2>
                </div>
                <div class="header-actions">
                    <span class="close" onclick="closeExportModal()" title="ปิด">&times;</span>
                </div>
            </div>
            <div class="modal-body">
                <!-- Filter Info -->
                <div id="exportFilterInfo" class="alert alert-info mb-4" style="display: none;">
                    <h6><i class="fas fa-filter me-2"></i>เงื่อนไขการกรองที่ใช้:</h6>
                    <p id="exportFilterText" class="mb-0"></p>
                </div>

                <div class="alert alert-warning mb-4" id="exportNoFilter" style="display: none;">
                    <p class="mb-0"><i class="fas fa-info-circle me-2"></i>จะส่งออกข้อมูลทั้งหมดในระบบ</p>
                </div>

                <!-- Export Options -->
                <div class="row g-4">
                    <!-- CSV Export -->
                    <div class="col-lg-4 col-md-6">
                        <div class="card export-card h-100" onclick="exportData('csv')">
                            <div class="export-preview preview-csv">
                                <i class="fas fa-file-csv preview-icon"></i>
                            </div>
                            <div class="card-body p-4">
                                <h5 class="export-title">CSV (Comma Separated Values)</h5>
                                <p class="export-description">
                                    รูปแบบไฟล์ที่เปิดได้ในโปรแกรม Excel, Google Sheets และโปรแกรมตารางคำนวณอื่นๆ
                                </p>
                                <ul class="feature-list">
                                    <li><i class="fas fa-check"></i> เปิดได้ใน Excel</li>
                                    <li><i class="fas fa-check"></i> ขนาดไฟล์เล็ก</li>
                                    <li><i class="fas fa-check"></i> รองรับภาษาไทย</li>
                                    <li><i class="fas fa-check"></i> เหมาะสำหรับการวิเคราะห์ข้อมูล</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Excel Export -->
                    <div class="col-lg-4 col-md-6">
                        <div class="card export-card h-100" onclick="exportData('excel')">
                            <div class="export-preview preview-excel">
                                <i class="fas fa-file-excel preview-icon"></i>
                            </div>
                            <div class="card-body p-4">
                                <h5 class="export-title">Excel (.xls)</h5>
                                <p class="export-description">
                                    ไฟล์ Excel พร้อมการจัดรูปแบบ เหมาะสำหรับการนำเสนอและการทำงานใน Microsoft Excel
                                </p>
                                <ul class="feature-list">
                                    <li><i class="fas fa-check"></i> จัดรูปแบบสวยงาม</li>
                                    <li><i class="fas fa-check"></i> ปรับความกว้างคอลัมน์อัตโนมัติ</li>
                                    <li><i class="fas fa-check"></i> หัวตารางมีสี</li>
                                    <li><i class="fas fa-check"></i> เหมาะสำหรับการนำเสนอ</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- JSON Export -->
                    <div class="col-lg-4 col-md-6">
                        <div class="card export-card h-100">
                            <div class="export-preview preview-json">
                                <i class="fas fa-file-code preview-icon"></i>
                            </div>
                            <div class="card-body p-4">
                                <h5 class="export-title">JSON (JavaScript Object Notation)</h5>
                                <p class="export-description">
                                    รูปแบบข้อมูลสำหรับการพัฒนาโปรแกรม API และการแลกเปลี่ยนข้อมูลระหว่างระบบ
                                </p>
                                <ul class="feature-list">
                                    <li><i class="fas fa-check"></i> รองรับการพัฒนา API</li>
                                    <li><i class="fas fa-check"></i> มี Metadata ครบถ้วน</li>
                                    <li><i class="fas fa-check"></i> รูปแบบมาตรฐาน</li>
                                    <li><i class="fas fa-check"></i> เหมาะสำหรับนักพัฒนา</li>
                                </ul>
                                <div class="text-center mt-3">
                                    <button class="btn btn-sm btn-outline-primary me-2" onclick="event.stopPropagation(); viewData('json')">
                                        <i class="fas fa-eye me-1"></i>ดูข้อมูล
                                    </button>
                                    <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); exportData('json')">
                                        <i class="fas fa-download me-1"></i>ดาวน์โหลด
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Additional View Asset Modal Styles */
        .status-display-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .status-badge-container {
            display: flex;
            align-items: center;
        }

        .asset-id-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .asset-id-container label {
            font-weight: bold;
            margin: 0;
            color: #495057;
        }

        .asset-id-display {
            background: #007bff;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-weight: bold;
            font-family: monospace;
        }

        .form-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
        }

        .form-section h4 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .form-control[readonly] {
            background-color: #f8f9fa;
            border-color: #e9ecef;
            color: #495057;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-actions .btn {
            padding: 8px 16px;
            font-size: 14px;
        }

        /* Input Group Styles for IP Address */
        .input-group {
            display: flex;
            width: 100%;
        }

        .input-group .form-control {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-right: 0;
        }

        .input-group-append {
            display: flex;
        }

        .input-group-append .btn {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: 1px solid #ced4da;
            white-space: nowrap;
            font-size: 0.875rem;
            padding: 6px 12px;
        }

        .input-group-append .btn:hover {
            background-color: #17a2b8;
            border-color: #138496;
        }

        .input-group-append .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    </style>

</body>
</html>
