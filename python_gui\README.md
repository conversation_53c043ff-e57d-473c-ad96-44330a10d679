# 🏢 Asset Management System - Python GUI

A modern Python GUI application for managing assets with Add, Edit, and Delete functionality.

## ✨ Features

- **Modern GUI**: Built with ttkbootstrap for a modern, professional look
- **Add Assets**: Easy-to-use dialog for adding new assets
- **Edit Assets**: Comprehensive editing with tabbed interface
- **Delete Assets**: Safe deletion with confirmation dialogs
- **Search & Filter**: Real-time search across all asset fields
- **Database Integration**: Direct connection to MySQL database
- **Responsive Design**: Adapts to different screen sizes

## 🛠️ Requirements

- Python 3.7 or higher
- MySQL database (same as the PHP application)
- Required Python packages (see requirements.txt)

## 📦 Installation

1. **Clone or download the python_gui folder**

2. **Navigate to the python_gui directory**
   ```bash
   cd python_gui
   ```

3. **Run the setup script**
   ```bash
   python setup.py
   ```
   
   This will:
   - Install all required Python packages
   - Test database connection
   - Create database if needed

4. **Run the application**
   ```bash
   python asset_gui.py
   ```

## ⚙️ Configuration

The application uses the same database as your PHP Asset Management System. 

Database configuration is in `database_config.py`:

```python
DB_CONFIG = {
    'host': 'localhost',
    'database': 'asset_management',
    'user': 'root',
    'password': 'Wxmujwsofu@1234',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci'
}
```

Update these settings to match your database configuration.

## 🎯 Usage

### Main Window
- **Search**: Type in the search box to filter assets in real-time
- **Add Asset**: Click "➕ Add Asset" to open the add dialog
- **Edit Asset**: Select an asset and click "✏️ Edit Asset" or double-click
- **Delete Asset**: Select an asset and click "🗑️ Delete Asset"
- **Refresh**: Click "🔄 Refresh" to reload data from database

### Add Asset Dialog
- **Basic Information**: Type, Brand, Model, Tag, Department, Status
- **Technical Info**: Hostname, OS, Serial Number, Asset ID, Warranty, Set
- **Additional Info**: Description field for detailed notes
- **Required Fields**: Only "Type" is required (marked with *)

### Edit Asset Dialog
- Same interface as Add Asset but pre-populated with existing data
- **Asset Info Tab**: Shows creation and modification history
- All fields can be updated except the Asset ID

### Delete Asset
- Shows confirmation dialog with asset details
- Deletion cannot be undone
- Asset is permanently removed from database

## 🗂️ File Structure

```
python_gui/
├── asset_gui.py           # Main application window
├── add_asset_dialog.py    # Add asset dialog
├── edit_asset_dialog.py   # Edit asset dialog
├── asset_model.py         # Database operations
├── database_config.py     # Database configuration
├── setup.py              # Setup and installation script
├── requirements.txt       # Python package requirements
└── README.md             # This file
```

## 🔧 Dependencies

- **mysql-connector-python**: MySQL database connectivity
- **ttkbootstrap**: Modern tkinter themes and widgets
- **Pillow**: Image processing (for icons)
- **python-dateutil**: Date handling utilities

## 🚀 Features in Detail

### Modern UI
- Professional appearance with ttkbootstrap themes
- Responsive layout that adapts to window size
- Intuitive icons and color coding
- Tabbed interface for organized data entry

### Database Integration
- Direct connection to existing MySQL database
- Supports all asset fields from the PHP application
- Automatic connection management
- Error handling and logging

### Search Functionality
- Real-time search as you type
- Searches across multiple fields:
  - Type, Brand, Model
  - Tag, Department, Hostname
  - Serial Number, Asset ID

### Data Validation
- Required field validation
- Date format validation
- Error messages for invalid data
- Prevents duplicate entries where applicable

## 🐛 Troubleshooting

### Database Connection Issues
1. Check database credentials in `database_config.py`
2. Ensure MySQL server is running
3. Verify database name exists
4. Check network connectivity

### Package Installation Issues
1. Ensure Python 3.7+ is installed
2. Update pip: `python -m pip install --upgrade pip`
3. Install packages manually: `pip install -r requirements.txt`

### Application Errors
1. Check the console for error messages
2. Verify database table structure matches expectations
3. Ensure all required files are present

## 📝 Notes

- This GUI application works with the same database as the PHP web application
- Changes made in the GUI will be reflected in the web application and vice versa
- The application maintains the same data structure and relationships
- All timestamps are in Thailand timezone (+07:00)

## 🔄 Updates

To update the application:
1. Download new files
2. Run `python setup.py` again
3. Restart the application

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Verify database connectivity
3. Check Python and package versions
4. Review error messages in console

---

**Enjoy using the Asset Management Python GUI! 🎉**
