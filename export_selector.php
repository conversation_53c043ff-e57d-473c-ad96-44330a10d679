<?php
// รับพารามิเตอร์จาก URL
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';
$filter_serial = $_GET['filter_serial'] ?? '';

// สร้าง query string สำหรับส่งต่อไปยังการ export
$queryParams = [];
if (!empty($search)) $queryParams['search'] = $search;
if (!empty($filter_type)) $queryParams['filter_type'] = $filter_type;
if (!empty($filter_brand)) $queryParams['filter_brand'] = $filter_brand;
if (!empty($filter_department)) $queryParams['filter_department'] = $filter_department;
if (!empty($filter_status)) $queryParams['filter_status'] = $filter_status;
if (!empty($filter_os)) $queryParams['filter_os'] = $filter_os;
if (!empty($filter_serial)) $queryParams['filter_serial'] = $filter_serial;

$queryString = !empty($queryParams) ? '?' . http_build_query($queryParams) : '';

// สร้างข้อมูลสำหรับแสดงเงื่อนไขที่ใช้
$filterInfo = [];
if (!empty($search)) $filterInfo[] = "ค้นหา: \"$search\"";
if (!empty($filter_type)) $filterInfo[] = "ประเภท: $filter_type";
if (!empty($filter_brand)) $filterInfo[] = "ยี่ห้อ: $filter_brand";
if (!empty($filter_department)) $filterInfo[] = "แผนก: $filter_department";
if (!empty($filter_status)) $filterInfo[] = "สถานะ: $filter_status";
if (!empty($filter_os)) $filterInfo[] = "ระบบปฏิบัติการ: $filter_os";
if (!empty($filter_serial)) $filterInfo[] = "หมายเลขเครื่อง: \"$filter_serial\"";
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เลือกรูปแบบการ Export - Asset Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .export-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
        }
        
        .export-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .export-preview {
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .preview-csv {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        
        .preview-excel {
            background: linear-gradient(45deg, #198754, #0d6efd);
        }
        
        .preview-json {
            background: linear-gradient(45deg, #fd7e14, #dc3545);
        }
        
        .preview-icon {
            font-size: 3rem;
            color: rgba(255,255,255,0.9);
        }
        
        .export-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .export-description {
            color: #7f8c8d;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .btn-export {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-export:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
            transform: scale(1.05);
            color: white;
        }
        
        .btn-view {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-view:hover {
            background: linear-gradient(45deg, #6f42c1, #17a2b8);
            transform: scale(1.05);
            color: white;
        }
        
        .container-custom {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header-section {
            text-align: center;
            margin-bottom: 50px;
            color: white;
        }
        
        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        
        .feature-list li {
            padding: 3px 0;
            color: #6c757d;
            font-size: 0.85rem;
        }
        
        .feature-list li i {
            color: #28a745;
            margin-right: 8px;
            width: 12px;
        }
    </style>
</head>
<body>
    <div class="container-custom">
        <!-- Header Section -->
        <div class="header-section">
            <h1 class="header-title">
                <i class="fas fa-download me-3"></i>
                เลือกรูปแบบการ Export
            </h1>
            <p class="header-subtitle">เลือกรูปแบบการส่งออกข้อมูลที่เหมาะสมกับความต้องการของคุณ</p>
            
            <?php if (!empty($filterInfo)): ?>
            <div class="alert alert-info mt-3" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; border-radius: 10px;">
                <h6><i class="fas fa-filter me-2"></i>เงื่อนไขการกรองที่ใช้:</h6>
                <p class="mb-0"><?= implode(" | ", $filterInfo) ?></p>
            </div>
            <?php else: ?>
            <div class="alert alert-warning mt-3" style="background: rgba(255,193,7,0.2); border: 1px solid rgba(255,193,7,0.3); color: white; border-radius: 10px;">
                <p class="mb-0"><i class="fas fa-info-circle me-2"></i>จะส่งออกข้อมูลทั้งหมดในระบบ</p>
            </div>
            <?php endif; ?>
        </div>

        <!-- Export Options -->
        <div class="row g-4">
            <!-- CSV Export -->
            <div class="col-lg-4 col-md-6">
                <div class="card export-card h-100">
                    <div class="export-preview preview-csv">
                        <i class="fas fa-file-csv preview-icon"></i>
                    </div>
                    <div class="card-body p-4">
                        <h5 class="export-title">CSV (Comma Separated Values)</h5>
                        <p class="export-description">
                            รูปแบบไฟล์ที่เปิดได้ในโปรแกรม Excel, Google Sheets และโปรแกรมตารางคำนวณอื่นๆ
                        </p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> เปิดได้ใน Excel</li>
                            <li><i class="fas fa-check"></i> ขนาดไฟล์เล็ก</li>
                            <li><i class="fas fa-check"></i> รองรับภาษาไทย</li>
                            <li><i class="fas fa-check"></i> เหมาะสำหรับการวิเคราะห์ข้อมูล</li>
                        </ul>
                        <div class="text-center mt-4">
                            <button class="btn btn-export" onclick="exportData('csv')">
                                <i class="fas fa-download me-2"></i>ดาวน์โหลด CSV
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Excel Export -->
            <div class="col-lg-4 col-md-6">
                <div class="card export-card h-100">
                    <div class="export-preview preview-excel">
                        <i class="fas fa-file-excel preview-icon"></i>
                    </div>
                    <div class="card-body p-4">
                        <h5 class="export-title">Excel (.xlsx)</h5>
                        <p class="export-description">
                            ไฟล์ Excel พร้อมการจัดรูปแบบ เหมาะสำหรับการนำเสนอและการทำงานใน Microsoft Excel
                        </p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> จัดรูปแบบสวยงาม</li>
                            <li><i class="fas fa-check"></i> ปรับความกว้างคอลัมน์อัตโนมัติ</li>
                            <li><i class="fas fa-check"></i> หัวตารางมีสี</li>
                            <li><i class="fas fa-check"></i> เหมาะสำหรับการนำเสนอ</li>
                        </ul>
                        <div class="text-center mt-4">
                            <button class="btn btn-export" onclick="exportData('excel')">
                                <i class="fas fa-download me-2"></i>ดาวน์โหลด Excel
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- JSON Export -->
            <div class="col-lg-4 col-md-6">
                <div class="card export-card h-100">
                    <div class="export-preview preview-json">
                        <i class="fas fa-file-code preview-icon"></i>
                    </div>
                    <div class="card-body p-4">
                        <h5 class="export-title">JSON (JavaScript Object Notation)</h5>
                        <p class="export-description">
                            รูปแบบข้อมูลสำหรับการพัฒนาโปรแกรม API และการแลกเปลี่ยนข้อมูลระหว่างระบบ
                        </p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> รองรับการพัฒนา API</li>
                            <li><i class="fas fa-check"></i> มี Metadata ครบถ้วน</li>
                            <li><i class="fas fa-check"></i> รูปแบบมาตรฐาน</li>
                            <li><i class="fas fa-check"></i> เหมาะสำหรับนักพัฒนา</li>
                        </ul>
                        <div class="text-center mt-4">
                            <button class="btn btn-view me-2" onclick="viewData('json')">
                                <i class="fas fa-eye me-2"></i>ดูข้อมูล
                            </button>
                            <button class="btn btn-export" onclick="exportData('json')">
                                <i class="fas fa-download me-2"></i>ดาวน์โหลด
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="text-center mt-5">
            <a href="index.php" class="btn btn-outline-light btn-lg">
                <i class="fas fa-arrow-left me-2"></i>กลับไปหน้าหลัก
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // ข้อมูลพารามิเตอร์จาก PHP
        const filterParams = <?= json_encode($queryParams) ?>;
        const queryString = <?= json_encode($queryString) ?>;
        
        function exportData(format) {
            let exportUrl = '';
            switch(format) {
                case 'csv':
                    exportUrl = 'export_csv.php';
                    break;
                case 'excel':
                    exportUrl = 'export_excel_simple.php';
                    break;
                case 'json':
                    exportUrl = 'export_json.php';
                    break;
            }
            
            // เพิ่มพารามิเตอร์การกรองไปยัง URL
            if (queryString) {
                exportUrl += queryString;
            }
            
            // แสดงข้อความยืนยัน
            const filterCount = Object.keys(filterParams).length;
            let confirmMessage = 'ส่งออกข้อมูลเป็น ' + format.toUpperCase();
            
            if (filterCount > 0) {
                confirmMessage += '\nพร้อมเงื่อนไขการกรอง ' + filterCount + ' รายการ';
            } else {
                confirmMessage += '\nจะส่งออกข้อมูลทั้งหมดในระบบ';
            }
            
            if (confirm(confirmMessage + '\n\nต้องการดำเนินการต่อหรือไม่?')) {
                // สร้าง link สำหรับดาวน์โหลด
                const link = document.createElement('a');
                link.href = exportUrl;
                link.download = '';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }
        
        function viewData(format) {
            if (format === 'json') {
                let viewUrl = 'export_json.php';
                
                // เพิ่มพารามิเตอร์การกรองและ format=view
                let viewParams = {...filterParams, format: 'view'};
                const viewQuery = '?' + new URLSearchParams(viewParams).toString();
                viewUrl += viewQuery;
                
                // เปิดในหน้าต่างใหม่
                window.open(viewUrl, '_blank');
            }
        }
        
        // เพิ่มเอฟเฟกต์ hover สำหรับการ์ด
        document.querySelectorAll('.export-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
