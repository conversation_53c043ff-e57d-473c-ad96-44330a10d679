<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>ตรวจสอบโครงสร้างฐานข้อมูลสำหรับ Import Update</h2>";

try {
    // ตรวจสอบโครงสร้างตาราง assets
    echo "<h3>1. โครงสร้างตาราง assets:</h3>";
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
    echo "</tr>";
    
    $fieldNames = [];
    foreach ($columns as $column) {
        $fieldNames[] = $column['Field'];
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h4>ฟิลด์ทั้งหมดในตาราง assets:</h4>";
    echo "<p>" . implode(', ', $fieldNames) . "</p>";
    
    // ตรวจสอบโครงสร้างตาราง asset_logs
    echo "<h3>2. โครงสร้างตาราง asset_logs:</h3>";
    $stmt = $pdo->query("DESCRIBE asset_logs");
    $logColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
    echo "</tr>";
    
    $logFieldNames = [];
    foreach ($logColumns as $column) {
        $logFieldNames[] = $column['Field'];
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h4>ฟิลด์ทั้งหมดในตาราง asset_logs:</h4>";
    echo "<p>" . implode(', ', $logFieldNames) . "</p>";
    
    // ตรวจสอบข้อมูล Assets ที่มีอยู่
    echo "<h3>3. ข้อมูล Assets ที่มีอยู่ (5 รายการแรก):</h3>";
    $stmt = $pdo->query("SELECT id, asset_id, type, brand, model, status, created_date, updated_date FROM assets LIMIT 5");
    $assets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($assets)) {
        echo "<p style='color: red;'>ไม่มี Assets ในระบบ กรุณาเพิ่ม Asset ก่อนทดสอบ Import</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>Asset ID</th><th>Type</th><th>Brand</th><th>Model</th><th>Status</th><th>Created</th><th>Updated</th>";
        echo "</tr>";
        
        foreach ($assets as $asset) {
            echo "<tr>";
            echo "<td>" . $asset['id'] . "</td>";
            echo "<td>" . htmlspecialchars($asset['asset_id']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['type']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['brand']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['model']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['status']) . "</td>";
            echo "<td>" . $asset['created_date'] . "</td>";
            echo "<td>" . $asset['updated_date'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // สร้างไฟล์ CSV ตัวอย่างตามโครงสร้างที่ถูกต้อง
    echo "<h3>4. ไฟล์ CSV ตัวอย่างที่ถูกต้อง:</h3>";
    
    $csvHeaders = [
        'asset_id', 'type', 'brand', 'model', 'tag', 'department', 'status',
        'hostname', 'operating_system', 'serial_number', 'warranty_expire',
        'description', 'set_name'
    ];
    
    echo "<h4>Headers ที่ต้องใช้ในไฟล์ CSV:</h4>";
    echo "<p><code>" . implode(', ', $csvHeaders) . "</code></p>";
    
    if (!empty($assets)) {
        $sampleAsset = $assets[0];
        echo "<h4>ตัวอย่างไฟล์ CSV สำหรับ Asset ID: " . htmlspecialchars($sampleAsset['asset_id']) . "</h4>";
        
        $csvContent = implode(',', $csvHeaders) . "\n";
        $csvContent .= $sampleAsset['asset_id'] . ",";
        $csvContent .= $sampleAsset['type'] . ",";
        $csvContent .= ($sampleAsset['brand'] ?? '') . ",";
        $csvContent .= ($sampleAsset['model'] ?? '') . ",";
        $csvContent .= "Updated Tag,"; // ตัวอย่างการเปลี่ยนแปลง
        $csvContent .= "IT Department,"; // ตัวอย่างการเปลี่ยนแปลง
        $csvContent .= "ทดสอบ Import,"; // ตัวอย่างการเปลี่ยนแปลง
        $csvContent .= "PC-UPDATED,"; // ตัวอย่างการเปลี่ยนแปลง
        $csvContent .= "Windows 11,"; // ตัวอย่างการเปลี่ยนแปลง
        $csvContent .= "SN-UPDATED,"; // ตัวอย่างการเปลี่ยนแปลง
        $csvContent .= "2025-12-31,"; // ตัวอย่างการเปลี่ยนแปลง
        $csvContent .= "Updated via Import - " . date('Y-m-d H:i:s') . ","; // ตัวอย่างการเปลี่ยนแปลง
        $csvContent .= "Import Set"; // ตัวอย่างการเปลี่ยนแปลง
        
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars($csvContent);
        echo "</pre>";
        
        // สร้างไฟล์ CSV ตัวอย่าง
        if (isset($_GET['create_sample'])) {
            $filename = 'sample_import_' . date('Y-m-d_H-i-s') . '.csv';
            file_put_contents($filename, $csvContent);
            echo "<p style='color: green;'>✅ สร้างไฟล์ตัวอย่าง: <a href='{$filename}' download>{$filename}</a></p>";
        }
    }
    
    // ตรวจสอบฟังก์ชัน getCurrentUserFullName
    echo "<h3>5. ตรวจสอบฟังก์ชัน getCurrentUserFullName:</h3>";
    $currentUser = getCurrentUserFullName();
    echo "<p><strong>ผู้ใช้ปัจจุบัน:</strong> " . htmlspecialchars($currentUser ?? 'ไม่ระบุ') . "</p>";
    
    // ตรวจสอบ AssetManager
    echo "<h3>6. ตรวจสอบ AssetManager:</h3>";
    require_once 'classes/AssetManager.php';
    $assetManager = new AssetManager($pdo);
    echo "<p style='color: green;'>✅ AssetManager โหลดสำเร็จ</p>";
    
    // ทดสอบการอัปเดต Asset (ถ้ามี Asset)
    if (!empty($assets) && isset($_GET['test_update'])) {
        echo "<h3>7. ทดสอบการอัปเดต Asset:</h3>";
        
        $testAsset = $assets[0];
        $updateData = [
            'type' => $testAsset['type'],
            'brand' => $testAsset['brand'],
            'model' => $testAsset['model'],
            'tag' => 'TEST-IMPORT-' . date('His'),
            'department' => 'Test Department',
            'status' => 'ทดสอบ Import',
            'hostname' => 'TEST-PC',
            'operating_system' => 'Windows 11',
            'serial_number' => 'TEST-SN-' . date('His'),
            'asset_id' => $testAsset['asset_id'],
            'warranty_expire' => '2025-12-31',
            'description' => 'ทดสอบการอัปเดตผ่าน Import - ' . date('Y-m-d H:i:s'),
            'set_name' => 'Test Import Set',
            'updated_by' => $currentUser ?? 'Test System'
        ];
        
        echo "<h4>ข้อมูลที่จะอัปเดต:</h4>";
        echo "<pre>" . print_r($updateData, true) . "</pre>";
        
        $result = $assetManager->updateAsset($testAsset['id'], $updateData);
        
        if ($result) {
            echo "<p style='color: green;'>✅ ทดสอบการอัปเดต Asset สำเร็จ!</p>";
            
            // ดึงข้อมูลใหม่
            $stmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
            $stmt->execute([$testAsset['id']]);
            $updatedAsset = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "<h4>ข้อมูลหลังอัปเดต:</h4>";
            echo "<ul>";
            echo "<li><strong>Tag:</strong> " . htmlspecialchars($updatedAsset['tag']) . "</li>";
            echo "<li><strong>Department:</strong> " . htmlspecialchars($updatedAsset['department']) . "</li>";
            echo "<li><strong>Status:</strong> " . htmlspecialchars($updatedAsset['status']) . "</li>";
            echo "<li><strong>Description:</strong> " . htmlspecialchars($updatedAsset['description']) . "</li>";
            echo "<li><strong>Updated Date:</strong> " . $updatedAsset['updated_date'] . "</li>";
            echo "<li><strong>Updated By:</strong> " . htmlspecialchars($updatedAsset['updated_by']) . "</li>";
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ ทดสอบการอัปเดต Asset ล้มเหลว</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<hr>
<p>
    <a href="?create_sample=1" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        📄 สร้างไฟล์ CSV ตัวอย่าง
    </a>
    <a href="?test_update=1" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🧪 ทดสอบการอัปเดต
    </a>
    <a href="import_update_assets.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        📁 Import Update Assets
    </a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th { background: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
