<?php
// ทดสอบการทำงานของ mPDF
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🧪 ทดสอบ mPDF</h2>";

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
try {
    $host = 'localhost';
    $username = 'root';
    $password = 'Wxmujwsofu@1234';
    $database = 'asset_management';

    $conn = new mysqli($host, $username, $password, $database);
    $conn->set_charset("utf8mb4");

    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
    
} catch (Exception $e) {
    die("<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูล: " . $e->getMessage() . "</p>");
}

// ตรวจสอบ TCPDF
try {
    require_once 'vendor/autoload.php';
    echo "<p style='color: green;'>✅ โหลด autoload สำเร็จ</p>";

    // สร้าง TCPDF
    $pdf = new TCPDF('P', PDF_UNIT, 'A4', true, 'UTF-8', false);
    echo "<p style='color: green;'>✅ สร้าง TCPDF object สำเร็จ</p>";

    // ตั้งค่าเอกสาร
    $pdf->SetCreator('Test System');
    $pdf->SetTitle('ทดสอบ TCPDF');
    $pdf->SetMargins(15, 15, 15);
    $pdf->SetAutoPageBreak(TRUE, 15);

    // เพิ่มหน้า
    $pdf->AddPage();

    // ตั้งค่าฟอนต์
    $pdf->SetFont('helvetica', 'B', 16);
    $pdf->Cell(0, 15, 'ทดสอบ TCPDF', 0, 1, 'C');

    $pdf->SetFont('helvetica', '', 12);
    $pdf->Cell(0, 10, 'นี่คือการทดสอบการทำงานของ TCPDF', 0, 1, 'L');
    $pdf->Ln(5);

    // สร้างตาราง
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(30, 8, 'ลำดับ', 1, 0, 'C');
    $pdf->Cell(60, 8, 'ชื่อ', 1, 0, 'C');
    $pdf->Cell(80, 8, 'รายละเอียด', 1, 1, 'C');

    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(30, 8, '1', 1, 0, 'C');
    $pdf->Cell(60, 8, 'ทดสอบ', 1, 0, 'L');
    $pdf->Cell(80, 8, 'การทำงานของ TCPDF', 1, 1, 'L');

    echo "<p style='color: green;'>✅ สร้าง PDF สำเร็จ</p>";

    // ทดสอบ output
    $pdf->Output('test.pdf', 'I');

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ เกิดข้อผิดพลาดกับ TCPDF: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

$conn->close();
?>
