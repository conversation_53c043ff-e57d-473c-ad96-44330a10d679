"""
Edit Asset Dialog
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime
import logging

class EditAssetDialog:
    """Dialog for editing existing assets"""
    
    def __init__(self, parent, asset_model, asset_id):
        self.parent = parent
        self.asset_model = asset_model
        self.asset_id = asset_id
        self.result = False
        
        # Load asset data
        self.asset_data = self.asset_model.get_asset_by_id(asset_id)
        if not self.asset_data:
            messagebox.showerror("Error", "Asset not found!")
            return
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"✏️ Edit Asset - ID: {asset_id}")
        self.dialog.geometry("800x700")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        # Create widgets
        self.create_widgets()
        
        # Load asset data into fields
        self.load_asset_data()
        
        # Focus on first field
        self.type_combo.focus()
        
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (800 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (700 // 2)
        self.dialog.geometry(f"800x700+{x}+{y}")
    
    def create_widgets(self):
        """Create and arrange dialog widgets"""
        # Main frame
        main_frame = ttk_bs.Frame(self.dialog, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # Title
        title_label = ttk_bs.Label(
            main_frame,
            text=f"✏️ Edit Asset - ID: {self.asset_id}",
            font=("Arial", 18, "bold"),
            bootstyle="warning"
        )
        title_label.pack(pady=(0, 20))
        
        # Create notebook for organized input
        notebook = ttk_bs.Notebook(main_frame)
        notebook.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        # Basic Information Tab
        basic_frame = ttk_bs.Frame(notebook, padding=20)
        notebook.add(basic_frame, text="📋 Basic Information")
        
        # Asset Type (Required)
        ttk_bs.Label(basic_frame, text="Type *", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=W, pady=5)
        self.type_combo = ttk_bs.Combobox(
            basic_frame,
            values=[
                "Desktop", "Laptop", "Monitor", "All-in-one", "Multifunction Laser Printer",
                "Barcode Printer", "Barcode Scanner", "Tablet", "UPS", "Queue",
                "IP Phone", "Teleconference", "Switch", "Access Point", "Peripheral"
            ],
            width=30
        )
        self.type_combo.grid(row=0, column=1, sticky=W+E, padx=(10, 0), pady=5)
        
        # Brand
        ttk_bs.Label(basic_frame, text="Brand", font=("Arial", 10)).grid(row=1, column=0, sticky=W, pady=5)
        self.brand_combo = ttk_bs.Combobox(
            basic_frame,
            values=[
                "Dell", "Lenovo", "Microsoft", "Apple", "Zebra", "HP", 
                "Philips", "Acer", "LG", "Cisco", "Other"
            ],
            width=30
        )
        self.brand_combo.grid(row=1, column=1, sticky=W+E, padx=(10, 0), pady=5)
        
        # Model
        ttk_bs.Label(basic_frame, text="Model", font=("Arial", 10)).grid(row=2, column=0, sticky=W, pady=5)
        self.model_entry = ttk_bs.Entry(basic_frame, width=30)
        self.model_entry.grid(row=2, column=1, sticky=W+E, padx=(10, 0), pady=5)
        
        # Tag
        ttk_bs.Label(basic_frame, text="Tag", font=("Arial", 10)).grid(row=3, column=0, sticky=W, pady=5)
        self.tag_entry = ttk_bs.Entry(basic_frame, width=30)
        self.tag_entry.grid(row=3, column=1, sticky=W+E, padx=(10, 0), pady=5)
        
        # Department
        ttk_bs.Label(basic_frame, text="Department", font=("Arial", 10)).grid(row=4, column=0, sticky=W, pady=5)
        self.department_entry = ttk_bs.Entry(basic_frame, width=30)
        self.department_entry.grid(row=4, column=1, sticky=W+E, padx=(10, 0), pady=5)
        
        # Status
        ttk_bs.Label(basic_frame, text="Status", font=("Arial", 10)).grid(row=5, column=0, sticky=W, pady=5)
        self.status_combo = ttk_bs.Combobox(
            basic_frame,
            values=["ใช้งาน", "ชำรุด", "สำรอง"],
            width=30
        )
        self.status_combo.grid(row=5, column=1, sticky=W+E, padx=(10, 0), pady=5)
        
        # Configure grid weights
        basic_frame.columnconfigure(1, weight=1)
        
        # Technical Information Tab
        tech_frame = ttk_bs.Frame(notebook, padding=20)
        notebook.add(tech_frame, text="💻 Technical Info")
        
        # Hostname
        ttk_bs.Label(tech_frame, text="Hostname", font=("Arial", 10)).grid(row=0, column=0, sticky=W, pady=5)
        self.hostname_entry = ttk_bs.Entry(tech_frame, width=30)
        self.hostname_entry.grid(row=0, column=1, sticky=W+E, padx=(10, 0), pady=5)
        
        # Operating System
        ttk_bs.Label(tech_frame, text="Operating System", font=("Arial", 10)).grid(row=1, column=0, sticky=W, pady=5)
        self.os_combo = ttk_bs.Combobox(
            tech_frame,
            values=["Windows 7", "Windows 10", "Windows 11", "MacOS", "Linux", "Other"],
            width=30
        )
        self.os_combo.grid(row=1, column=1, sticky=W+E, padx=(10, 0), pady=5)
        
        # Serial Number
        ttk_bs.Label(tech_frame, text="Serial Number", font=("Arial", 10)).grid(row=2, column=0, sticky=W, pady=5)
        self.serial_entry = ttk_bs.Entry(tech_frame, width=30)
        self.serial_entry.grid(row=2, column=1, sticky=W+E, padx=(10, 0), pady=5)
        
        # Asset ID
        ttk_bs.Label(tech_frame, text="Asset ID", font=("Arial", 10)).grid(row=3, column=0, sticky=W, pady=5)
        self.asset_id_entry = ttk_bs.Entry(tech_frame, width=30)
        self.asset_id_entry.grid(row=3, column=1, sticky=W+E, padx=(10, 0), pady=5)
        
        # Warranty Expire
        ttk_bs.Label(tech_frame, text="Warranty Expire", font=("Arial", 10)).grid(row=4, column=0, sticky=W, pady=5)
        self.warranty_entry = ttk_bs.DateEntry(tech_frame, width=28)
        self.warranty_entry.grid(row=4, column=1, sticky=W+E, padx=(10, 0), pady=5)
        
        # Set Name
        ttk_bs.Label(tech_frame, text="Set Name", font=("Arial", 10)).grid(row=5, column=0, sticky=W, pady=5)
        self.set_entry = ttk_bs.Entry(tech_frame, width=30)
        self.set_entry.grid(row=5, column=1, sticky=W+E, padx=(10, 0), pady=5)
        
        # Configure grid weights
        tech_frame.columnconfigure(1, weight=1)
        
        # Additional Information Tab
        additional_frame = ttk_bs.Frame(notebook, padding=20)
        notebook.add(additional_frame, text="📝 Additional Info")
        
        # Description
        ttk_bs.Label(additional_frame, text="Description", font=("Arial", 10)).pack(anchor=W, pady=(0, 5))
        self.description_text = tk.Text(additional_frame, height=10, width=60, wrap=tk.WORD)
        self.description_text.pack(fill=BOTH, expand=True, pady=(0, 10))
        
        # Scrollbar for description
        desc_scrollbar = ttk_bs.Scrollbar(additional_frame, orient=VERTICAL, command=self.description_text.yview)
        self.description_text.configure(yscrollcommand=desc_scrollbar.set)
        
        # Asset Info Tab (Read-only information)
        info_frame = ttk_bs.Frame(notebook, padding=20)
        notebook.add(info_frame, text="ℹ️ Asset Info")
        
        # Created info
        ttk_bs.Label(info_frame, text="Created Date:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=W, pady=5)
        created_date = self.asset_data.get('created_date', 'N/A')
        if isinstance(created_date, datetime):
            created_date = created_date.strftime('%Y-%m-%d %H:%M:%S')
        ttk_bs.Label(info_frame, text=str(created_date)).grid(row=0, column=1, sticky=W, padx=(10, 0), pady=5)
        
        ttk_bs.Label(info_frame, text="Created By:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky=W, pady=5)
        ttk_bs.Label(info_frame, text=self.asset_data.get('created_by', 'N/A')).grid(row=1, column=1, sticky=W, padx=(10, 0), pady=5)
        
        # Updated info
        ttk_bs.Label(info_frame, text="Last Updated:", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=W, pady=5)
        updated_date = self.asset_data.get('updated_date', 'N/A')
        if isinstance(updated_date, datetime):
            updated_date = updated_date.strftime('%Y-%m-%d %H:%M:%S')
        ttk_bs.Label(info_frame, text=str(updated_date)).grid(row=2, column=1, sticky=W, padx=(10, 0), pady=5)
        
        ttk_bs.Label(info_frame, text="Updated By:", font=("Arial", 10, "bold")).grid(row=3, column=0, sticky=W, pady=5)
        ttk_bs.Label(info_frame, text=self.asset_data.get('updated_by', 'N/A')).grid(row=3, column=1, sticky=W, padx=(10, 0), pady=5)
        
        # Button frame
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X, pady=(10, 0))
        
        # Buttons
        update_btn = ttk_bs.Button(
            button_frame,
            text="💾 Update Asset",
            command=self.update_asset,
            bootstyle="warning",
            width=15
        )
        update_btn.pack(side=RIGHT, padx=(10, 0))
        
        cancel_btn = ttk_bs.Button(
            button_frame,
            text="❌ Cancel",
            command=self.cancel,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=RIGHT)
        
        # Required field note
        note_label = ttk_bs.Label(
            button_frame,
            text="* Required fields",
            font=("Arial", 9, "italic"),
            bootstyle="danger"
        )
        note_label.pack(side=LEFT)
    
    def load_asset_data(self):
        """Load existing asset data into form fields"""
        # Basic Information
        self.type_combo.set(self.asset_data.get('type', ''))
        self.brand_combo.set(self.asset_data.get('brand', ''))
        self.model_entry.insert(0, self.asset_data.get('model', ''))
        self.tag_entry.insert(0, self.asset_data.get('tag', ''))
        self.department_entry.insert(0, self.asset_data.get('department', ''))
        self.status_combo.set(self.asset_data.get('status', 'ใช้งาน'))
        
        # Technical Information
        self.hostname_entry.insert(0, self.asset_data.get('hostname', ''))
        self.os_combo.set(self.asset_data.get('operating_system', ''))
        self.serial_entry.insert(0, self.asset_data.get('serial_number', ''))
        self.asset_id_entry.insert(0, self.asset_data.get('asset_id', ''))
        
        # Warranty date
        warranty_date = self.asset_data.get('warranty_expire')
        if warranty_date:
            if isinstance(warranty_date, str):
                self.warranty_entry.entry.insert(0, warranty_date)
            else:
                self.warranty_entry.entry.insert(0, warranty_date.strftime('%Y-%m-%d'))
        
        self.set_entry.insert(0, self.asset_data.get('set_name', ''))
        
        # Description
        description = self.asset_data.get('description', '')
        if description:
            self.description_text.insert(1.0, description)
    
    def update_asset(self):
        """Update the asset"""
        # Validate required fields
        if not self.type_combo.get().strip():
            messagebox.showerror("Validation Error", "Type is required!")
            self.type_combo.focus()
            return
        
        # Prepare asset data
        asset_data = {
            'type': self.type_combo.get().strip(),
            'brand': self.brand_combo.get().strip(),
            'model': self.model_entry.get().strip(),
            'tag': self.tag_entry.get().strip(),
            'department': self.department_entry.get().strip(),
            'status': self.status_combo.get().strip(),
            'hostname': self.hostname_entry.get().strip(),
            'operating_system': self.os_combo.get().strip(),
            'serial_number': self.serial_entry.get().strip(),
            'asset_id': self.asset_id_entry.get().strip(),
            'warranty_expire': self.warranty_entry.entry.get() if self.warranty_entry.entry.get() else None,
            'description': self.description_text.get(1.0, tk.END).strip(),
            'set_name': self.set_entry.get().strip(),
            'updated_by': 'Python GUI User'
        }
        
        try:
            success, message = self.asset_model.update_asset(self.asset_id, asset_data)
            if success:
                messagebox.showinfo("Success", message)
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("Error", message)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update asset:\n{e}")
            logging.error(f"Failed to update asset: {e}")
    
    def cancel(self):
        """Cancel and close dialog"""
        self.result = False
        self.dialog.destroy()
