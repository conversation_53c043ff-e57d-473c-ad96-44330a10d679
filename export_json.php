<?php
// Export ข้อมูลเป็น JSON
error_reporting(E_ALL);
ini_set('display_errors', 1);

// เชื่อมต่อฐานข้อมูล
$host = 'localhost';
$username = 'root';
$password = 'Wxmujwsofu@1234';
$database = 'asset_management';

$conn = new mysqli($host, $username, $password, $database);
$conn->set_charset("utf8mb4");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// รับพารามิเตอร์
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';
$filter_serial = $_GET['filter_serial'] ?? '';
$format = $_GET['format'] ?? 'download'; // download หรือ view

// สร้าง SQL query
$sql = "SELECT * FROM assets WHERE 1=1";
$params = [];
$conditions = [];

// เพิ่มเงื่อนไขการกรอง
if (!empty($search)) {
    $conditions[] = "(type LIKE ? OR brand LIKE ? OR model LIKE ? OR tag LIKE ? OR hostname LIKE ? OR department LIKE ? OR asset_id LIKE ? OR serial_number LIKE ?)";
    $searchTerm = '%' . $search . '%';
    for ($i = 0; $i < 8; $i++) {
        $params[] = $searchTerm;
    }
}

if (!empty($filter_type)) {
    $conditions[] = "type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_brand)) {
    $conditions[] = "brand = ?";
    $params[] = $filter_brand;
}

if (!empty($filter_department)) {
    $conditions[] = "department = ?";
    $params[] = $filter_department;
}

if (!empty($filter_status)) {
    $conditions[] = "status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_os)) {
    $conditions[] = "operating_system = ?";
    $params[] = $filter_os;
}

if (!empty($filter_serial)) {
    $conditions[] = "serial_number LIKE ?";
    $params[] = '%' . $filter_serial . '%';
}

// รวมเงื่อนไข
if (!empty($conditions)) {
    $sql .= " AND " . implode(" AND ", $conditions);
}

$sql .= " ORDER BY created_date DESC";

// เตรียม prepared statement
$stmt = $conn->prepare($sql);

// Bind parameters ถ้ามี
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();

// รวบรวมข้อมูล (ตรงกับโครงสร้างฐานข้อมูล)
$assets = [];
while ($row = $result->fetch_assoc()) {
    $assets[] = [
        'id' => (int)($row['id'] ?? 0),
        'asset_id' => $row['asset_id'] ?? '',
        'type' => $row['type'] ?? '',
        'brand' => $row['brand'] ?? '',
        'model' => $row['model'] ?? '',
        'tag' => $row['tag'] ?? '',
        'department' => $row['department'] ?? '',
        'status' => $row['status'] ?? '',
        'hostname' => $row['hostname'] ?? '',
        'ip_address' => $row['ip_address'] ?? '',
        'operating_system' => $row['operating_system'] ?? '',
        'serial_number' => $row['serial_number'] ?? '',
        'warranty_expire' => $row['warranty_expire'] ?? null,
        'description' => $row['description'] ?? '',
        'set_name' => $row['set_name'] ?? '',
        'created_date' => $row['created_date'] ?? null,
        'created_by' => $row['created_by'] ?? '',
        'updated_date' => $row['updated_date'] ?? null,
        'updated_by' => $row['updated_by'] ?? ''
    ];
}

// สร้างข้อมูล metadata
$metadata = [
    'export_date' => date('Y-m-d H:i:s'),
    'total_records' => count($assets),
    'filters_applied' => []
];

// เพิ่มข้อมูลการกรอง
if (!empty($search)) $metadata['filters_applied']['search'] = $search;
if (!empty($filter_type)) $metadata['filters_applied']['type'] = $filter_type;
if (!empty($filter_brand)) $metadata['filters_applied']['brand'] = $filter_brand;
if (!empty($filter_department)) $metadata['filters_applied']['department'] = $filter_department;
if (!empty($filter_status)) $metadata['filters_applied']['status'] = $filter_status;
if (!empty($filter_os)) $metadata['filters_applied']['operating_system'] = $filter_os;
if (!empty($filter_serial)) $metadata['filters_applied']['serial_number'] = $filter_serial;

// สร้างข้อมูล JSON
$jsonData = [
    'metadata' => $metadata,
    'data' => $assets
];

// สร้างชื่อไฟล์
$filename = "asset_data_" . date('Y-m-d_H-i-s');
if (!empty($filter_type)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_type);
if (!empty($filter_department)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_department);

if ($format === 'view') {
    // แสดงผลใน browser
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
} else {
    // ดาวน์โหลดไฟล์
    header('Content-Type: application/json; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.json"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: 0');
    
    echo json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}

// ปิดการเชื่อมต่อฐานข้อมูล
$stmt->close();
$conn->close();
?>
