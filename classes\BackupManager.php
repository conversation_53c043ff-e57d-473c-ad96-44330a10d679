<?php
/**
 * BackupManager Class
 * จัดการระบบ Auto Backup SQL Database
 */

// Include config file if not already included
if (!defined('DB_HOST')) {
    require_once __DIR__ . '/../includes/config.php';
}

require_once __DIR__ . '/TelegramNotifier.php';

class BackupManager {
    private $pdo;
    private $config;
    private $telegramNotifier;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->telegramNotifier = new TelegramNotifier();
        $this->loadConfig();
    }
    
    /**
     * โหลดการตั้งค่า Backup
     */
    private function loadConfig() {
        $configFile = 'config/backup_config.json';
        
        if (file_exists($configFile)) {
            $this->config = json_decode(file_get_contents($configFile), true);
        } else {
            // ค่าเริ่มต้น
            $this->config = [
                'enabled' => true,
                'backup_path' => 'backups/',
                'max_backups' => 30,
                'email_enabled' => false,
                'email_recipients' => [],
                'smtp_host' => '',
                'smtp_port' => 587,
                'smtp_username' => '',
                'smtp_password' => '',
                'smtp_secure' => 'tls'
            ];
            $this->saveConfig();
        }
    }
    
    /**
     * บันทึกการตั้งค่า
     */
    public function saveConfig() {
        $configFile = 'config/backup_config.json';
        $configDir = dirname($configFile);
        
        if (!is_dir($configDir)) {
            mkdir($configDir, 0755, true);
        }
        
        file_put_contents($configFile, json_encode($this->config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
    
    /**
     * อัปเดตการตั้งค่า
     */
    public function updateConfig($newConfig) {
        $this->config = array_merge($this->config, $newConfig);
        $this->saveConfig();
    }
    
    /**
     * ดึงการตั้งค่า
     */
    public function getConfig() {
        return $this->config;
    }
    
    /**
     * สร้าง Backup อัตโนมัติ
     */
    public function createAutoBackup($action = 'AUTO', $description = '') {
        if (!$this->config['enabled']) {
            return false;
        }
        
        try {
            $backupFile = $this->generateBackup($action, $description);
            
            if ($backupFile) {
                // ลบ backup เก่าที่เกินจำนวนที่กำหนด
                $this->cleanOldBackups();

                // ส่ง email แจ้งเตือน
                if ($this->config['email_enabled']) {
                    $this->sendBackupNotification($backupFile, $action, $description);
                }

                // ส่งแจ้งเตือน Telegram (ไม่ให้ error หยุดการทำงาน)
                try {
                    $backupPath = rtrim($this->config['backup_path'], '/\\');
                    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                        $backupPath .= '\\';
                    } else {
                        $backupPath .= '/';
                    }
                    $filePath = $backupPath . $backupFile;
                    $fileSize = file_exists($filePath) ? filesize($filePath) : 0;

                    $this->telegramNotifier->notifyBackupSuccess($backupFile, $action, $description, $fileSize);
                } catch (Exception $e) {
                    error_log("Telegram Backup Notification Error: " . $e->getMessage());
                }

                return $backupFile;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Backup Error: " . $e->getMessage());

            // ส่งแจ้งเตือน Telegram เมื่อ backup ล้มเหลว
            try {
                $this->telegramNotifier->notifyBackupError($action, $e->getMessage());
            } catch (Exception $telegramError) {
                error_log("Telegram Backup Error Notification Error: " . $telegramError->getMessage());
            }

            return false;
        }
    }
    
    /**
     * สร้างไฟล์ Backup
     */
    private function generateBackup($action = 'MANUAL', $description = '') {
        // สร้างโฟลเดอร์ backup ถ้าไม่มี
        $backupPath = $this->config['backup_path'];

        // ทำความสะอาด path และเพิ่ม directory separator
        $backupPath = rtrim($backupPath, '/\\');
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            $backupPath .= '\\';
        } else {
            $backupPath .= '/';
        }

        if (!is_dir($backupPath)) {
            if (!mkdir($backupPath, 0755, true)) {
                throw new Exception("ไม่สามารถสร้างโฟลเดอร์ backup ได้: {$backupPath}");
            }
        }

        // ตรวจสอบสิทธิ์การเขียน
        if (!is_writable($backupPath)) {
            throw new Exception("ไม่สามารถเขียนในโฟลเดอร์ backup ได้: {$backupPath}");
        }

        // สร้างชื่อไฟล์
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "asset_backup_{$timestamp}_{$action}.sql";
        $filepath = $backupPath . $filename;
        
        // ดึงข้อมูลการเชื่อมต่อฐานข้อมูล
        $dbConfig = $this->getDatabaseConfig();
        
        // สร้าง SQL dump
        $sql = $this->createSQLDump($action, $description);
        
        // บันทึกไฟล์
        if (file_put_contents($filepath, $sql)) {
            // บันทึก log การ backup
            $this->logBackup($filename, $action, $description, filesize($filepath));
            return $filename;
        }
        
        return false;
    }
    
    /**
     * สร้าง SQL Dump
     */
    private function createSQLDump($action, $description) {
        $sql = "-- Asset Management System Database Backup\n";
        $sql .= "-- Created: " . date('Y-m-d H:i:s') . "\n";
        $sql .= "-- Action: {$action}\n";
        $sql .= "-- Description: {$description}\n";
        $sql .= "-- Generated by Auto Backup System\n\n";
        
        $sql .= "SET FOREIGN_KEY_CHECKS=0;\n";
        $sql .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
        $sql .= "SET AUTOCOMMIT = 0;\n";
        $sql .= "START TRANSACTION;\n\n";
        
        // ดึงรายชื่อตาราง
        $tables = $this->getTables();
        
        foreach ($tables as $table) {
            $sql .= $this->dumpTable($table);
        }
        
        $sql .= "\nSET FOREIGN_KEY_CHECKS=1;\n";
        $sql .= "COMMIT;\n";
        
        return $sql;
    }
    
    /**
     * ดึงรายชื่อตาราง
     */
    private function getTables() {
        $stmt = $this->pdo->query("SHOW TABLES");
        $tables = [];
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $tables[] = $row[0];
        }
        return $tables;
    }
    
    /**
     * Dump ข้อมูลตาราง
     */
    private function dumpTable($tableName) {
        $sql = "-- Table: {$tableName}\n";
        
        // สร้างโครงสร้างตาราง
        $stmt = $this->pdo->query("SHOW CREATE TABLE `{$tableName}`");
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
        $sql .= $row['Create Table'] . ";\n\n";
        
        // ดึงข้อมูลในตาราง
        $stmt = $this->pdo->query("SELECT * FROM `{$tableName}`");
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            $columns = array_keys($rows[0]);
            $sql .= "INSERT INTO `{$tableName}` (`" . implode('`, `', $columns) . "`) VALUES\n";
            
            $values = [];
            foreach ($rows as $row) {
                $rowValues = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $rowValues[] = 'NULL';
                    } else {
                        $rowValues[] = "'" . addslashes($value) . "'";
                    }
                }
                $values[] = "(" . implode(', ', $rowValues) . ")";
            }
            
            $sql .= implode(",\n", $values) . ";\n\n";
        }
        
        return $sql;
    }
    
    /**
     * ดึงการตั้งค่าฐานข้อมูล
     */
    private function getDatabaseConfig() {
        // ดึงจาก config/database.php หรือ environment variables
        return [
            'host' => DB_HOST ?? 'localhost',
            'dbname' => DB_NAME ?? 'asset_management',
            'username' => DB_USER ?? 'root',
            'password' => DB_PASS ?? ''
        ];
    }
    
    /**
     * ลบ backup เก่า
     */
    private function cleanOldBackups() {
        $backupPath = $this->config['backup_path'];
        $maxBackups = $this->config['max_backups'];

        // ทำความสะอาด path และเพิ่ม directory separator
        $backupPath = rtrim($backupPath, '/\\');
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            $backupPath .= '\\';
        } else {
            $backupPath .= '/';
        }

        $files = glob($backupPath . 'asset_backup_*.sql');
        
        if (count($files) > $maxBackups) {
            // เรียงตามวันที่
            usort($files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // ลบไฟล์เก่า
            $filesToDelete = array_slice($files, 0, count($files) - $maxBackups);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }
    
    /**
     * บันทึก log การ backup
     */
    private function logBackup($filename, $action, $description, $filesize) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO backup_logs (filename, action_type, description, file_size, created_date, created_by) 
                VALUES (?, ?, ?, ?, NOW(), ?)
            ");
            
            $stmt->execute([
                $filename,
                $action,
                $description,
                $filesize,
                getCurrentUserFullName() ?? 'System'
            ]);
            
        } catch (Exception $e) {
            error_log("Backup Log Error: " . $e->getMessage());
        }
    }
    
    /**
     * ส่ง email แจ้งเตือน
     */
    private function sendBackupNotification($filename, $action, $description) {
        if (empty($this->config['email_recipients'])) {
            return false;
        }
        
        try {
            require_once 'includes/PHPMailer.php';
            
            $mail = new PHPMailer(true);
            
            // การตั้งค่า SMTP
            $mail->isSMTP();
            $mail->Host = $this->config['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $this->config['smtp_username'];
            $mail->Password = $this->config['smtp_password'];
            $mail->SMTPSecure = $this->config['smtp_secure'];
            $mail->Port = $this->config['smtp_port'];
            $mail->CharSet = 'UTF-8';
            
            // ผู้ส่งและผู้รับ
            $mail->setFrom($this->config['smtp_username'], 'Asset Management System');
            
            foreach ($this->config['email_recipients'] as $email) {
                $mail->addAddress($email);
            }
            
            // เนื้อหา email
            $mail->isHTML(true);
            $mail->Subject = 'Database Backup Notification - Asset Management System';
            
            $body = $this->generateEmailBody($filename, $action, $description);
            $mail->Body = $body;
            
            $mail->send();
            return true;
            
        } catch (Exception $e) {
            error_log("Email Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * สร้างเนื้อหา email
     */
    private function generateEmailBody($filename, $action, $description) {
        // ทำความสะอาด path และเพิ่ม directory separator
        $backupPath = rtrim($this->config['backup_path'], '/\\');
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            $backupPath .= '\\';
        } else {
            $backupPath .= '/';
        }

        $fullPath = realpath($backupPath . $filename);
        $filesize = filesize($fullPath);
        $filesizeFormatted = $this->formatFileSize($filesize);
        
        return "
        <html>
        <body style='font-family: Arial, sans-serif;'>
            <h2 style='color: #2c3e50;'>Database Backup Notification</h2>
            <p>ระบบได้ทำการสำรองข้อมูลฐานข้อมูลเรียบร้อยแล้ว</p>
            
            <table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>
                <tr>
                    <td style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'><strong>ไฟล์ Backup:</strong></td>
                    <td style='border: 1px solid #ddd; padding: 8px;'>{$filename}</td>
                </tr>
                <tr>
                    <td style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'><strong>การดำเนินการ:</strong></td>
                    <td style='border: 1px solid #ddd; padding: 8px;'>{$action}</td>
                </tr>
                <tr>
                    <td style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'><strong>รายละเอียด:</strong></td>
                    <td style='border: 1px solid #ddd; padding: 8px;'>{$description}</td>
                </tr>
                <tr>
                    <td style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'><strong>ขนาดไฟล์:</strong></td>
                    <td style='border: 1px solid #ddd; padding: 8px;'>{$filesizeFormatted}</td>
                </tr>
                <tr>
                    <td style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'><strong>วันที่สร้าง:</strong></td>
                    <td style='border: 1px solid #ddd; padding: 8px;'>" . date('Y-m-d H:i:s') . "</td>
                </tr>
            </table>
            
            <p style='color: #7f8c8d; font-size: 12px;'>
                Email นี้ถูกส่งโดยระบบ Asset Management System อัตโนมัติ<br>
                กรุณาอย่าตอบกลับ email นี้
            </p>
        </body>
        </html>
        ";
    }
    
    /**
     * แปลงขนาดไฟล์
     */
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * ดึงรายการ backup logs
     */
    public function getBackupLogs($limit = 50) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM backup_logs 
                ORDER BY created_date DESC 
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * ลบไฟล์ backup
     */
    public function deleteBackup($filename) {
        // ทำความสะอาด path และเพิ่ม directory separator
        $backupPath = rtrim($this->config['backup_path'], '/\\');
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            $backupPath .= '\\';
        } else {
            $backupPath .= '/';
        }

        $filepath = $backupPath . $filename;

        if (file_exists($filepath)) {
            return unlink($filepath);
        }

        return false;
    }
}
?>
