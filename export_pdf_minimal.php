<?php
// รายงาน PDF แบบ Minimal Design
error_reporting(E_ALL);
ini_set('display_errors', 1);

// เชื่อมต่อฐานข้อมูล
$host = 'localhost';
$username = 'root';
$password = 'Wxmujwsofu@1234';
$database = 'asset_management';

$conn = new mysqli($host, $username, $password, $database);
$conn->set_charset("utf8mb4");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

require_once 'vendor/autoload.php';

// ฟังก์ชันสำหรับตั้งค่าฟอนต์ไทย
function setupThaiFont() {
    $fontPath = __DIR__ . "/fonts/THSarabunNew.ttf";
    
    if (file_exists($fontPath)) {
        try {
            $fontName = TCPDF_FONTS::addTTFfont($fontPath, "TrueTypeUnicode", "", 96);
            return $fontName ? $fontName : "helvetica";
        } catch (Exception $e) {
            return "helvetica";
        }
    }
    
    return "helvetica";
}

// รับพารามิเตอร์
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';
$filter_serial = $_GET['filter_serial'] ?? '';
$isDownload = isset($_GET['download']) && $_GET['download'] === 'true';

// สร้าง TCPDF object
$pdf = new TCPDF('L', PDF_UNIT, 'A4', true, 'UTF-8', false);

// ตั้งค่าข้อมูลเอกสาร
$pdf->SetCreator('Asset Management');
$pdf->SetAuthor('Asset Management');
$pdf->SetTitle('Asset Report');
$pdf->SetSubject('Asset Report');

// ตั้งค่าหน้า - ขอบน้อยลง
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
$pdf->SetMargins(15, 20, 15);
$pdf->SetHeaderMargin(5);
$pdf->SetFooterMargin(10);
$pdf->SetAutoPageBreak(TRUE, 15);

// ตั้งค่าฟอนต์ไทย
$thaiFont = setupThaiFont();
$pdf->SetFont($thaiFont, '', 10);

// เพิ่มหน้าแรก
$pdf->AddPage();

// สร้าง SQL query
$sql = "SELECT * FROM assets WHERE 1=1";
$params = [];
$conditions = [];

// เพิ่มเงื่อนไขการกรอง
if (!empty($search)) {
    $conditions[] = "(type LIKE ? OR brand LIKE ? OR model LIKE ? OR tag LIKE ? OR hostname LIKE ? OR department LIKE ? OR asset_id LIKE ? OR serial_number LIKE ?)";
    $searchTerm = '%' . $search . '%';
    for ($i = 0; $i < 8; $i++) {
        $params[] = $searchTerm;
    }
}

if (!empty($filter_type)) {
    $conditions[] = "type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_brand)) {
    $conditions[] = "brand = ?";
    $params[] = $filter_brand;
}

if (!empty($filter_department)) {
    $conditions[] = "department = ?";
    $params[] = $filter_department;
}

if (!empty($filter_status)) {
    $conditions[] = "status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_os)) {
    $conditions[] = "operating_system = ?";
    $params[] = $filter_os;
}

if (!empty($filter_serial)) {
    $conditions[] = "serial_number LIKE ?";
    $params[] = '%' . $filter_serial . '%';
}

// รวมเงื่อนไข
if (!empty($conditions)) {
    $sql .= " AND " . implode(" AND ", $conditions);
}

$sql .= " ORDER BY created_date DESC";

// สร้างชื่อรายงาน
$title = "รายงานทรัพย์สิน";
$filterInfo = [];
if (!empty($search)) $filterInfo[] = "ค้นหา: $search";
if (!empty($filter_type)) $filterInfo[] = "ประเภท: $filter_type";
if (!empty($filter_brand)) $filterInfo[] = "ยี่ห้อ: $filter_brand";
if (!empty($filter_department)) $filterInfo[] = "แผนก: $filter_department";
if (!empty($filter_status)) $filterInfo[] = "สถานะ: $filter_status";

// วันที่รายงาน
$reportDate = date('d/m/Y');

// === MINIMAL HEADER ===
$pdf->SetFont($thaiFont, 'B', 24);
$pdf->SetTextColor(0, 0, 0); // สีดำ
$pdf->Cell(0, 15, $title, 0, 1, 'L');

$pdf->SetFont($thaiFont, '', 10);
$pdf->SetTextColor(100, 100, 100); // สีเทา
$pdf->Cell(0, 5, $reportDate, 0, 1, 'L');

if (!empty($filterInfo)) {
    $pdf->Cell(0, 5, implode(" | ", $filterInfo), 0, 1, 'L');
}

$pdf->Ln(8);

// === MINIMAL TABLE ===
$pdf->SetDrawColor(200, 200, 200); // เส้นสีเทาอ่อน
$pdf->SetTextColor(0, 0, 0);

// กำหนดความกว้างของคอลัมน์
$colWidths = [12, 32, 28, 28, 28, 32, 32, 22, 25];

// หัวตาราง - ไม่มีพื้นหลัง
$headers = ['#', 'แผนก', 'ประเภท', 'รหัส', 'แท็ก', 'ยี่ห้อ/รุ่น', 'S/N', 'สถานะ', 'วันที่'];

$pdf->SetFont($thaiFont, 'B', 9);
foreach ($headers as $i => $header) {
    $pdf->Cell($colWidths[$i], 7, $header, 'B', 0, 'L'); // เส้นล่างเท่านั้น
}
$pdf->Ln();

// === TABLE DATA ===
$pdf->SetFont($thaiFont, '', 8);

// เตรียม prepared statement
$stmt = $conn->prepare($sql);

// Bind parameters ถ้ามี
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();

$sequenceNumber = 1;
$rowsPerPage = 0;
$maxRowsPerPage = 32;

while ($row = $result->fetch_assoc()) {
    // ตรวจสอบว่าต้องขึ้นหน้าใหม่หรือไม่
    if ($rowsPerPage >= $maxRowsPerPage || $pdf->GetY() > 185) {
        $pdf->AddPage();
        
        // หัวตารางใหม่
        $pdf->SetFont($thaiFont, 'B', 9);
        foreach ($headers as $i => $header) {
            $pdf->Cell($colWidths[$i], 7, $header, 'B', 0, 'L');
        }
        $pdf->Ln();
        
        $pdf->SetFont($thaiFont, '', 8);
        $rowsPerPage = 0;
    }

    // ข้อมูลแถว - ไม่มีเส้นขอบ
    $brandModel = trim(($row['brand'] ?? '') . ' ' . ($row['model'] ?? ''));
    $createdDate = !empty($row['created_date']) ? date('d/m/y', strtotime($row['created_date'])) : '';
    
    $rowData = [
        $sequenceNumber,
        substr($row['department'] ?? '', 0, 18),
        substr($row['type'] ?? '', 0, 15),
        substr($row['asset_id'] ?? '', 0, 15),
        substr($row['tag'] ?? '', 0, 15),
        substr($brandModel, 0, 20),
        substr($row['serial_number'] ?? '', 0, 18),
        substr($row['status'] ?? '', 0, 12),
        $createdDate
    ];

    // สีเทาอ่อนสำหรับแถวคู่
    if ($sequenceNumber % 2 == 0) {
        $pdf->SetTextColor(80, 80, 80);
    } else {
        $pdf->SetTextColor(0, 0, 0);
    }

    foreach ($rowData as $i => $data) {
        $pdf->Cell($colWidths[$i], 5, $data, 0, 0, 'L');
    }
    $pdf->Ln();

    $sequenceNumber++;
    $rowsPerPage++;
}

// === MINIMAL SUMMARY ===
$totalRecords = $result->num_rows;
$pdf->Ln(8);

$pdf->SetTextColor(0, 0, 0);
$pdf->SetFont($thaiFont, '', 10);
$pdf->Cell(0, 5, "รวม $totalRecords รายการ", 'T', 1, 'R'); // เส้นบนเท่านั้น

// === MINIMAL FOOTER ===
$pdf->Ln(5);
$pdf->SetFont($thaiFont, '', 8);
$pdf->SetTextColor(150, 150, 150);
$pdf->Cell(0, 5, 'Asset Management System', 0, 1, 'C');

// สร้างชื่อไฟล์
$filename = "asset_minimal_" . date('Y-m-d_H-i-s');

// ส่งออก PDF
if ($isDownload) {
    $pdf->Output($filename . '.pdf', 'D');
} else {
    $pdf->Output($filename . '.pdf', 'I');
}

// ปิดการเชื่อมต่อฐานข้อมูล
$stmt->close();
$conn->close();
?>
