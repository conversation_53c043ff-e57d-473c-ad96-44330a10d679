"""
Asset Management GUI Application
Main application window with Add, Edit, Delete functionality
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from asset_model import AssetModel
from add_asset_dialog import AddAssetDialog
from edit_asset_dialog import EditAssetDialog
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class AssetManagementGUI:
    """Main Asset Management GUI Application"""
    
    def __init__(self):
        # Initialize the main window with ttkbootstrap theme
        self.root = ttk_bs.Window(themename="cosmo")
        self.root.title("🏢 Asset Management System - Python GUI")
        self.root.geometry("1400x800")
        self.root.minsize(1200, 600)
        
        # Initialize asset model
        self.asset_model = AssetModel()
        
        # Test database connection
        self.test_database_connection()
        
        # Create GUI components
        self.create_widgets()
        self.load_assets()
        
        # Center window on screen
        self.center_window()
    
    def test_database_connection(self):
        """Test database connection on startup"""
        try:
            from database_config import DatabaseConfig
            success, message = DatabaseConfig.test_connection()
            if not success:
                messagebox.showerror("Database Error", f"Cannot connect to database:\n{message}")
                self.root.destroy()
                return
            logging.info(f"Database connection successful: {message}")
        except Exception as e:
            messagebox.showerror("Database Error", f"Database connection failed:\n{e}")
            self.root.destroy()
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """Create and arrange GUI widgets"""
        # Main container
        main_frame = ttk_bs.Frame(self.root, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # Title
        title_label = ttk_bs.Label(
            main_frame, 
            text="🏢 Asset Management System", 
            font=("Arial", 24, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))
        
        # Search and action frame
        search_frame = ttk_bs.Frame(main_frame)
        search_frame.pack(fill=X, pady=(0, 20))
        
        # Search section
        search_label = ttk_bs.Label(search_frame, text="🔍 Search:", font=("Arial", 12))
        search_label.pack(side=LEFT, padx=(0, 10))
        
        self.search_var = tk.StringVar()
        self.search_entry = ttk_bs.Entry(
            search_frame, 
            textvariable=self.search_var,
            font=("Arial", 11),
            width=30
        )
        self.search_entry.pack(side=LEFT, padx=(0, 10))
        self.search_entry.bind('<KeyRelease>', self.on_search)
        
        search_btn = ttk_bs.Button(
            search_frame,
            text="Search",
            command=self.search_assets,
            bootstyle="info"
        )
        search_btn.pack(side=LEFT, padx=(0, 20))
        
        # Action buttons
        btn_frame = ttk_bs.Frame(search_frame)
        btn_frame.pack(side=RIGHT)
        
        self.add_btn = ttk_bs.Button(
            btn_frame,
            text="➕ Add Asset",
            command=self.add_asset,
            bootstyle="success",
            width=12
        )
        self.add_btn.pack(side=LEFT, padx=(0, 10))
        
        self.edit_btn = ttk_bs.Button(
            btn_frame,
            text="✏️ Edit Asset",
            command=self.edit_asset,
            bootstyle="warning",
            width=12,
            state=DISABLED
        )
        self.edit_btn.pack(side=LEFT, padx=(0, 10))
        
        self.delete_btn = ttk_bs.Button(
            btn_frame,
            text="🗑️ Delete Asset",
            command=self.delete_asset,
            bootstyle="danger",
            width=12,
            state=DISABLED
        )
        self.delete_btn.pack(side=LEFT, padx=(0, 10))
        
        refresh_btn = ttk_bs.Button(
            btn_frame,
            text="🔄 Refresh",
            command=self.load_assets,
            bootstyle="secondary",
            width=12
        )
        refresh_btn.pack(side=LEFT)
        
        # Assets table frame
        table_frame = ttk_bs.Frame(main_frame)
        table_frame.pack(fill=BOTH, expand=True)
        
        # Create treeview for assets
        columns = ('ID', 'Type', 'Brand', 'Model', 'Tag', 'Department', 'Status', 'Hostname', 'OS', 'Serial', 'Asset ID', 'Created')
        
        self.tree = ttk_bs.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=20
        )
        
        # Configure columns
        column_widths = {
            'ID': 60, 'Type': 120, 'Brand': 100, 'Model': 120, 'Tag': 80,
            'Department': 120, 'Status': 80, 'Hostname': 120, 'OS': 100,
            'Serial': 120, 'Asset ID': 100, 'Created': 120
        }
        
        for col in columns:
            self.tree.heading(col, text=col, anchor=W)
            self.tree.column(col, width=column_widths.get(col, 100), anchor=W)
        
        # Scrollbars
        v_scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk_bs.Scrollbar(table_frame, orient=HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        v_scrollbar.pack(side=RIGHT, fill=Y)
        h_scrollbar.pack(side=BOTTOM, fill=X)
        
        # Bind selection event
        self.tree.bind('<<TreeviewSelect>>', self.on_select)
        self.tree.bind('<Double-1>', self.on_double_click)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk_bs.Label(
            main_frame,
            textvariable=self.status_var,
            relief=SUNKEN,
            anchor=W,
            padding=5
        )
        status_bar.pack(side=BOTTOM, fill=X, pady=(10, 0))
    
    def load_assets(self):
        """Load assets from database"""
        try:
            # Clear existing items
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Load assets
            assets = self.asset_model.get_all_assets()
            
            for asset in assets:
                # Format created date
                created_date = ""
                if asset.get('created_date'):
                    if isinstance(asset['created_date'], datetime):
                        created_date = asset['created_date'].strftime('%Y-%m-%d')
                    else:
                        created_date = str(asset['created_date'])[:10]
                
                # Insert into treeview
                self.tree.insert('', 'end', values=(
                    asset.get('id', ''),
                    asset.get('type', ''),
                    asset.get('brand', ''),
                    asset.get('model', ''),
                    asset.get('tag', ''),
                    asset.get('department', ''),
                    asset.get('status', ''),
                    asset.get('hostname', ''),
                    asset.get('operating_system', ''),
                    asset.get('serial_number', ''),
                    asset.get('asset_id', ''),
                    created_date
                ))
            
            self.status_var.set(f"Loaded {len(assets)} assets")
            logging.info(f"Loaded {len(assets)} assets")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load assets:\n{e}")
            logging.error(f"Failed to load assets: {e}")
    
    def on_search(self, event=None):
        """Handle search input"""
        # Implement real-time search with a small delay
        self.root.after(500, self.search_assets)
    
    def search_assets(self):
        """Search assets based on search term"""
        search_term = self.search_var.get().strip()
        
        try:
            # Clear existing items
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Load filtered assets
            assets = self.asset_model.get_all_assets(search_term)
            
            for asset in assets:
                # Format created date
                created_date = ""
                if asset.get('created_date'):
                    if isinstance(asset['created_date'], datetime):
                        created_date = asset['created_date'].strftime('%Y-%m-%d')
                    else:
                        created_date = str(asset['created_date'])[:10]
                
                # Insert into treeview
                self.tree.insert('', 'end', values=(
                    asset.get('id', ''),
                    asset.get('type', ''),
                    asset.get('brand', ''),
                    asset.get('model', ''),
                    asset.get('tag', ''),
                    asset.get('department', ''),
                    asset.get('status', ''),
                    asset.get('hostname', ''),
                    asset.get('operating_system', ''),
                    asset.get('serial_number', ''),
                    asset.get('asset_id', ''),
                    created_date
                ))
            
            if search_term:
                self.status_var.set(f"Found {len(assets)} assets matching '{search_term}'")
            else:
                self.status_var.set(f"Showing all {len(assets)} assets")
                
        except Exception as e:
            messagebox.showerror("Error", f"Search failed:\n{e}")
            logging.error(f"Search failed: {e}")
    
    def on_select(self, event=None):
        """Handle treeview selection"""
        selection = self.tree.selection()
        if selection:
            self.edit_btn.config(state=NORMAL)
            self.delete_btn.config(state=NORMAL)
        else:
            self.edit_btn.config(state=DISABLED)
            self.delete_btn.config(state=DISABLED)
    
    def on_double_click(self, event=None):
        """Handle double-click on treeview item"""
        self.edit_asset()
    
    def get_selected_asset_id(self):
        """Get selected asset ID"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            return item['values'][0]  # ID is the first column
        return None

    def add_asset(self):
        """Open Add Asset dialog"""
        try:
            dialog = AddAssetDialog(self.root, self.asset_model)
            if dialog.result:
                self.load_assets()
                self.status_var.set("Asset added successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open Add Asset dialog:\n{e}")
            logging.error(f"Failed to open Add Asset dialog: {e}")

    def edit_asset(self):
        """Open Edit Asset dialog"""
        asset_id = self.get_selected_asset_id()
        if not asset_id:
            messagebox.showwarning("Warning", "Please select an asset to edit")
            return

        try:
            dialog = EditAssetDialog(self.root, self.asset_model, asset_id)
            if dialog.result:
                self.load_assets()
                self.status_var.set("Asset updated successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open Edit Asset dialog:\n{e}")
            logging.error(f"Failed to open Edit Asset dialog: {e}")

    def delete_asset(self):
        """Delete selected asset"""
        asset_id = self.get_selected_asset_id()
        if not asset_id:
            messagebox.showwarning("Warning", "Please select an asset to delete")
            return

        # Get asset details for confirmation
        asset = self.asset_model.get_asset_by_id(asset_id)
        if not asset:
            messagebox.showerror("Error", "Asset not found")
            return

        # Confirmation dialog
        asset_info = f"Type: {asset.get('type', 'N/A')}\nBrand: {asset.get('brand', 'N/A')}\nModel: {asset.get('model', 'N/A')}"
        confirm = messagebox.askyesno(
            "Confirm Delete",
            f"Are you sure you want to delete this asset?\n\n{asset_info}\n\nThis action cannot be undone.",
            icon='warning'
        )

        if confirm:
            try:
                success, message = self.asset_model.delete_asset(asset_id)
                if success:
                    messagebox.showinfo("Success", message)
                    self.load_assets()
                    self.status_var.set("Asset deleted successfully")
                else:
                    messagebox.showerror("Error", message)
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete asset:\n{e}")
                logging.error(f"Failed to delete asset: {e}")

    def run(self):
        """Start the GUI application"""
        self.root.mainloop()


def main():
    """Main function to run the application"""
    try:
        app = AssetManagementGUI()
        app.run()
    except Exception as e:
        logging.error(f"Application error: {e}")
        messagebox.showerror("Application Error", f"Failed to start application:\n{e}")


if __name__ == "__main__":
    main()
