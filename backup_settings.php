<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'classes/BackupManager.php';

// ตรวจสอบการเข้าสู่ระบบและสิทธิ์ Admin
requireLogin();
if (!isAdmin()) {
    header('Location: index.php');
    exit;
}

$backupManager = new BackupManager($pdo);
$message = '';
$messageType = '';

// ประมวลผลการบันทึกการตั้งค่า
if ($_POST && isset($_POST['save_settings'])) {
    try {
        $config = [
            'enabled' => isset($_POST['enabled']),
            'backup_path' => trim($_POST['backup_path']) ?: 'backups/',
            'max_backups' => max(1, intval($_POST['max_backups'])),
            'email_enabled' => isset($_POST['email_enabled']),
            'email_recipients' => array_filter(array_map('trim', explode(',', $_POST['email_recipients']))),
            'smtp_host' => trim($_POST['smtp_host']),
            'smtp_port' => intval($_POST['smtp_port']) ?: 587,
            'smtp_username' => trim($_POST['smtp_username']),
            'smtp_password' => trim($_POST['smtp_password']),
            'smtp_secure' => $_POST['smtp_secure'] ?: 'tls'
        ];
        
        // ตรวจสอบและสร้างโฟลเดอร์ backup
        if (!is_dir($config['backup_path'])) {
            if (!mkdir($config['backup_path'], 0755, true)) {
                throw new Exception('ไม่สามารถสร้างโฟลเดอร์ backup ได้');
            }
        }
        
        $backupManager->updateConfig($config);
        $message = 'บันทึกการตั้งค่าเรียบร้อยแล้ว';
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// ประมวลผลการสร้าง backup manual
if ($_POST && isset($_POST['create_backup'])) {
    try {
        $description = trim($_POST['backup_description']) ?: 'Manual backup';
        $filename = $backupManager->createAutoBackup('MANUAL', $description);
        
        if ($filename) {
            $message = "สร้าง Backup สำเร็จ: {$filename}";
            $messageType = 'success';
        } else {
            $message = 'ไม่สามารถสร้าง Backup ได้';
            $messageType = 'danger';
        }
        
    } catch (Exception $e) {
        $message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

$config = $backupManager->getConfig();
$backupLogs = $backupManager->getBackupLogs(20);
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ตั้งค่า Auto Backup - Asset Management System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/style.css">

    <style>
        .input-group {
            display: flex;
        }

        .input-group .form-control {
            flex: 1;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .input-group-append {
            display: flex;
        }

        .input-group-append .btn {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: 0;
        }

        .folder-browser {
            max-height: 500px;
        }

        .drive-selection {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .drive-selection label {
            font-weight: bold;
            margin-bottom: 5px;
            display: block;
        }

        .current-path {
            margin-bottom: 15px;
        }

        .current-path label {
            font-weight: bold;
            margin-bottom: 5px;
            display: block;
        }

        .folder-navigation {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }

        .folder-navigation .btn {
            margin-right: 10px;
        }

        .folder-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }

        .folder-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .folder-item:hover {
            background-color: #f8f9fa;
        }

        .folder-item.writable {
            border-left: 3px solid #28a745;
        }

        .folder-item.not-writable {
            border-left: 3px solid #dc3545;
            opacity: 0.7;
        }

        .folder-item i:first-child {
            margin-right: 10px;
            width: 16px;
        }

        .folder-name {
            flex: 1;
        }

        .folder-item i:last-child {
            margin-left: 10px;
        }

        .loading, .error, .empty-folders {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error {
            color: #dc3545;
        }

        .alert {
            padding: 8px 12px;
            border-radius: 4px;
            margin-top: 5px;
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .text-success {
            color: #28a745 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Cache Button Styling */
        .btn-cache {
            position: relative;
            overflow: hidden;
        }

        .btn-cache:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .btn-cache .fa-spin {
            animation: fa-spin 1s infinite linear;
        }

        @keyframes fa-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ระบบจัดการ Asset</h1>
            <p class="subtitle">ตั้งค่า Auto Backup SQL Database</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="index.php"><i class="fas fa-list"></i> รายการ Assets</a></li>
                <?php if (isAdmin()): ?>
                    <li><a href="users.php"><i class="fas fa-users"></i> จัดการผู้ใช้</a></li>
                <?php endif; ?>
                <li><a href="#" onclick="openToolsModal()" class="active"><i class="fas fa-tools"></i> เครื่องมือ</a></li>
                <li><a href="#" onclick="openProfileModal()"><i class="fas fa-user"></i> โปรไฟล์</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> ออกจากระบบ</a></li>
            </ul>
        </nav>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- การตั้งค่า Backup -->
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-cog"></i> การตั้งค่า Auto Backup</h2>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="enabled" <?= $config['enabled'] ? 'checked' : '' ?>>
                                เปิดใช้งาน Auto Backup
                            </label>
                            <small class="form-text">เมื่อเปิดใช้งาน ระบบจะสร้าง backup อัตโนมัติทุกครั้งที่มีการเพิ่ม แก้ไข หรือลบข้อมูล</small>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="backup_path">โฟลเดอร์เก็บ Backup</label>
                            <div class="input-group">
                                <input type="text" id="backup_path" name="backup_path" class="form-control"
                                       value="<?= htmlspecialchars($config['backup_path']) ?>" required>
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary" onclick="openFolderBrowser()" title="เลือกโฟลเดอร์">
                                        <i class="fas fa-folder-open"></i> เลือกโฟลเดอร์
                                    </button>
                                </div>
                            </div>
                            <small class="form-text">ระบุ path ที่ต้องการเก็บไฟล์ backup (เช่น backups/ หรือ /var/backups/)</small>
                            <div id="path_validation" class="mt-2" style="display: none;"></div>
                        </div>
                        <div class="form-group">
                            <label for="max_backups">จำนวน Backup สูงสุด</label>
                            <input type="number" id="max_backups" name="max_backups" class="form-control" 
                                   value="<?= $config['max_backups'] ?>" min="1" max="100" required>
                            <small class="form-text">ระบบจะลบ backup เก่าอัตโนมัติเมื่อเกินจำนวนที่กำหนด</small>
                        </div>
                    </div>

                    <hr>
                    <h4><i class="fas fa-envelope"></i> การตั้งค่า Email แจ้งเตือน</h4>

                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="email_enabled" <?= $config['email_enabled'] ? 'checked' : '' ?>>
                                เปิดใช้งานการส่ง Email แจ้งเตือน
                            </label>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="email_recipients">Email ผู้รับ</label>
                            <input type="text" id="email_recipients" name="email_recipients" class="form-control" 
                                   value="<?= htmlspecialchars(implode(', ', $config['email_recipients'])) ?>"
                                   placeholder="<EMAIL>, <EMAIL>">
                            <small class="form-text">ระบุ email ผู้รับ คั่นด้วยเครื่องหมายจุลภาค</small>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="smtp_host">SMTP Host</label>
                            <input type="text" id="smtp_host" name="smtp_host" class="form-control" 
                                   value="<?= htmlspecialchars($config['smtp_host']) ?>"
                                   placeholder="smtp.gmail.com">
                        </div>
                        <div class="form-group">
                            <label for="smtp_port">SMTP Port</label>
                            <input type="number" id="smtp_port" name="smtp_port" class="form-control" 
                                   value="<?= $config['smtp_port'] ?>" placeholder="587">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="smtp_username">SMTP Username</label>
                            <input type="email" id="smtp_username" name="smtp_username" class="form-control" 
                                   value="<?= htmlspecialchars($config['smtp_username']) ?>"
                                   placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="smtp_password">SMTP Password</label>
                            <input type="password" id="smtp_password" name="smtp_password" class="form-control" 
                                   value="<?= htmlspecialchars($config['smtp_password']) ?>"
                                   placeholder="App Password หรือ Email Password">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="smtp_secure">SMTP Security</label>
                            <select id="smtp_secure" name="smtp_secure" class="form-control">
                                <option value="tls" <?= $config['smtp_secure'] === 'tls' ? 'selected' : '' ?>>TLS</option>
                                <option value="ssl" <?= $config['smtp_secure'] === 'ssl' ? 'selected' : '' ?>>SSL</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" name="save_settings" class="btn btn-primary">
                            <i class="fas fa-save"></i> บันทึกการตั้งค่า
                        </button>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> กลับหน้าหลัก
                        </a>
                        <button type="button" class="btn btn-warning btn-cache" onclick="clearBrowserCache()">
                            <i class="fas fa-broom"></i> ล้าง Browser Cache
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- สร้าง Backup Manual -->
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-download"></i> สร้าง Backup Manual</h2>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="backup_description">รายละเอียด Backup</label>
                            <input type="text" id="backup_description" name="backup_description" class="form-control" 
                                   placeholder="เช่น Backup ก่อนอัปเดตระบบ">
                        </div>
                    </div>
                    <div class="form-group">
                        <button type="submit" name="create_backup" class="btn btn-success">
                            <i class="fas fa-database"></i> สร้าง Backup ทันที
                        </button>
                        <button type="button" class="btn btn-info btn-cache" onclick="clearSystemCache()">
                            <i class="fas fa-sync-alt"></i> ล้าง System Cache
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- ประวัติ Backup -->
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-history"></i> ประวัติ Backup (20 รายการล่าสุด)</h2>
            </div>
            <div class="card-body">
                <?php if (empty($backupLogs)): ?>
                    <div class="empty-state">
                        <div class="icon">📁</div>
                        <h3>ยังไม่มีประวัติ Backup</h3>
                        <p>เมื่อระบบสร้าง backup ประวัติจะแสดงที่นี่</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ไฟล์ Backup</th>
                                    <th>การดำเนินการ</th>
                                    <th>รายละเอียด</th>
                                    <th>ขนาดไฟล์</th>
                                    <th>วันที่สร้าง</th>
                                    <th>ผู้สร้าง</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($backupLogs as $log): ?>
                                    <tr>
                                        <td><code><?= htmlspecialchars($log['filename']) ?></code></td>
                                        <td>
                                            <?php
                                            $actionColors = [
                                                'CREATE' => 'success',
                                                'UPDATE' => 'warning', 
                                                'DELETE' => 'danger',
                                                'MANUAL' => 'info'
                                            ];
                                            $color = $actionColors[$log['action_type']] ?? 'secondary';
                                            ?>
                                            <span class="badge badge-<?= $color ?>"><?= htmlspecialchars($log['action_type']) ?></span>
                                        </td>
                                        <td><?= htmlspecialchars($log['description']) ?></td>
                                        <td><?= formatFileSize($log['file_size']) ?></td>
                                        <td><?= formatDateTime($log['created_date']) ?></td>
                                        <td><?= htmlspecialchars($log['created_by']) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Folder Browser Modal -->
    <div id="folderBrowserModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h2><i class="fas fa-folder-open"></i> เลือกโฟลเดอร์สำหรับเก็บ Backup</h2>
                <span class="close" onclick="closeFolderBrowser()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="folder-browser">
                    <!-- Drive Selection -->
                    <div class="drive-selection">
                        <label>เลือก Drive:</label>
                        <select id="drive_selector" class="form-control" onchange="changeDrive()">
                            <option value="">กำลังโหลด drives...</option>
                        </select>
                    </div>

                    <div class="current-path">
                        <label>Path ปัจจุบัน:</label>
                        <input type="text" id="current_path" class="form-control" readonly>
                    </div>

                    <div class="folder-navigation">
                        <button type="button" class="btn btn-sm btn-secondary" onclick="navigateUp()">
                            <i class="fas fa-level-up-alt"></i> ขึ้นไปหนึ่งระดับ
                        </button>
                        <button type="button" class="btn btn-sm btn-info" onclick="refreshFolders()">
                            <i class="fas fa-sync"></i> รีเฟรช
                        </button>
                        <button type="button" class="btn btn-sm btn-success" onclick="createNewFolder()">
                            <i class="fas fa-plus"></i> สร้างโฟลเดอร์ใหม่
                        </button>
                        <button type="button" class="btn btn-sm btn-warning" onclick="goToRoot()">
                            <i class="fas fa-home"></i> Root
                        </button>
                        <button type="button" class="btn btn-sm btn-danger btn-cache" onclick="clearBrowserCache()">
                            <i class="fas fa-trash-alt"></i> Clear Cache
                        </button>
                    </div>

                    <div class="folder-list" id="folder_list">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i> กำลังโหลด...
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="selectCurrentPath()">
                    <i class="fas fa-check"></i> เลือกโฟลเดอร์นี้
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeFolderBrowser()">ยกเลิก</button>
            </div>
        </div>
    </div>

    <script>
        let currentBrowsePath = '';

        // ตรวจสอบการเปิด/ปิดใช้งาน email
        document.querySelector('input[name="email_enabled"]').addEventListener('change', function() {
            const emailFields = document.querySelectorAll('#email_recipients, #smtp_host, #smtp_port, #smtp_username, #smtp_password, #smtp_secure');
            emailFields.forEach(field => {
                field.disabled = !this.checked;
            });
        });

        // เรียกใช้เมื่อโหลดหน้า
        document.addEventListener('DOMContentLoaded', function() {
            const emailEnabled = document.querySelector('input[name="email_enabled"]');
            if (emailEnabled) {
                emailEnabled.dispatchEvent(new Event('change'));
            }

            // ตรวจสอบ path เมื่อพิมพ์
            document.getElementById('backup_path').addEventListener('input', validatePath);
            document.getElementById('backup_path').addEventListener('blur', validatePath);
        });

        // ฟังก์ชันเปิด Folder Browser
        function openFolderBrowser() {
            const currentPath = document.getElementById('backup_path').value || './';
            currentBrowsePath = currentPath;
            document.getElementById('current_path').value = currentBrowsePath;
            document.getElementById('folderBrowserModal').style.display = 'block';
            document.body.style.overflow = 'hidden';

            // โหลด drives ก่อน
            loadDrives();
            loadFolders(currentBrowsePath);
        }

        // โหลดรายการ drives
        function loadDrives() {
            fetch('get_drives.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                const driveSelector = document.getElementById('drive_selector');
                driveSelector.innerHTML = '';

                if (data.success && data.drives.length > 0) {
                    // เพิ่มตัวเลือก "เลือก Drive"
                    driveSelector.innerHTML += '<option value="">-- เลือก Drive --</option>';

                    data.drives.forEach(drive => {
                        const option = document.createElement('option');
                        option.value = drive.path;
                        option.textContent = `${drive.name} (${drive.free_space})`;
                        driveSelector.appendChild(option);
                    });

                    // เลือก drive ปัจจุบันถ้ามี
                    const currentDrive = getCurrentDrive(currentBrowsePath);
                    if (currentDrive) {
                        driveSelector.value = currentDrive;
                    }
                } else {
                    driveSelector.innerHTML = '<option value="">ไม่พบ drives</option>';
                }
            })
            .catch(error => {
                console.error('Error loading drives:', error);
                document.getElementById('drive_selector').innerHTML = '<option value="">เกิดข้อผิดพลาดในการโหลด drives</option>';
            });
        }

        // หา drive ปัจจุบันจาก path
        function getCurrentDrive(path) {
            if (!path) return null;

            // สำหรับ Windows (C:, D:, etc.)
            const windowsMatch = path.match(/^([A-Za-z]:)/);
            if (windowsMatch) {
                return windowsMatch[1] + '\\';
            }

            // สำหรับ Unix-like systems
            if (path.startsWith('/')) {
                return '/';
            }

            return null;
        }

        // เปลี่ยน drive
        function changeDrive() {
            const selectedDrive = document.getElementById('drive_selector').value;
            if (selectedDrive) {
                currentBrowsePath = selectedDrive;
                loadFolders(selectedDrive);
            }
        }

        // ไปที่ root
        function goToRoot() {
            const selectedDrive = document.getElementById('drive_selector').value;
            if (selectedDrive) {
                currentBrowsePath = selectedDrive;
                loadFolders(selectedDrive);
            } else {
                // ถ้าไม่ได้เลือก drive ให้ไปที่ root ของระบบ
                const rootPath = navigator.platform.toLowerCase().includes('win') ? 'C:\\' : '/';
                currentBrowsePath = rootPath;
                loadFolders(rootPath);
            }
        }

        // ฟังก์ชันปิด Folder Browser
        function closeFolderBrowser() {
            document.getElementById('folderBrowserModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // โหลดรายการโฟลเดอร์
        function loadFolders(path) {
            const folderList = document.getElementById('folder_list');
            folderList.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> กำลังโหลด...</div>';

            fetch('get_folders.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ path: path })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayFolders(data.folders);
                    currentBrowsePath = data.current_path;
                    document.getElementById('current_path').value = currentBrowsePath;

                    // อัพเดท drive selector
                    const currentDrive = getCurrentDrive(currentBrowsePath);
                    if (currentDrive) {
                        document.getElementById('drive_selector').value = currentDrive;
                    }
                } else {
                    folderList.innerHTML = '<div class="error">เกิดข้อผิดพลาด: ' + data.message + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                folderList.innerHTML = '<div class="error">เกิดข้อผิดพลาดในการโหลดโฟลเดอร์</div>';
            });
        }

        // แสดงรายการโฟลเดอร์
        function displayFolders(folders) {
            const folderList = document.getElementById('folder_list');

            if (folders.length === 0) {
                folderList.innerHTML = '<div class="empty-folders">ไม่มีโฟลเดอร์ในตำแหน่งนี้</div>';
                return;
            }

            let html = '<div class="folders">';
            folders.forEach(folder => {
                const isWritable = folder.writable ? 'writable' : 'not-writable';
                const icon = folder.writable ? 'fa-folder' : 'fa-folder-minus';
                const title = folder.writable ? 'สามารถเขียนได้' : 'ไม่สามารถเขียนได้';

                // Escape path สำหรับ JavaScript
                const escapedPath = folder.path.replace(/\\/g, '\\\\').replace(/'/g, "\\'");

                html += `
                    <div class="folder-item ${isWritable}" onclick="navigateToFolder('${escapedPath}')" title="${title}">
                        <i class="fas ${icon}"></i>
                        <span class="folder-name">${folder.name}</span>
                        ${folder.writable ? '<i class="fas fa-check-circle text-success"></i>' : '<i class="fas fa-times-circle text-danger"></i>'}
                    </div>
                `;
            });
            html += '</div>';

            folderList.innerHTML = html;
        }

        // นำทางไปยังโฟลเดอร์
        function navigateToFolder(path) {
            loadFolders(path);
        }

        // ขึ้นไปหนึ่งระดับ
        function navigateUp() {
            let parentPath = currentBrowsePath.replace(/[^\/\\]+[\/\\]?$/, '');

            // ตรวจสอบว่าถึง root ของ drive แล้วหรือไม่
            if (navigator.platform.toLowerCase().includes('win')) {
                // Windows: ถ้าถึง C:\ แล้วไม่ให้ขึ้นไปอีก
                if (parentPath.match(/^[A-Za-z]:\\?$/)) {
                    parentPath = parentPath.replace(/\\?$/, '\\');
                }
            } else {
                // Unix-like: ถ้าถึง / แล้วไม่ให้ขึ้นไปอีก
                if (parentPath === '' || parentPath === '/') {
                    parentPath = '/';
                }
            }

            if (parentPath && parentPath !== currentBrowsePath) {
                loadFolders(parentPath);
            }
        }

        // รีเฟรชโฟลเดอร์
        function refreshFolders() {
            loadFolders(currentBrowsePath);
        }

        // สร้างโฟลเดอร์ใหม่
        function createNewFolder() {
            const folderName = prompt('ชื่อโฟลเดอร์ใหม่:');
            if (folderName && folderName.trim()) {
                fetch('create_folder.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        path: currentBrowsePath,
                        name: folderName.trim()
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('สร้างโฟลเดอร์สำเร็จ');
                        refreshFolders();
                    } else {
                        alert('เกิดข้อผิดพลาด: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการสร้างโฟลเดอร์');
                });
            }
        }

        // เลือก path ปัจจุบัน
        function selectCurrentPath() {
            document.getElementById('backup_path').value = currentBrowsePath;
            closeFolderBrowser();
            validatePath();
        }

        // ตรวจสอบ path
        function validatePath() {
            const path = document.getElementById('backup_path').value;
            const validationDiv = document.getElementById('path_validation');

            if (!path.trim()) {
                validationDiv.style.display = 'none';
                return;
            }

            fetch('validate_path.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ path: path })
            })
            .then(response => response.json())
            .then(data => {
                validationDiv.style.display = 'block';
                if (data.valid) {
                    validationDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check"></i> โฟลเดอร์ถูกต้องและสามารถเขียนได้</div>';
                } else {
                    validationDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> ' + data.message + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                validationDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times"></i> เกิดข้อผิดพลาดในการตรวจสอบ</div>';
            });
        }

        // ล้าง Browser Cache
        function clearBrowserCache() {
            if (confirm('คุณต้องการล้าง Browser Cache หรือไม่?\n\nการดำเนินการนี้จะ:\n- ล้างข้อมูล Cache ของเบราว์เซอร์\n- ล้าง Local Storage\n- ล้าง Session Storage\n- รีเฟรชหน้าเว็บ')) {
                try {
                    // ล้าง Local Storage
                    if (typeof(Storage) !== "undefined" && localStorage) {
                        localStorage.clear();
                        console.log('Local Storage cleared');
                    }

                    // ล้าง Session Storage
                    if (typeof(Storage) !== "undefined" && sessionStorage) {
                        sessionStorage.clear();
                        console.log('Session Storage cleared');
                    }

                    // ล้าง Cache ผ่าน Service Worker (ถ้ามี)
                    if ('serviceWorker' in navigator) {
                        navigator.serviceWorker.getRegistrations().then(function(registrations) {
                            for(let registration of registrations) {
                                registration.unregister();
                            }
                        });
                    }

                    // ล้าง Cache API (ถ้ามี)
                    if ('caches' in window) {
                        caches.keys().then(function(names) {
                            for (let name of names) {
                                caches.delete(name);
                            }
                        });
                    }

                    // แสดงข้อความสำเร็จ
                    alert('ล้าง Browser Cache เรียบร้อยแล้ว\nหน้าเว็บจะรีเฟรชอัตโนมัติ');

                    // รีเฟรชหน้าเว็บแบบ Hard Reload
                    window.location.reload(true);

                } catch (error) {
                    console.error('Error clearing cache:', error);
                    alert('เกิดข้อผิดพลาดในการล้าง Cache: ' + error.message);
                }
            }
        }

        // ล้าง System Cache
        function clearSystemCache() {
            if (confirm('คุณต้องการล้าง System Cache หรือไม่?\n\nการดำเนินการนี้จะ:\n- ล้าง PHP OpCache (ถ้ามี)\n- ล้าง Application Cache\n- ล้าง Temporary Files\n- รีเฟรชข้อมูลระบบ')) {

                // แสดง loading
                const originalText = event.target.innerHTML;
                event.target.innerHTML = '<i class="fas fa-spinner fa-spin"></i> กำลังล้าง Cache...';
                event.target.disabled = true;

                fetch('clear_system_cache.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ action: 'clear_cache' })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('ล้าง System Cache เรียบร้อยแล้ว\n\n' + data.message);

                        // ล้าง browser cache ด้วย
                        if (confirm('ต้องการล้าง Browser Cache ด้วยหรือไม่?')) {
                            clearBrowserCache();
                        } else {
                            // รีเฟรชหน้าเว็บ
                            window.location.reload();
                        }
                    } else {
                        alert('เกิดข้อผิดพลาด: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการล้าง System Cache: ' + error.message);
                })
                .finally(() => {
                    // คืนค่าปุ่ม
                    event.target.innerHTML = originalText;
                    event.target.disabled = false;
                });
            }
        }

        // ปิด modal เมื่อคลิกนอก modal
        window.onclick = function(event) {
            const modal = document.getElementById('folderBrowserModal');
            if (event.target === modal) {
                closeFolderBrowser();
            }
        }
    </script>
</body>
</html>
