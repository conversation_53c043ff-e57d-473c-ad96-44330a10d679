<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>🧪 ทดสอบ NSLOOKUP และ IP Address Lookup</h2>";

// ฟังก์ชันสำหรับ NSLOOKUP
function performNslookup($hostname) {
    $result = [
        'success' => false,
        'ip_address' => null,
        'method' => '',
        'error' => '',
        'execution_time' => 0,
        'debug_info' => []
    ];

    $startTime = microtime(true);

    // ตรวจสอบว่า hostname ไม่ว่าง
    if (empty(trim($hostname))) {
        $result['error'] = 'Hostname is empty';
        return $result;
    }

    $hostname = trim($hostname);

    // วิธีที่ 1: ใช้ gethostbyname (PHP built-in) - IPv4 เท่านั้น
    $ip = gethostbyname($hostname);
    $result['debug_info'][] = "gethostbyname result: $ip";

    if ($ip !== $hostname && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
        $result['success'] = true;
        $result['ip_address'] = $ip;
        $result['method'] = 'gethostbyname';
        $result['execution_time'] = microtime(true) - $startTime;
        return $result;
    }

    // วิธีที่ 2: ใช้ dns_get_record - IPv4 เท่านั้น
    if (function_exists('dns_get_record')) {
        $records = @dns_get_record($hostname, DNS_A);
        $result['debug_info'][] = "dns_get_record result: " . print_r($records, true);

        if ($records && isset($records[0]['ip'])) {
            $ip = $records[0]['ip'];
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                $result['success'] = true;
                $result['ip_address'] = $ip;
                $result['method'] = 'dns_get_record';
                $result['execution_time'] = microtime(true) - $startTime;
                return $result;
            }
        }
    }

    // วิธีที่ 3: ใช้ nslookup command แบบปรับปรุง
    if (function_exists('exec')) {
        $output = [];
        $return_var = 0;

        // สำหรับ Windows
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            exec("nslookup $hostname 2>&1", $output, $return_var);
        } else {
            // สำหรับ Linux/Unix
            exec("nslookup $hostname 2>&1", $output, $return_var);
        }

        $result['debug_info'][] = "nslookup output: " . implode("\n", $output);

        if ($return_var === 0 && !empty($output)) {
            $foundDnsServer = false;

            foreach ($output as $line) {
                $line = trim($line);

                // ข้าม DNS Server address
                if (preg_match('/Server:|Address:.*#53/', $line)) {
                    $foundDnsServer = true;
                    continue;
                }

                // หา IPv4 Address ที่ไม่ใช่ DNS Server
                if (preg_match('/Address:\s*(\d+\.\d+\.\d+\.\d+)/', $line, $matches)) {
                    if (filter_var($matches[1], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                        $result['success'] = true;
                        $result['ip_address'] = $matches[1];
                        $result['method'] = 'nslookup command';
                        $result['execution_time'] = microtime(true) - $startTime;
                        return $result;
                    }
                }

                // สำหรับ Windows format
                if (preg_match('/Name:\s*' . preg_quote($hostname, '/') . '/i', $line)) {
                    // หาบรรทัดถัดไปที่มี Address
                    $nextLineIndex = array_search($line, $output) + 1;
                    if (isset($output[$nextLineIndex])) {
                        $nextLine = trim($output[$nextLineIndex]);
                        if (preg_match('/Address(?:es)?:\s*(\d+\.\d+\.\d+\.\d+)/', $nextLine, $matches)) {
                            if (filter_var($matches[1], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                                $result['success'] = true;
                                $result['ip_address'] = $matches[1];
                                $result['method'] = 'nslookup command (Windows format)';
                                $result['execution_time'] = microtime(true) - $startTime;
                                return $result;
                            }
                        }
                    }
                }
            }
        }
    }

    // วิธีที่ 4: ใช้ dig command (สำหรับ Linux/Unix)
    if (function_exists('exec') && strtoupper(substr(PHP_OS, 0, 3)) !== 'WIN') {
        $output = [];
        $return_var = 0;

        exec("dig +short $hostname A 2>&1", $output, $return_var);
        $result['debug_info'][] = "dig output: " . implode("\n", $output);

        if ($return_var === 0 && !empty($output)) {
            foreach ($output as $line) {
                $line = trim($line);
                if (filter_var($line, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                    $result['success'] = true;
                    $result['ip_address'] = $line;
                    $result['method'] = 'dig command';
                    $result['execution_time'] = microtime(true) - $startTime;
                    return $result;
                }
            }
        }
    }

    $result['error'] = 'Unable to resolve hostname';
    $result['execution_time'] = microtime(true) - $startTime;
    return $result;
}

// ทดสอบ NSLOOKUP
if (isset($_POST['test_hostname'])) {
    $testHostname = $_POST['hostname'];
    echo "<h3>🔍 ผลการทดสอบ NSLOOKUP:</h3>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Hostname:</strong> " . htmlspecialchars($testHostname) . "</p>";
    
    $lookupResult = performNslookup($testHostname);
    
    if ($lookupResult['success']) {
        echo "<p style='color: green;'><strong>✅ สำเร็จ!</strong></p>";
        echo "<p><strong>IP Address:</strong> " . htmlspecialchars($lookupResult['ip_address']) . "</p>";
        echo "<p><strong>วิธีการ:</strong> " . htmlspecialchars($lookupResult['method']) . "</p>";
        echo "<p><strong>เวลาที่ใช้:</strong> " . number_format($lookupResult['execution_time'] * 1000, 2) . " ms</p>";
    } else {
        echo "<p style='color: red;'><strong>❌ ล้มเหลว</strong></p>";
        echo "<p><strong>ข้อผิดพลาด:</strong> " . htmlspecialchars($lookupResult['error']) . "</p>";
        echo "<p><strong>เวลาที่ใช้:</strong> " . number_format($lookupResult['execution_time'] * 1000, 2) . " ms</p>";
    }

    // แสดงข้อมูล debug
    if (isset($lookupResult['debug_info']) && !empty($lookupResult['debug_info'])) {
        echo "<details style='margin-top: 10px;'>";
        echo "<summary style='cursor: pointer; color: #007bff;'><strong>🔍 ข้อมูล Debug (คลิกเพื่อดู)</strong></summary>";
        echo "<div style='background: #f8f9fa; padding: 10px; margin-top: 5px; border-radius: 3px; font-family: monospace; font-size: 0.9rem;'>";
        foreach ($lookupResult['debug_info'] as $info) {
            echo "<pre>" . htmlspecialchars($info) . "</pre>";
        }
        echo "</div>";
        echo "</details>";
    }
    echo "</div>";
}

// อัปเดต IP Address สำหรับ Asset
if (isset($_POST['update_asset_ip'])) {
    $assetId = $_POST['asset_id'];
    
    try {
        // ดึงข้อมูล Asset
        $stmt = $pdo->prepare("SELECT id, asset_id, hostname FROM assets WHERE id = ?");
        $stmt->execute([$assetId]);
        $asset = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($asset && !empty($asset['hostname'])) {
            echo "<h3>🔄 กำลังอัปเดต IP Address:</h3>";
            
            $lookupResult = performNslookup($asset['hostname']);
            
            if ($lookupResult['success']) {
                // อัปเดต IP Address ในฐานข้อมูล
                $updateStmt = $pdo->prepare("UPDATE assets SET ip_address = ?, last_ip_check = NOW() WHERE id = ?");
                $updateStmt->execute([$lookupResult['ip_address'], $assetId]);
                
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<p style='color: green;'><strong>✅ อัปเดต IP Address สำเร็จ!</strong></p>";
                echo "<p><strong>Asset ID:</strong> " . htmlspecialchars($asset['asset_id']) . "</p>";
                echo "<p><strong>Hostname:</strong> " . htmlspecialchars($asset['hostname']) . "</p>";
                echo "<p><strong>IP Address:</strong> " . htmlspecialchars($lookupResult['ip_address']) . "</p>";
                echo "<p><strong>วิธีการ:</strong> " . htmlspecialchars($lookupResult['method']) . "</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<p style='color: red;'><strong>❌ ไม่สามารถหา IP Address ได้</strong></p>";
                echo "<p><strong>Asset ID:</strong> " . htmlspecialchars($asset['asset_id']) . "</p>";
                echo "<p><strong>Hostname:</strong> " . htmlspecialchars($asset['hostname']) . "</p>";
                echo "<p><strong>ข้อผิดพลาด:</strong> " . htmlspecialchars($lookupResult['error']) . "</p>";
                echo "</div>";
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

try {
    // แสดงข้อมูล Assets ที่มี Hostname
    echo "<h3>📋 Assets ที่มี Hostname:</h3>";
    $stmt = $pdo->query("SELECT id, asset_id, hostname, ip_address, last_ip_check FROM assets WHERE hostname IS NOT NULL AND hostname != '' ORDER BY hostname");
    $assets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($assets)) {
        echo "<p style='color: orange;'>⚠️ ไม่มี Assets ที่มี Hostname</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Asset ID</th><th>Hostname</th><th>IP Address</th><th>Last Check</th><th>การจัดการ</th>";
        echo "</tr>";
        
        foreach ($assets as $asset) {
            $ipStatus = empty($asset['ip_address']) ? 
                "<span style='color: orange;'>ยังไม่มี IP</span>" : 
                "<span style='color: green;'>" . htmlspecialchars($asset['ip_address']) . "</span>";
                
            echo "<tr>";
            echo "<td>" . htmlspecialchars($asset['asset_id']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($asset['hostname']) . "</strong></td>";
            echo "<td>" . $ipStatus . "</td>";
            echo "<td>" . ($asset['last_ip_check'] ? date('d/m/Y H:i', strtotime($asset['last_ip_check'])) : '-') . "</td>";
            echo "<td>";
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='asset_id' value='" . $asset['id'] . "'>";
            echo "<button type='submit' name='update_asset_ip' style='background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;'>🔄 อัปเดต IP</button>";
            echo "</form>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // ทดสอบ Hostname ตัวอย่าง
    echo "<h3>🧪 ทดสอบ NSLOOKUP:</h3>";
    echo "<form method='POST' style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<div style='margin-bottom: 10px;'>";
    echo "<label for='hostname'><strong>Hostname ที่ต้องการทดสอบ:</strong></label><br>";
    echo "<input type='text' id='hostname' name='hostname' placeholder='เช่น google.com, facebook.com' style='width: 300px; padding: 8px; margin-top: 5px;' required>";
    echo "</div>";
    echo "<button type='submit' name='test_hostname' style='background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>🔍 ทดสอบ NSLOOKUP</button>";
    echo "</form>";
    
    // ตัวอย่าง Hostname สำหรับทดสอบ
    echo "<h4>💡 ตัวอย่าง Hostname สำหรับทดสอบ:</h4>";
    $testHostnames = ['google.com', 'facebook.com', 'github.com', 'stackoverflow.com', 'microsoft.com'];
    echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
    foreach ($testHostnames as $hostname) {
        echo "<form method='POST' style='display: inline;'>";
        echo "<input type='hidden' name='hostname' value='{$hostname}'>";
        echo "<button type='submit' name='test_hostname' style='background: #17a2b8; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; font-size: 0.9rem;'>{$hostname}</button>";
        echo "</form>";
    }
    echo "</div>";
    
    // แสดงข้อมูลระบบ
    echo "<h3>ℹ️ ข้อมูลระบบ:</h3>";
    echo "<ul>";
    echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>";
    echo "<li><strong>Operating System:</strong> " . PHP_OS . "</li>";
    echo "<li><strong>gethostbyname function:</strong> " . (function_exists('gethostbyname') ? '✅ Available' : '❌ Not Available') . "</li>";
    echo "<li><strong>dns_get_record function:</strong> " . (function_exists('dns_get_record') ? '✅ Available' : '❌ Not Available') . "</li>";
    echo "<li><strong>exec function:</strong> " . (function_exists('exec') ? '✅ Available' : '❌ Not Available') . "</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<hr>
<p>
    <a href="add_ip_address_field.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        🔧 เพิ่มฟิลด์ IP Address
    </a>
    <a href="ip_lookup_manager.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🔍 จัดการ IP Lookup
    </a>
    <a href="index.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th { background: #f8f9fa; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
button:hover { opacity: 0.8; }
</style>
