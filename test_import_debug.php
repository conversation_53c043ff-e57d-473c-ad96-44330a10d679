<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

echo "<h2>ทดสอบ Import Update Assets</h2>";

// ทดสอบการอ่านไฟล์ CSV
if (isset($_GET['test']) && $_GET['test'] === 'csv') {
    echo "<h3>ทดสอบการอ่านไฟล์ CSV</h3>";
    
    // สร้างไฟล์ CSV ทดสอบ
    $testCsvContent = "asset_id,type,brand,model,status\n";
    $testCsvContent .= "IT-001,Desktop,Dell,OptiPlex 7090,ใช้งาน\n";
    $testCsvContent .= "HR-002,Laptop,HP,EliteBook 840,ชำรุด\n";
    
    $testFile = 'test_import.csv';
    file_put_contents($testFile, $testCsvContent);
    
    echo "<p><strong>ไฟล์ทดสอบ:</strong> {$testFile}</p>";
    echo "<pre>" . htmlspecialchars($testCsvContent) . "</pre>";
    
    // ทดสอบการอ่าน
    include_once 'import_update_assets.php';
    
    try {
        $data = parseCSVFile($testFile);
        echo "<h4>ผลการอ่าน:</h4>";
        echo "<pre>" . print_r($data, true) . "</pre>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
    
    // ลบไฟล์ทดสอบ
    unlink($testFile);
}

// ตรวจสอบ Assets ที่มีอยู่
echo "<h3>Assets ที่มีอยู่ในระบบ:</h3>";
try {
    $stmt = $pdo->query("SELECT asset_id, type, brand, model, status FROM assets LIMIT 10");
    $assets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($assets)) {
        echo "<p>ไม่มี Assets ในระบบ</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Asset ID</th><th>Type</th><th>Brand</th><th>Model</th><th>Status</th></tr>";
        foreach ($assets as $asset) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($asset['asset_id']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['type']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['brand']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['model']) . "</td>";
            echo "<td>" . htmlspecialchars($asset['status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// ตรวจสอบ AssetManager
echo "<h3>ทดสอบ AssetManager:</h3>";
try {
    require_once 'classes/AssetManager.php';
    $assetManager = new AssetManager($pdo);
    echo "<p style='color: green;'>✅ AssetManager โหลดสำเร็จ</p>";
    
    // ทดสอบ getCurrentUserFullName
    $currentUser = getCurrentUserFullName();
    echo "<p><strong>ผู้ใช้ปัจจุบัน:</strong> " . htmlspecialchars($currentUser) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ AssetManager Error: " . $e->getMessage() . "</p>";
}

// ตรวจสอบ Error Log
echo "<h3>Error Logs ล่าสุด:</h3>";
$errorLogFile = ini_get('error_log');
if ($errorLogFile && file_exists($errorLogFile)) {
    $logs = file($errorLogFile);
    $recentLogs = array_slice($logs, -10); // 10 บรรทัดล่าสุด
    echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: auto;'>";
    foreach ($recentLogs as $log) {
        if (strpos($log, 'Import') !== false || strpos($log, 'CSV') !== false) {
            echo htmlspecialchars($log);
        }
    }
    echo "</pre>";
} else {
    echo "<p>ไม่พบ Error Log หรือไม่สามารถเข้าถึงได้</p>";
}
?>

<hr>
<p>
    <a href="?test=csv" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        🧪 ทดสอบอ่าน CSV
    </a>
    <a href="import_update_assets.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        📁 Import Update Assets
    </a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
        🏠 กลับหน้าหลัก
    </a>
</p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
table { border-collapse: collapse; margin: 10px 0; }
th { background: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
