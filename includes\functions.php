<?php
// Functions for Asset Management System

// Set timezone to Thailand
date_default_timezone_set('Asia/Bangkok');

// Helper functions
function getThailandDateTime() {
    $timezone = new DateTimeZone('Asia/Bangkok');
    $datetime = new DateTime('now', $timezone);
    return $datetime->format('Y-m-d H:i:s');
}

function formatDate($date) {
    if (empty($date)) return '-';
    $timezone = new DateTimeZone('Asia/Bangkok');
    $datetime = new DateTime($date);
    $datetime->setTimezone($timezone);
    return $datetime->format('d/m/Y');
}

function formatDateTime($datetime) {
    if (empty($datetime)) return '-';
    $timezone = new DateTimeZone('Asia/Bangkok');
    $dt = new DateTime($datetime);
    $dt->setTimezone($timezone);
    return $dt->format('d/m/Y H:i:s');
}

function getStatusBadge($status) {
    $badges = [
        'ใช้งาน' => 'success',
        'ชำรุด' => 'danger',
        'สำรอง' => 'warning'
    ];
    $class = $badges[$status] ?? 'secondary';
    return "<span class='badge badge-{$class}'>{$status}</span>";
}

/**
 * แปลงขนาดไฟล์เป็นรูปแบบที่อ่านง่าย
 */
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 B';

    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= pow(1024, $pow);

    return round($bytes, 2) . ' ' . $units[$pow];
}
?>
